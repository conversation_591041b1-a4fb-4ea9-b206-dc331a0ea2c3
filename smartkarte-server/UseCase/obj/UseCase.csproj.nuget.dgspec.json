{"format": 1, "restore": {"/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/UseCase/UseCase.csproj": {}}, "projects": {"/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/CalculateService/CalculateService.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/CalculateService/CalculateService.csproj", "projectName": "CalculateService", "projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/CalculateService/CalculateService.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/CalculateService/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Entity/Entity.csproj": {"projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Entity/Entity.csproj"}, "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Helper/Helper.csproj": {"projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Helper/Helper.csproj"}, "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Infrastructure/Infrastructure.csproj": {"projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Infrastructure/Infrastructure.csproj"}, "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/PostgreDataContext/PostgreDataContext.csproj": {"projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/PostgreDataContext/PostgreDataContext.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.404/RuntimeIdentifierGraph.json"}}}, "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/CommonChecker/CommonChecker.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/CommonChecker/CommonChecker.csproj", "projectName": "Common<PERSON><PERSON><PERSON>", "projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/CommonChecker/CommonChecker.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/CommonChecker/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Helper/Helper.csproj": {"projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Helper/Helper.csproj"}, "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Infrastructure/Infrastructure.csproj": {"projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Infrastructure/Infrastructure.csproj"}, "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/PostgreDataContext/PostgreDataContext.csproj": {"projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/PostgreDataContext/PostgreDataContext.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.404/RuntimeIdentifierGraph.json"}}}, "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Domain/Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Domain/Domain.csproj", "projectName": "Domain", "projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Domain/Domain.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Domain/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Entity/Entity.csproj": {"projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Entity/Entity.csproj"}, "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Helper/Helper.csproj": {"projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Helper/Helper.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"AWSSDK.S3": {"target": "Package", "version": "[3.7.9.42, )"}, "Microsoft.AspNetCore.Http.Features": {"target": "Package", "version": "[2.2.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.404/RuntimeIdentifierGraph.json"}}}, "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Entity/Entity.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Entity/Entity.csproj", "projectName": "Entity", "projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Entity/Entity.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Entity/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Helper/Helper.csproj": {"projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Helper/Helper.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": {"target": "Package", "version": "[6.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.404/RuntimeIdentifierGraph.json"}}}, "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/ErrorCodeGenerator/ErrorCodeGenerator.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/ErrorCodeGenerator/ErrorCodeGenerator.csproj", "projectName": "ErrorCodeGenerator", "projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/ErrorCodeGenerator/ErrorCodeGenerator.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/ErrorCodeGenerator/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[13.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.404/RuntimeIdentifierGraph.json"}}}, "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Helper/Helper.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Helper/Helper.csproj", "projectName": "Helper", "projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Helper/Helper.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Helper/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/ErrorCodeGenerator/ErrorCodeGenerator.csproj": {"projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/ErrorCodeGenerator/ErrorCodeGenerator.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Kana.NET": {"target": "Package", "version": "[1.0.6, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[7.0.0, )"}, "MyNihongo.KanaConverter": {"target": "Package", "version": "[1.0.3, )"}, "StackExchange.Redis": {"target": "Package", "version": "[2.6.111, )"}, "System.Text.Encoding.CodePages": {"target": "Package", "version": "[6.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.404/RuntimeIdentifierGraph.json"}}}, "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Infrastructure/Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Infrastructure/Infrastructure.csproj", "projectName": "Infrastructure", "projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Infrastructure/Infrastructure.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Infrastructure/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Domain/Domain.csproj": {"projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Domain/Domain.csproj"}, "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/PostgreDataContext/PostgreDataContext.csproj": {"projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/PostgreDataContext/PostgreDataContext.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"AWSSDK.S3": {"target": "Package", "version": "[3.7.9.42, )"}, "GraphQL.Client": {"target": "Package", "version": "[6.0.0, )"}, "GraphQL.Client.Serializer.Newtonsoft": {"target": "Package", "version": "[6.0.0, )"}, "Konscious.Security.Cryptography.Argon2": {"target": "Package", "version": "[1.3.0, )"}, "Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.AspNetCore.Mvc.Abstractions": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.AspNetCore.Mvc.Core": {"target": "Package", "version": "[2.2.5, )"}, "Microsoft.EntityFrameworkCore.DynamicLinq": {"target": "Package", "version": "[6.2.20, )"}, "Sentry": {"target": "Package", "version": "[4.13.0, )"}, "Sentry.Profiling": {"target": "Package", "version": "[4.13.0, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[6.34.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.404/RuntimeIdentifierGraph.json"}}}, "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/PostgreDataContext/PostgreDataContext.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/PostgreDataContext/PostgreDataContext.csproj", "projectName": "PostgreDataContext", "projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/PostgreDataContext/PostgreDataContext.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/PostgreDataContext/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Entity/Entity.csproj": {"projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Entity/Entity.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[7.0.1, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[7.0.1, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[7.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.404/RuntimeIdentifierGraph.json"}}}, "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Reporting/Reporting.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Reporting/Reporting.csproj", "projectName": "Reporting", "projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Reporting/Reporting.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Reporting/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/CalculateService/CalculateService.csproj": {"projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/CalculateService/CalculateService.csproj"}, "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Domain/Domain.csproj": {"projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Domain/Domain.csproj"}, "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Entity/Entity.csproj": {"projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Entity/Entity.csproj"}, "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Infrastructure/Infrastructure.csproj": {"projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Infrastructure/Infrastructure.csproj"}, "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/PostgreDataContext/PostgreDataContext.csproj": {"projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/PostgreDataContext/PostgreDataContext.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Castle.Windsor": {"target": "Package", "version": "[6.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.2, )"}, "Spire.PDF": {"target": "Package", "version": "[11.3.0, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.8.6, )"}, "System.Text.Encoding.CodePages": {"target": "Package", "version": "[7.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.404/RuntimeIdentifierGraph.json"}}}, "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/UseCase/UseCase.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/UseCase/UseCase.csproj", "projectName": "UseCase", "projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/UseCase/UseCase.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/UseCase/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/CommonChecker/CommonChecker.csproj": {"projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/CommonChecker/CommonChecker.csproj"}, "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Domain/Domain.csproj": {"projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Domain/Domain.csproj"}, "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Reporting/Reporting.csproj": {"projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Reporting/Reporting.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.404/RuntimeIdentifierGraph.json"}}}}}