﻿using Amazon.S3.Model.Internal.MarshallTransformations;
using CalculateService.Interface;
using Entity.Tenant;
using Infrastructure.Base;
using Infrastructure.CommonDB;
using Infrastructure.Interfaces;
using PostgreDataContext;

namespace CalculateService.Implementation
{
    public class SystemConfigProvider : ISystemConfigProvider
    {
        private readonly List<SystemConf> _systemConfigs;
        public SystemConfigProvider(ITenantProvider tenantProvider)
        {
            _systemConfigs = tenantProvider.GetNoTrackingDataContext().SystemConfs.ToList();
            tenantProvider.DisposeDataContext();
        }

        public int GetChokiDateRange(int hpId)
        {
            return (int)GetSettingValue(hpId, 3006, 3, 0);
        }

        public int GetChokiFutan(int hpId)
        {
            return (int)GetSettingValue(hpId, 3006, 2, 0);
        }

        public int GetJibaiJunkyo(int hpId)
        {
            return (int)GetSettingValue(hpId, 3001, 0);
        }

        public double GetJibaiRousaiRate(int hpId)
        {
            return GetSettingValue(hpId, 3001, 1);
        }

        public int GetRoundKogakuPtFutan(int hpId)
        {
            return (int)GetSettingValue(hpId, 3016, 0);
        }
        public int GetReceiptTantoIdTarget(int hpId)
        {
            return (int)GetSettingValue(hpId, 6002, 1);
        }
        public int GetReceiptKaIdTarget(int hpId)
        {
            return (int)GetSettingValue(6002, 0);
        }
        public int GetHokensyuHandling(int hpId)
        {
            return (int)GetSettingValue(hpId, 3013);
        }
        public int GetCalcCheckKensaDuplicateLog(int hpId)
        {
            return (int)GetSettingValue(hpId, 3019, 0);
        }
        public int GetHoukatuHaihanCheckMode(int hpId)
        {
            return (int)GetSettingValue(hpId, 3008, 0, 0);
        }
        public int GetHoukatuHaihanLogputMode(int hpId)
        {
            return (int)GetSettingValue(hpId, 3009, 0, 0);
        }
        public int GetHoukatuHaihanSPJyokenLogputMode(int hpId)
        {
            return (int)GetSettingValue(hpId, 3009, 1, 0);
        }
        public int GetHoumonKangoSaisinHokatu(int hpId)
        {
            return (int)GetSettingValue(hpId, 3023, 0, 0);
        }
        public int GetKensaMarumeBuntenSyaho(int hpId)
        {
            return (int)GetSettingValue(hpId, 3017, 0);
        }
        public int GetKensaMarumeBuntenKokuho(int hpId) 
        {
            return (int) GetSettingValue(hpId, 3017, 1);
        }
        public int GetReceNoDspComment(int hpId)
        {
            return (int)GetSettingValue(hpId, 3012, 0, 0); 
        }
        public int GetOutDrugYohoDsp(int hpId)
        {
            return (int)GetSettingValue(hpId, 3005, 0, 1); 
        }
        public int GetSyohoRinjiDays(int hpId)
        {
            return (int)GetSettingValue(hpId, 3002, 0, 14);
        }
        public int GetRousaiRecedenLicense(int hpId)
        {
            return (int)GetSettingValue(hpId, 100003, 0);
        }
        public int GetAfterCareRecedenLicense(int hpId)
        {
            return (int)GetSettingValue(hpId, 100003, 1);
        }
        public string GetRousaiRecedenStartYm(int hpId)
        {
            return GetSettingParam(hpId, 100003, 0);
        }
        public string GetAfterCareRecedenStartYm(int hpId)
        {
            return GetSettingParam(hpId, 100003, 1);
        }
        public int GetDrugPid(int hpId)
        {
            return (int)GetSettingValue(hpId, 3007, 0, 0);
        }
        public int GetSyouniCounselingCheck(int hpId)
        {
            return (int)GetSettingValue(hpId, 3024, 0, 0);
        }
        public int GetChyokiSenteiMarume(int hpId)
        {
            return (int)GetSettingValue(hpId, 3025, 0, 0);
        }
        public int GetInDrugYohoComment(int hpId)
        {
            return (int)GetSettingValue(hpId, 3022, 0, 0);
        }
        public int GetCalcAutoComment(int hpId)
        {
            return (int)GetSettingValue(hpId, 3018, 0);
        }
        public string GetNaraFukusiReceCmtStartDate(int hpId)
        {
            return (string)GetSettingParam(hpId, 3011, 0, "");
        }
        public int GetNaraFukusiReceCmt(int hpId)
        {
            return (int)GetSettingValue(hpId, 3011, 0, 0);
        }
        public int GetReceiptOutDrgSinId(int hpId)
        {
            return (int)GetSettingValue(hpId, 94006, 0);
        }
        public int GetReceiptCommentTenCount(int hpId)
        {
            return (int)GetSettingValue(hpId, 94007, 0);
        }
        public int GetSameRpMerge(int hpId)
        {
            return (int)GetSettingValue(hpId, 3014);
        }

        public int GetChokiTokki(int hpId)
        {
            return (int)GetSettingValue(hpId, 3006, 1, 0);
        }

        public int GetReceKyufuKisai(int hpId)
        {
            return (int)GetSettingValue(hpId, 3010, 0, 0);
        }

        public int GetReceKyufuKisai2(int hpId)
        {
            return (int)GetSettingValue(hpId, 3010, 1, 0);
        }

        private double GetSettingValue(int hp_id, int groupCd, int grpEdaNo = 0, int defaultValue = 0)
        {
            SystemConf? systemConf = _systemConfigs.FirstOrDefault(p => p.HpId == hp_id && p.GrpCd == groupCd && p.GrpEdaNo == grpEdaNo);
            return systemConf != null ? systemConf.Val : defaultValue;
        }
        private string GetSettingParam(int hp_id, int groupCd, int grpEdaNo = 0, string defaultParam = "")
        {
            SystemConf systemConf = _systemConfigs.FirstOrDefault(p => p.HpId == hp_id && p.GrpCd == groupCd && p.GrpEdaNo == grpEdaNo) ?? new();
            return systemConf != null ? systemConf.Param ?? string.Empty : defaultParam;
        }
    }
}
