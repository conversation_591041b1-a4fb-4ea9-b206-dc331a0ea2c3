﻿namespace CalculateService.Interface
{
    public interface ISystemConfigProvider
    {
        int GetJibaiJunkyo(int hpId);

        int GetChokiFutan(int hpId);

        int GetChokiDateRange(int hpId);

        int GetRoundKogakuPtFutan(int hpId);

        double GetJibaiRousaiRate(int hpId);

        int GetChokiTokki(int hpId);

        int GetReceKyufuKisai(int hpId);

        int GetReceKyufuKisai2(int hpId);
        int GetReceiptTantoIdTarget(int hpId);
        int GetReceiptKaIdTarget(int hpId);

        int GetHokensyuHandling(int hpId);
        int GetCalcCheckKensaDuplicateLog(int hpId);
        int GetHoukatuHaihanCheckMode(int hpId);
        int GetHoukatuHaihanLogputMode(int hpId);
        int GetHoukatuHaihanSPJyokenLogputMode(int hpId);
        int GetHoumonKangoSaisinHokatu(int hpId);
        int GetKensaMarumeBuntenKokuho(int hpId);
        int GetKensaMarumeBuntenSyaho(int hpId);
        int GetReceNoDspComment(int hpId);
        int GetOutDrugYohoDsp(int hpId);
        int GetSyohoRinjiDays(int hpId);
        int GetRousaiRecedenLicense(int hpId);
        string GetRousaiRecedenStartYm(int hpId);
        int GetAfterCareRecedenLicense(int hpId);
        string GetAfterCareRecedenStartYm(int hpId);
        int GetDrugPid(int hpId);
        int GetSyouniCounselingCheck(int hpId);
        int GetInDrugYohoComment(int hpId);
        int GetCalcAutoComment(int hpId);
        string GetNaraFukusiReceCmtStartDate(int hpId);
        int GetNaraFukusiReceCmt(int hpId);
        int GetReceiptCommentTenCount(int hpId);
        int GetReceiptOutDrgSinId(int hpId);
        int GetSameRpMerge(int hpId);
        int GetChyokiSenteiMarume(int hpId);
    }
}
