﻿using Entity.Tenant;
using Helper.Common;

namespace CalculateService.Receipt.Models;

public class EFPtByomeiModel
{
    private PtByomei PtByomei { get; } = null;

    public EFPtByomeiModel(PtByomei ptByomei)
    {
        PtByomei = ptByomei;
    }

    /// <summary>
    /// 患者病名
    /// </summary>
    /// <summary>
    /// 医療機関識別ID
    /// </summary>
    public int HpId
    {
        get { return PtByomei.HpId; }
        set
        {
            if (PtByomei.HpId == value) return;
            PtByomei.HpId = value;
            // RaisePropertyChanged(() => HpId);
        }
    }

    /// <summary>
    /// 患者ID
    ///     患者を識別するためのシステム固有の番号
    /// </summary>
    public long PtId
    {
        get { return PtByomei.PtId; }
        set
        {
            if (PtByomei.PtId == value) return;
            PtByomei.PtId = value;
            // RaisePropertyChanged(() => PtId);
        }
    }

    /// <summary>
    /// 基本病名コード
    ///     コードを使用しない場合、「0000999」をセット
    /// </summary>
    public string ByomeiCd
    {
        get { return PtByomei.ByomeiCd; }
        set
        {
            if (PtByomei.ByomeiCd == value) return;
            PtByomei.ByomeiCd = value;
            // RaisePropertyChanged(() => ByomeiCd);
        }
    }

    /// <summary>
    /// 連番
    ///     患者の病名を識別するためのシステム固有の番号
    /// </summary>
    public long Id
    {
        get { return PtByomei.Id; }
        set
        {
            if (PtByomei.Id == value) return;
            PtByomei.Id = value;
            // RaisePropertyChanged(() => Id);
        }
    }

    /// <summary>
    /// 並び順
    /// </summary>
    public int SortNo
    {
        get { return PtByomei.SortNo; }
        set
        {
            if (PtByomei.SortNo == value) return;
            PtByomei.SortNo = value;
            // RaisePropertyChanged(() => SortNo);
        }
    }

    /// <summary>
    /// 修飾語コード１
    /// </summary>
    public string SyusyokuCd1
    {
        get { return PtByomei.SyusyokuCd1; }
        set
        {
            if (PtByomei.SyusyokuCd1 == value) return;
            PtByomei.SyusyokuCd1 = value;
            // RaisePropertyChanged(() => SyusyokuCd1);
        }
    }

    /// <summary>
    /// 修飾語コード２
    /// </summary>
    public string SyusyokuCd2
    {
        get { return PtByomei.SyusyokuCd2; }
        set
        {
            if (PtByomei.SyusyokuCd2 == value) return;
            PtByomei.SyusyokuCd2 = value;
            // RaisePropertyChanged(() => SyusyokuCd2);
        }
    }

    /// <summary>
    /// 修飾語コード３
    /// </summary>
    public string SyusyokuCd3
    {
        get { return PtByomei.SyusyokuCd3; }
        set
        {
            if (PtByomei.SyusyokuCd3 == value) return;
            PtByomei.SyusyokuCd3 = value;
            // RaisePropertyChanged(() => SyusyokuCd3);
        }
    }

    /// <summary>
    /// 修飾語コード４
    /// </summary>
    public string SyusyokuCd4
    {
        get { return PtByomei.SyusyokuCd4; }
        set
        {
            if (PtByomei.SyusyokuCd4 == value) return;
            PtByomei.SyusyokuCd4 = value;
            // RaisePropertyChanged(() => SyusyokuCd4);
        }
    }

    /// <summary>
    /// 修飾語コード５
    /// </summary>
    public string SyusyokuCd5
    {
        get { return PtByomei.SyusyokuCd5; }
        set
        {
            if (PtByomei.SyusyokuCd5 == value) return;
            PtByomei.SyusyokuCd5 = value;
            // RaisePropertyChanged(() => SyusyokuCd5);
        }
    }

    /// <summary>
    /// 修飾語コード６
    /// </summary>
    public string SyusyokuCd6
    {
        get { return PtByomei.SyusyokuCd6; }
        set
        {
            if (PtByomei.SyusyokuCd6 == value) return;
            PtByomei.SyusyokuCd6 = value;
            // RaisePropertyChanged(() => SyusyokuCd6);
        }
    }

    /// <summary>
    /// 修飾語コード７
    /// </summary>
    public string SyusyokuCd7
    {
        get { return PtByomei.SyusyokuCd7; }
        set
        {
            if (PtByomei.SyusyokuCd7 == value) return;
            PtByomei.SyusyokuCd7 = value;
            // RaisePropertyChanged(() => SyusyokuCd7);
        }
    }

    /// <summary>
    /// 修飾語コード８
    /// </summary>
    public string SyusyokuCd8
    {
        get { return PtByomei.SyusyokuCd8; }
        set
        {
            if (PtByomei.SyusyokuCd8 == value) return;
            PtByomei.SyusyokuCd8 = value;
            // RaisePropertyChanged(() => SyusyokuCd8);
        }
    }

    /// <summary>
    /// 修飾語コード９
    /// </summary>
    public string SyusyokuCd9
    {
        get { return PtByomei.SyusyokuCd9; }
        set
        {
            if (PtByomei.SyusyokuCd9 == value) return;
            PtByomei.SyusyokuCd9 = value;
            // RaisePropertyChanged(() => SyusyokuCd9);
        }
    }

    /// <summary>
    /// 修飾語コード１０
    /// </summary>
    public string SyusyokuCd10
    {
        get { return PtByomei.SyusyokuCd10; }
        set
        {
            if (PtByomei.SyusyokuCd10 == value) return;
            PtByomei.SyusyokuCd10 = value;
            // RaisePropertyChanged(() => SyusyokuCd10);
        }
    }

    /// <summary>
    /// 修飾語コード１１
    /// </summary>
    public string SyusyokuCd11
    {
        get { return PtByomei.SyusyokuCd11; }
        set
        {
            if (PtByomei.SyusyokuCd11 == value) return;
            PtByomei.SyusyokuCd11 = value;
            // RaisePropertyChanged(() => SyusyokuCd11);
        }
    }

    /// <summary>
    /// 修飾語コード１２
    /// </summary>
    public string SyusyokuCd12
    {
        get { return PtByomei.SyusyokuCd12; }
        set
        {
            if (PtByomei.SyusyokuCd12 == value) return;
            PtByomei.SyusyokuCd12 = value;
            // RaisePropertyChanged(() => SyusyokuCd12);
        }
    }

    /// <summary>
    /// 修飾語コード１３
    /// </summary>
    public string SyusyokuCd13
    {
        get { return PtByomei.SyusyokuCd13; }
        set
        {
            if (PtByomei.SyusyokuCd13 == value) return;
            PtByomei.SyusyokuCd13 = value;
            // RaisePropertyChanged(() => SyusyokuCd13);
        }
    }

    /// <summary>
    /// 修飾語コード１４
    /// </summary>
    public string SyusyokuCd14
    {
        get { return PtByomei.SyusyokuCd14; }
        set
        {
            if (PtByomei.SyusyokuCd14 == value) return;
            PtByomei.SyusyokuCd14 = value;
            // RaisePropertyChanged(() => SyusyokuCd14);
        }
    }

    /// <summary>
    /// 修飾語コード１５
    /// </summary>
    public string SyusyokuCd15
    {
        get { return PtByomei.SyusyokuCd15; }
        set
        {
            if (PtByomei.SyusyokuCd15 == value) return;
            PtByomei.SyusyokuCd15 = value;
            // RaisePropertyChanged(() => SyusyokuCd15);
        }
    }

    /// <summary>
    /// 修飾語コード１６
    /// </summary>
    public string SyusyokuCd16
    {
        get { return PtByomei.SyusyokuCd16; }
        set
        {
            if (PtByomei.SyusyokuCd16 == value) return;
            PtByomei.SyusyokuCd16 = value;
            // RaisePropertyChanged(() => SyusyokuCd16);
        }
    }

    /// <summary>
    /// 修飾語コード１７
    /// </summary>
    public string SyusyokuCd17
    {
        get { return PtByomei.SyusyokuCd17; }
        set
        {
            if (PtByomei.SyusyokuCd17 == value) return;
            PtByomei.SyusyokuCd17 = value;
            // RaisePropertyChanged(() => SyusyokuCd17);
        }
    }

    /// <summary>
    /// 修飾語コード１８
    /// </summary>
    public string SyusyokuCd18
    {
        get { return PtByomei.SyusyokuCd18; }
        set
        {
            if (PtByomei.SyusyokuCd18 == value) return;
            PtByomei.SyusyokuCd18 = value;
            // RaisePropertyChanged(() => SyusyokuCd18);
        }
    }

    /// <summary>
    /// 修飾語コード１９
    /// </summary>
    public string SyusyokuCd19
    {
        get { return PtByomei.SyusyokuCd19; }
        set
        {
            if (PtByomei.SyusyokuCd19 == value) return;
            PtByomei.SyusyokuCd19 = value;
            // RaisePropertyChanged(() => SyusyokuCd19);
        }
    }

    /// <summary>
    /// 修飾語コード２０
    /// </summary>
    public string SyusyokuCd20
    {
        get { return PtByomei.SyusyokuCd20; }
        set
        {
            if (PtByomei.SyusyokuCd20 == value) return;
            PtByomei.SyusyokuCd20 = value;
            // RaisePropertyChanged(() => SyusyokuCd20);
        }
    }

    /// <summary>
    /// 修飾語コード２１
    /// </summary>
    public string SyusyokuCd21
    {
        get { return PtByomei.SyusyokuCd21; }
        set
        {
            if (PtByomei.SyusyokuCd21 == value) return;
            PtByomei.SyusyokuCd21 = value;
            // RaisePropertyChanged(() => SyusyokuCd21);
        }
    }

    /// <summary>
    /// 病名
    /// </summary>
    public string Byomei
    {
        get { return PtByomei.Byomei; }
        set
        {
            if (PtByomei.Byomei == value) return;
            PtByomei.Byomei = value;
            // RaisePropertyChanged(() => Byomei);
        }
    }

    /// <summary>
    /// 開始日
    /// </summary>
    public int StartDate
    {
        get { return PtByomei.StartDate; }
        set
        {
            if (PtByomei.StartDate == value) return;
            PtByomei.StartDate = value;
            // RaisePropertyChanged(() => StartDate);
        }
    }

    /// <summary>
    /// 転帰区分
    /// 転帰区分を表す。
    ///      0: 下記以外
    ///      1: 治ゆ
    ///      2: 中止
    ///      3: 死亡
    ///      9: その他
    /// </summary>
    public int TenkiKbn
    {
        get { return PtByomei.TenkiKbn; }
        set
        {
            if (PtByomei.TenkiKbn == value) return;
            PtByomei.TenkiKbn = value;
            // RaisePropertyChanged(() => TenkiKbn);
        }
    }

    /// <summary>
    /// 転帰日
    /// </summary>
    public int TenkiDate
    {
        get { return PtByomei.TenkiDate; }
        set
        {
            if (PtByomei.TenkiDate == value) return;
            PtByomei.TenkiDate = value;
            // RaisePropertyChanged(() => TenkiDate);
        }
    }

    /// <summary>
    /// 主病名区分
    ///     0: 主病名以外
    ///     1: 主病名
    /// </summary>
    public int SyubyoKbn
    {
        get { return PtByomei.SyubyoKbn; }
        set
        {
            if (PtByomei.SyubyoKbn == value) return;
            PtByomei.SyubyoKbn = value;
            // RaisePropertyChanged(() => SyobyoKbn);
        }
    }

    /// <summary>
    /// 慢性疾患区分
    ///     特定疾患療養指導料等の算定対象であるか否かを表す
    ///     00: 対象外
    ///     03: 皮膚科特定疾患指導管理料（１）算定対象
    ///     04: 皮膚科特定疾患指導管理料（２）算定対象
    ///     05: 特定疾患療養指導料／老人慢性疾患生活指導料算定対象
    ///     07: てんかん指導料算定対象 
    ///     08: 特定疾患療養管理料又はてんかん指導料算
    ///     定対象 
    /// </summary>
    public int SikkanKbn
    {
        get { return PtByomei.SikkanKbn; }
        set
        {
            if (PtByomei.SikkanKbn == value) return;
            PtByomei.SikkanKbn = value;
            // RaisePropertyChanged(() => SikkanKbn);
        }
    }

    /// <summary>
    /// 難病外来コード
    ///     当該傷病名が難病外来指導管理料の算定対象であるか否かを表す。
    ///     00: 算定対象外
    ///     09: 難病外来指導管理料算定対象
    /// </summary>
    public int NanByoCd
    {
        get { return PtByomei.NanByoCd; }
        set
        {
            if (PtByomei.NanByoCd == value) return;
            PtByomei.NanByoCd = value;
            // RaisePropertyChanged(() => NanByoCd);
        }
    }

    /// <summary>
    /// 補足コメント
    /// </summary>
    public string HosokuCmt
    {
        get { return PtByomei.HosokuCmt; }
        set
        {
            if (PtByomei.HosokuCmt == value) return;
            PtByomei.HosokuCmt = value;
            // RaisePropertyChanged(() => HosokuCmt);
        }
    }

    /// <summary>
    /// 保険組み合わせ番号
    ///     0: 共通病名
    /// </summary>
    public int HokenPid
    {
        get { return PtByomei.HokenPid; }
        set
        {
            if (PtByomei.HokenPid == value) return;
            PtByomei.HokenPid = value;
            // RaisePropertyChanged(() => HokenPid);
        }
    }

    /// <summary>
    /// 当月病名区分
    ///     1: 当月病名
    /// </summary>
    public int TogetuByomei
    {
        get { return PtByomei.TogetuByomei; }
        set
        {
            if (PtByomei.TogetuByomei == value) return;
            PtByomei.TogetuByomei = value;
            // RaisePropertyChanged(() => TogetuByomei);
        }
    }

    /// <summary>
    /// レセプト非表示区分
    ///     1: 非表示
    /// </summary>
    public int IsNodspRece
    {
        get { return PtByomei.IsNodspRece; }
        set
        {
            if (PtByomei.IsNodspRece == value) return;
            PtByomei.IsNodspRece = value;
            // RaisePropertyChanged(() => IsNodspRece);
        }
    }

    /// <summary>
    /// カルテ非表示区分
    ///     1: 非表示
    /// </summary>
    public int IsNodspKarte
    {
        get { return PtByomei.IsNodspKarte; }
        set
        {
            if (PtByomei.IsNodspKarte == value) return;
            PtByomei.IsNodspKarte = value;
            // RaisePropertyChanged(() => IsNodspKarte);
        }
    }

    /// <summary>
    /// 削除区分
    ///     1:削除
    /// </summary>
    public int IsDeleted
    {
        get { return PtByomei.IsDeleted; }
        set
        {
            if (PtByomei.IsDeleted == value) return;
            PtByomei.IsDeleted = value;
            // RaisePropertyChanged(() => IsDeleted);
        }
    }

    /// <summary>
    /// 作成日時 
    /// </summary>
    public DateTime CreateDate
    {
        get { return PtByomei.CreateDate; }
        set
        {
            if (PtByomei.CreateDate == value) return;
            PtByomei.CreateDate = value;
            // RaisePropertyChanged(() => CreateDate);
        }
    }

    /// <summary>
    /// 作成者
    /// </summary>
    public int CreateId
    {
        get { return PtByomei.CreateId; }
        set
        {
            if (PtByomei.CreateId == value) return;
            PtByomei.CreateId = value;
            // RaisePropertyChanged(() => CreateId);
        }
    }

    /// <summary>
    /// 作成端末 
    /// </summary>
    public string CreateMachine
    {
        get { return PtByomei.CreateMachine; }
        set
        {
            if (PtByomei.CreateMachine == value) return;
            PtByomei.CreateMachine = value;
            // RaisePropertyChanged(() => CreateMachine);
        }
    }

    /// <summary>
    /// 更新日時 
    /// </summary>
    public DateTime UpdateDate
    {
        get { return PtByomei.UpdateDate; }
        set
        {
            if (PtByomei.UpdateDate == value) return;
            PtByomei.UpdateDate = value;
            // RaisePropertyChanged(() => UpdateDate);
        }
    }

    /// <summary>
    /// 更新者
    /// </summary>
    public int UpdateId
    {
        get { return PtByomei.UpdateId; }
        set
        {
            if (PtByomei.UpdateId == value) return;
            PtByomei.UpdateId = value;
            // RaisePropertyChanged(() => UpdateId);
        }
    }

    /// <summary>
    /// 更新端末 
    /// </summary>
    public string UpdateMachine
    {
        get { return PtByomei.UpdateMachine; }
        set
        {
            if (PtByomei.UpdateMachine == value) return;
            PtByomei.UpdateMachine = value;
            // RaisePropertyChanged(() => UpdateMachine);
        }
    }

    public long SeqNo
    {
        get { return PtByomei.SeqNo; }
        set
        {
            if (PtByomei.SeqNo == value) return;
            PtByomei.SeqNo = value;
            // RaisePropertyChanged(() => SeqNo);
        }
    }

    /// <summary>
    /// 重要
    ///     1:重要
    /// </summary>
    public int IsImportant
    {
        get { return PtByomei.IsImportant; }
        set
        {
            if (PtByomei.IsImportant == value) return;
            PtByomei.IsImportant = value;
            // RaisePropertyChanged(() => IsImportant);
        }
    }
}
