﻿using Entity.Tenant;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CalculateService.Ika.Models
{
    public class KaMstModel
    {
        public KaMst KaMst { get; } = null;

        public KaMstModel(KaMst kaMst)
        {
            KaMst = kaMst;
        }

        /// <summary>
        /// 医療機関識別ID
        /// 
        /// </summary>
        public int HpId
        {
            get { return KaMst.HpId; }
        }

        /// <summary>
        /// 診療科ID
        /// </summary>
        public long KaId
        {
            get { return KaMst.KaId; }
        }

        /// <summary>
        /// 並び順
        /// </summary>
        public long SortNo
        {
            get { return KaMst.SortNo; }
        }

        /// <summary>
        /// レセ診療科コード
        /// 
        /// </summary>
        public string ReceKaCd
        {
            get { return KaMst.ReceKaCd; }
        }

        /// <summary>
        /// 様式診療科コード
        /// 
        /// </summary>
        public string YousikiKaCd
        {
            get { return KaMst.YousikiKaCd; }
        }

        /// <summary>
        /// 診療科略称
        /// 
        /// </summary>
        public string KaSname
        {
            get { return KaMst.KaSname; }
        }

        /// <summary>
        /// 診療科名称
        /// </summary>
        public string KaName
        {
            get { return KaMst.KaName; }
        }

        /// <summary>
        /// 削除区分
        /// </summary>
        public int IsDeleted
        {
            get { return KaMst.IsDeleted; }
        }
    }
}
