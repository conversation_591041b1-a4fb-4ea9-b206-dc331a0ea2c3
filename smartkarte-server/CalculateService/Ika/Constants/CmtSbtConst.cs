﻿namespace CalculateService.Ika.Constants
{
    class CmtSbtConst
    {
        ///<summary>
        /// 1:初回日
        ///</summary>
        public static int Syokai = 1;

        ///<summary>
        ///2:前回日
        ///</summary>
        public static int Zenkai = 2;
        ///<summary>
        ///3:実施日
        ///</summary>
        public static int JissiBi = 3;
        ///<summary>
        ///4:手術日
        ///</summary>
        public static int Syujyutu = 4;
        ///<summary>
        ///5:発症日
        ///</summary>
        public static int Hassyo = 5;
        ///<summary>
        ///6:治療開始日
        ///</summary>
        public static int ChiryoKaisi = 6;
        ///<summary>
        ///7:発症日または治療開始日
        ///</summary>
        public static int HassyoOrChiryo = 7;
        ///<summary>
        ///8:急性憎悪
        ///</summary>
        public static int KyuseiZouaku = 8;
        ///<summary>
        ///9:初回診断
        ///</summary>
        public static int SyokaiSindan = 9;
        ///<summary>
        ///10:診療時間
        ///</summary>
        public static int SinryoJikan = 10;
        ///<summary>
        ///11:疾患名
        ///</summary>
        public static int Sikkan = 11;
        ///<summary>
        ///20:撮影部位
        ///</summary>
        public static int SatueiBui = 20;
        ///<summary>
        ///21:撮影部位（胸部）
        ///</summary>
        public static int SatueiBuiKyobu = 21;
        ///<summary>
        ///22:撮影部位（腹部）
        ///</summary>
        public static int SatueiBuiFukubu = 22;
        /// <summary>
        /// 40:数量
        /// </summary>
        public static int Suryo = 40;
    }
}
