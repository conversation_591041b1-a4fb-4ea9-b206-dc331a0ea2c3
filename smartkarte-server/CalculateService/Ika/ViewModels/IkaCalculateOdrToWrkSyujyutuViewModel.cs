﻿using Entity.Tenant;
using CalculateService.Ika.DB.Finder;
using CalculateService.Ika.DB.CommandHandler;
using CalculateService.Ika.Models;
using CalculateService.Ika.Constants;
using Helper.Constants;
using CalculateService.Utils;
using Infrastructure.Interfaces;
using Domain.Constant;
using CalculateService.Interface;
using CalculateService.Constants;

namespace CalculateService.Ika.ViewModels
{
    class IkaCalculateOdrToWrkSyujyutuViewModel
    {
        private const string ModuleName = ModuleNameConst.EmrCalculateIka;

        private IkaCalculateCommonDataViewModel _common;

        private readonly ISystemConfigProvider _systemConfigProvider;
        private readonly IEmrLogger _emrLogger;

        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="common">共通データ</param>
        public IkaCalculateOdrToWrkSyujyutuViewModel(IkaCalculateCommonDataViewModel common,
            ISystemConfigProvider systemConfigProvider, IEmrLogger emrLogger)
        {
            _common = common;

            _systemConfigProvider = systemConfigProvider;
            _emrLogger = emrLogger;
        }

        /// <summary>
        /// 計算ロジック
        /// </summary>
        /// <param name="hpId">HospitalID</param>
        public void Calculate(int hpId)
        {
            const string conFncName = nameof(Calculate);
            _emrLogger.WriteLogStart(this, conFncName, "");

            if (_common.Odr.ExistOdrKoui(OdrKouiKbnConst.SyujyutuMin, OdrKouiKbnConst.SyujyutuMax))
            {
                // 保険
                CalculateHoken(hpId);

                // 自費
                CalculateJihi(hpId);
            }

            _common.Wrk.CommitWrkSinRpInf();

            _emrLogger.WriteLogEnd(this, conFncName, "");
        }

        /// <summary>
        /// 保険分を処理する
        /// </summary>
        /// <param name="hpId">HospitalID</param>
        private void CalculateHoken(int hpId)
        {
            const string conFncName = nameof(CalculateHoken);

            // 通常算定処理
            List<OdrInfModel> filteredOdrInf;
            List<OdrDtlTenModel> filteredOdrDtl;

            for (int i = 0; i <= 1; i++)
            {
                if (i == 0)
                {
                    // 手術・輸血のRpを取得
                    filteredOdrInf = _common.Odr.FilterOdrInfByKouiKbnRange(OdrKouiKbnConst.SyujyutuMin, OdrKouiKbnConst.Yuketu);
                }
                else
                {
                    // 麻酔
                    filteredOdrInf = _common.Odr.FilterOdrInfByKouiKbnRange(OdrKouiKbnConst.Masui, OdrKouiKbnConst.Masui);
                }

                if (filteredOdrInf.Any())
                {
                    foreach (OdrInfModel odrInf in filteredOdrInf)
                    {

                        // 行為に紐づく詳細を取得
                        filteredOdrDtl = _common.Odr.FilterOdrDetailByRpNo(odrInf.RpNo, odrInf.RpEdaNo);

                        if (filteredOdrDtl.Any())
                        {
                            string cdKbn = "K";

                            // 初回、必ずRpと行為のレコードを用意
                            _common.Wrk.AppendNewWrkSinRpInf(ReceKouiKbn.Syujyutu, ReceSinId.Syujyutu, odrInf.SanteiKbn);

                            // 集計先は、後で内容により変更する
                            cdKbn = _common.GetCdKbn(odrInf.SanteiKbn, cdKbn);
                            _common.Wrk.AppendNewWrkSinKoui(odrInf.HokenPid, odrInf.HokenId, ReceSyukeisaki.OpeMasui, cdKbn: cdKbn);

                            bool checkAgeKasan = false;
                            bool checkJikanKasan = false;

                            int firstItem = _common.CheckFirstItemSbt(filteredOdrDtl);
                            // コメントレコードを読み飛ばすフラグ（コメント以外の項目に付随するコメントをくっつけるためのフラグ）
                            //bool commentSkipFlg = (firstItem != 0);
                            // 手術のコメントは手技につける
                            bool commentSkipFlg = false;
                            // 最初のコメント以外の項目であることを示すフラグ
                            bool firstSinryoKoui = true;
                            // 閉鎖循環式全身麻酔を算定したことを示すフラグ
                            bool isSanteiHeisaZensinMasui = false;

                            // 労災四肢加算が手オーダーされているRpかどうかチェック
                            bool existsRosaiSisiKasan =
                                (_common.IsRosai &&
                                  filteredOdrDtl.Any(p =>
                                    p.ItemCd == ItemCdConst.SyujyutuRosaiSisiKasan ||
                                    p.ItemCd == ItemCdConst.SyujyutuRosaiSisiKasan2)
                                );

                            // 再製造加算で自動算定する項目
                            List<string> SaiseizoKikis = new List<string>();
                            if (i == 0 && _common.sinDate >= 20240601 &&
                                _common.Mst.ExistAutoSantei(ItemCdConst.SyujyutuSaiseizoKasanKanjyojyomyaku) &&
                                filteredOdrDtl.Any(p => string.IsNullOrEmpty(p.SaiseizoKiki) == false && p.SaiseizoKiki != "000") &&
                                _common.Odr.ExistOdrDetailByItemCd(ItemCdConst.SyujyutuSaiseizoCancel) == false)
                            {
                                SaiseizoKikis.AddRange(
                                    filteredOdrDtl.Select(p => p.SaiseizoKiki).Where(p => string.IsNullOrEmpty(p) == false && p != "000").Distinct());
                            }

                            // 閉鎖循環式全身麻酔の算定情報を取得
                            (string heisaSanteiItemCd, double heisaKihonTen, double heisaKasanTen) = GetHeisaZensinMasuiInf(odrInf.RpNo, odrInf.RpEdaNo);

                            foreach (OdrDtlTenModel odrDtl in filteredOdrDtl)
                            {
                                if (!odrDtl.IsJihi && (odrDtl.IsSorCommentItem(commentSkipFlg) || _common.IsSelectComment(odrDtl.ItemCd)))
                                {
                                    // 診療行為・コメント

                                    commentSkipFlg = false;

                                    if (odrDtl.IsKihonKoumoku && existsRosaiSisiKasan == false)
                                    {
                                        // 基本項目

                                        if (firstSinryoKoui == true)
                                        {
                                            // もう一カ所使用するので、falseにするのは最後にする
                                            //firstSinryoKoui = false;
                                        }
                                        else if (odrDtl.IsHeisaZensinMasui && isSanteiHeisaZensinMasui)
                                        {
                                            // 閉鎖循環式全身麻酔算定後同一Rp内でSeqNoを分けない
                                            // SeqNoを分けると点数を算定した項目に乳幼児加算が加算されないため
                                        }
                                        else
                                        {
                                            //最初以外の基本項目が来たらRpを分ける　
                                            //※最初にコメントが入っていると困るのでこういう処理にする

                                            //※手術は同一Rpにオーダーされていれば、一連の行為とみなすようにしておく
                                            //if (odrDtl.Kokuji2 != "7")
                                            //{
                                            //    _common.Wrk.AppendNewWrkSinRpInf(ReceKouiKbn.Syujyutu, ReceSinId.Syujyutu, odrInf.SanteiKbn);
                                            //}
                                            //_common.Wrk.AppendNewWrkSinKoui(odrInf.HokenPid, odrInf.HokenId, ReceSyukeisaki.OpeMasui);

                                            if (checkJikanKasan)
                                            {
                                                JikanKasan(i);

                                                checkJikanKasan = false;
                                            }

                                            cdKbn = _common.GetCdKbn(odrInf.SanteiKbn, cdKbn);
                                            _common.Wrk.AppendNewWrkSinKoui(odrInf.HokenPid, odrInf.HokenId, ReceSyukeisaki.OpeMasui, cdKbn: cdKbn);
                                        }
                                    }
                                    //firstSinryoKoui = false;

                                    if (!(odrDtl.IsComment))
                                    {
                                        // コメント項目以外

                                        // 算定回数チェック
                                        if (_common.CheckSanteiKaisu(odrDtl.ItemCd, odrDtl.SanteiKbn, odrDtl.HokenId, 0, odrDtl.Suryo) == 2)
                                        {
                                            // 算定回数マスタのチェックにより算定不可
                                            _common.Wrk.AppendNewWrkSinKouiDetail(hpId, odrDtl, _common.Odr.GetOdrCmt(odrDtl), isDeleted: DeleteStatus.DeleteFlag);
                                        }
                                        else if (_common.CheckAge(odrDtl) == 2)
                                        {
                                            // 年齢チェックにより算定不可
                                            _common.Wrk.AppendNewWrkSinKouiDetail(hpId, odrDtl, _common.Odr.GetOdrCmt(odrDtl), isDeleted: DeleteStatus.DeleteFlag);
                                        }
                                        else if (odrDtl.IsHeisaZensinMasui && string.IsNullOrEmpty(heisaSanteiItemCd))
                                        {
                                            // 閉鎖循環式全身麻酔は20分未満により算定不可
                                            _common.AppendCalcLog(2, string.Format(FormatConst.NotSanteiReason, odrDtl.ItemName, "一の全身麻酔において、閉鎖循環式全身麻酔の合計時間が20分未満"));
                                            _common.Wrk.AppendNewWrkSinKouiDetail(hpId, odrDtl, _common.Odr.GetOdrCmt(odrDtl), isDeleted: DeleteStatus.DeleteFlag);
                                        }
                                        else
                                        {
                                            _common.Wrk.AppendNewWrkSinKouiDetail(hpId, odrDtl, _common.Odr.GetOdrCmt(odrDtl));

                                            if (odrDtl.IsHeisaZensinMasui)
                                            {
                                                // 閉鎖循環式全身麻酔
                                                if (!isSanteiHeisaZensinMasui && odrDtl.ItemCd == heisaSanteiItemCd)
                                                {
                                                    // 最も点数の高い項目の基本点数と麻酔管理時間加算の点数を算定
                                                    _common.Wrk.wrkSinKouiDetails.Last().Ten = heisaKihonTen + heisaKasanTen;
                                                    // きざみ計算はし直さない
                                                    _common.Wrk.wrkSinKouiDetails.Last().AdjustTensu = true;
                                                    isSanteiHeisaZensinMasui = true;
                                                }
                                                else
                                                {
                                                    // 他の項目は摘要欄に名称と時間だけ記載する
                                                    _common.Wrk.wrkSinKouiDetails.Last().Ten = 0;
                                                    // きざみ計算はし直さない
                                                    _common.Wrk.wrkSinKouiDetails.Last().AdjustTensu = true;
                                                }
                                            }

                                            // 労災加算チェック
                                            if (_common.IsRosai)
                                            {
                                                if ((filteredOdrDtl.Any(p => p.ItemCd == ItemCdConst.SyujyutuRosaiSisiKasan || p.ItemCd == ItemCdConst.SyujyutuRosaiSisiKasan2) == false) &&
                                                        (_common.Wrk.wrkSinKouiDetails.Any(p =>
                                                        p.RaiinNo == _common.Wrk.RaiinNo &&
                                                        p.HokenKbn == _common.Wrk.HokenKbn &&
                                                        p.RpNo == _common.Wrk.RpNo &&
                                                        p.SeqNo == _common.Wrk.SeqNo &&
                                                        (p.ItemCd == ItemCdConst.SyujyutuRosaiSisiKasan || p.ItemCd == ItemCdConst.SyujyutuRosaiSisiKasan2) &&
                                                        p.IsDeleted == DeleteStatus.None)
                                                        == false))
                                                {
                                                    // 四肢加算項目のオーダーはない
                                                    if (filteredOdrDtl.Any(p => p.BuiKbn == 10))
                                                    {
                                                        if (odrDtl.SisiKbn == 1 || odrDtl.SisiKbn == 3)
                                                        {
                                                            // 四肢が存在する場合、２倍を自動算定
                                                            _common.Wrk.AppendNewWrkSinKouiDetail(ItemCdConst.SyujyutuRosaiSisiKasan2, autoAdd: 1);
                                                        }
                                                        else if (odrDtl.SisiKbn == 2)
                                                        {
                                                            // 四肢が存在する場合、１．５倍を自動算定
                                                            _common.Wrk.AppendNewWrkSinKouiDetail(ItemCdConst.SyujyutuRosaiSisiKasan, autoAdd: 1);
                                                        }
                                                    }
                                                    else if (filteredOdrDtl.Any(p => p.BuiKbn == 3))
                                                    {
                                                        if (odrDtl.SisiKbn == 1 || odrDtl.SisiKbn == 2)
                                                        {
                                                            // 四肢が存在する場合、１．５倍を自動算定
                                                            _common.Wrk.AppendNewWrkSinKouiDetail(ItemCdConst.SyujyutuRosaiSisiKasan, autoAdd: 1);
                                                        }
                                                    }
                                                }
                                            }

                                            // 年齢加算自動算定
                                            if (_common.AppendNewWrkSinKouiDetailAgeKasan(odrDtl, filteredOdrDtl) == false)
                                            {
                                                if (odrDtl.IsKihonKoumoku && odrDtl.SanteiItemCd != ItemCdConst.NoSantei)
                                                {
                                                    //AgeKasan(i, filteredOdrDtl);
                                                    checkAgeKasan = true;
                                                }
                                            }

                                            // 時間加算算定
                                            if (odrDtl.IsKihonKoumoku &&
                                                !(odrDtl.CdKbn == "K" && odrDtl.CdKbnno == 914) &&
                                                !(odrDtl.CdKbn == "K" && odrDtl.CdKbnno == 915) &&
                                                odrDtl.TimeKasanKbn > 0 &&
                                                new string[] { "1", "3", "5" }.Contains(odrDtl.Kokuji2))
                                            {
                                                //JikanKasan(i);
                                                checkJikanKasan = true;
                                            }

                                            // コメント自動追加
                                            _common.Wrk.AppendNewWrkSinKouiDetailComment(hpId, odrDtl, filteredOdrDtl);

                                            if (odrDtl.CdKbn != "")
                                            {
                                                if (odrDtl.SanteiKbn == SanteiKbnConst.Jihi)
                                                {
                                                    cdKbn = "JS";
                                                }
                                                else
                                                {
                                                    cdKbn = odrDtl.CdKbn;
                                                    if (cdKbn == "-")
                                                    {
                                                        if (odrDtl.SinKouiKbn == 50)
                                                        {
                                                            cdKbn = "K";
                                                        }
                                                        else
                                                        {
                                                            cdKbn = "L";
                                                        }
                                                    }
                                                }

                                                _common.Wrk.wrkSinKouis.Last().CdKbn = cdKbn;

                                            }

                                            //// 診療区分の設定
                                            //if(odrDtl.TenMst != null && odrDtl.TenMst.SinKouiKbn == OdrKouiKbnConst.Masui)
                                            //{
                                            //    // 初期値はOdrKouiKbnConst.Syujyutu(50)なので、麻酔の場合だけ、麻酔(OdrKouiKbnConst.Masui(54))に設定する
                                            //    _common.Wrk.wrkSinRpInfs.Last().SinId = ReceSinId.Masui;
                                            //}
                                        }

                                        // 診療区分の設定（オーダーが算定回数上限等で算定できなかったとしても、後の薬剤・特材の行為をこの項目の診区に変える必要があるため）
                                        if (firstSinryoKoui && odrDtl.TenMst != null && odrDtl.TenMst.SinKouiKbn == OdrKouiKbnConst.Masui)
                                        {
                                            // 初期値はOdrKouiKbnConst.Syujyutu(50)なので、麻酔の場合だけ、麻酔(OdrKouiKbnConst.Masui(54))に設定する
                                            _common.Wrk.wrkSinRpInfs.Last().SinId = ReceSinId.Masui;
                                        }
                                    }
                                    else
                                    {
                                        _common.Wrk.AppendNewWrkSinKouiDetail(hpId, odrDtl, _common.Odr.GetOdrCmt(odrDtl));
                                    }

                                    if (odrDtl.IsKihonKoumoku && existsRosaiSisiKasan == false)
                                    {
                                        // 基本項目

                                        if (firstSinryoKoui == true)
                                        {
                                            firstSinryoKoui = false;
                                        }
                                    }
                                }
                                else
                                {
                                    // 手術のコメントは手技につける
                                    //commentSkipFlg = true;
                                }

                            }

                            if (checkAgeKasan)
                            {
                                if (i == 0)
                                {
                                    SyujyutuAgeKasan(filteredOdrDtl);
                                }
                                else
                                {
                                    MasuiAgeKasan(filteredOdrDtl);
                                }
                            }

                            if (checkJikanKasan)
                            {
                                JikanKasan(i);
                            }

                            // 再製造単回使用医療機器使用加算
                            if (i == 0 && SaiseizoKikis.Any())
                            {
                                foreach (string saiseizoKiki in SaiseizoKikis)
                                {
                                    string saiseizoKasanItemCd = _common.Mst.GetSaiseizoKasan(saiseizoKiki);
                                    if (string.IsNullOrEmpty(saiseizoKasanItemCd) == false)
                                    {
                                        _common.Wrk.AppendNewWrkSinKouiDetail(saiseizoKasanItemCd, autoAdd: 1);
                                    }
                                }
                            }
                            // 薬剤・コメント算定

                            commentSkipFlg = false;

                            _common.Wrk.AppendOrUpdateKoui(odrInf.HokenPid, odrInf.HokenId, ReceSyukeisaki.OpeYakuzai, cdKbn, ref firstSinryoKoui);

                            foreach (OdrDtlTenModel odrDtl in filteredOdrDtl)
                            {
                                //if (odrDtl.IsYorCommentItem(commentSkipFlg))
                                if (odrDtl.IsYItem)
                                {
                                    // 薬剤・コメント
                                    _common.Wrk.AppendNewWrkSinKouiDetail(hpId, odrDtl, _common.Odr.GetOdrCmt(odrDtl));

                                    //commentSkipFlg = false;
                                }
                                //else if (_common.IsSelectComment(odrDtl.ItemCd))
                                //{
                                //    // 選択式コメントは手技で対応しているので読み飛ばす
                                //}
                                //else
                                //{
                                //    commentSkipFlg = true;
                                //}
                            }

                            // 特材・コメント算定

                            commentSkipFlg = false;

                            _common.Wrk.AppendOrUpdateKoui(odrInf.HokenPid, odrInf.HokenId, ReceSyukeisaki.OpeYakuzai, cdKbn, ref firstSinryoKoui);

                            foreach (OdrDtlTenModel odrDtl in filteredOdrDtl)
                            {
                                //if (odrDtl.IsTorCommentItem(commentSkipFlg))
                                if (odrDtl.IsTItem)
                                {
                                    // 特材・コメント
                                    _common.Wrk.AppendNewWrkSinKouiDetail(hpId, odrDtl, _common.Odr.GetOdrCmt(odrDtl));

                                    if (odrDtl.IsSanso)
                                    {
                                        // 酸素補正率
                                        _common.Wrk.AppendNewWrkSinKouiDetail(ItemCdConst.SansoHoseiRitu, autoAdd: 1);
                                    }
                                    //commentSkipFlg = false;
                                }
                                //else if (_common.IsSelectComment(odrDtl.ItemCd))
                                //{
                                //    // 選択式コメントは手技で対応しているので読み飛ばす
                                //}
                                //else
                                //{
                                //    commentSkipFlg = true;
                                //}
                            }
                        }
                    }

                    _common.Wrk.CommitWrkSinRpInf();
                }
            }
        }

        /// <summary>
        /// 時間外加算
        /// </summary>
        /// <param name="mode">
        ///     0-手術・輸血
        ///     1-麻酔
        /// </param>
        private void JikanKasan(int mode)
        {
            string[] jikangails =
                new string[] { ItemCdConst.SyujyutuJikangai, ItemCdConst.MasuiJikangai };
            string[] kyujituls =
                new string[] { ItemCdConst.SyujyutuKyujitu, ItemCdConst.MasuiKyujitu };
            string[] sinyals =
                new string[] { ItemCdConst.SyujyutuSinya, ItemCdConst.MasuiSinya };


            if (_common.Odr.ExistOdrDetailByItemCd(ItemCdConst.SyujyutuTimeKasanCancel) == false)
            {
                if (_common.jikan == JikanConst.JikanGai)
                {
                    _common.Wrk.AppendNewWrkSinKouiDetail(jikangails[mode], autoAdd: 1);
                }
                else if (_common.jikan == JikanConst.Kyujitu)
                {
                    _common.Wrk.AppendNewWrkSinKouiDetail(kyujituls[mode], autoAdd: 1);
                }
                else if (_common.jikan == JikanConst.Sinya)
                {
                    _common.Wrk.AppendNewWrkSinKouiDetail(sinyals[mode], autoAdd: 1);
                }
            }
        }
        private class MasuiJikanInf
        {
            public string ItemCd { get; set; }
            public double Ten { get; set; }
            public double KizamiTen { get; set; }
            public double Minute { get; set; }
            public void AddMinute(double minute)
            {
                Minute += minute;
            }
            private double _usedMinute;
            public double LeftMinute
            {
                get => Minute - _usedMinute;
            }
            public double UseMinute(double minute)
            {
                if (LeftMinute >= minute)
                {
                    _usedMinute += minute;
                    return minute;
                }
                else
                {
                    double bk = LeftMinute;
                    _usedMinute += LeftMinute;
                    return bk;
                }

            }
        }

        /// <summary>
        /// L008 閉鎖循環式全身麻酔の算定情報を取得
        /// </summary>
        private (string SanteiItemCd, double KihonTen, double KasanTen) GetHeisaZensinMasuiInf(long rpNo, long rpEdaNo)
        {
            const double conSanteiMinMinute = 20;
            const double conSyoteiMinute = 120;
            const double conKasanMinute = 30;
            string santeiItemCd = "";
            double kihonTensu = 0;
            double kasanTensu = 0;

            // 留意事項(10) 複数の点数に分類される麻酔や手術が一の全身麻酔の中で行われる場合においては、行われた麻酔の中で最も高い点数のものを算定する。
            var heisaZensinMasuis = _common.Odr.FilterOdrDetailHeisaZensinMasui(rpNo, rpEdaNo)
                .OrderByDescending(h => h.Ten);

            if (heisaZensinMasuis.Any() && heisaZensinMasuis.Sum(h => h.Suryo) >= conSanteiMinMinute)
            {
                // 留意事項(1) ガス麻酔器を使用する閉鎖式・半閉鎖式等の全身麻酔を20分以上実施した場合は、本区分により算定する。

                // 最も高い所定点数の項目を算定
                santeiItemCd = heisaZensinMasuis.First().ItemCd;
                kihonTensu = heisaZensinMasuis.First().Ten;

                // 時間の計算は留意事項(12)に従う
                //  (イ) 同じ点数区分にある麻酔の時間について合算する。
                List<MasuiJikanInf> masuiJikanInfs = new List<MasuiJikanInf> { };
                foreach (var heisaZensinMasui in heisaZensinMasuis)
                {
                    if (masuiJikanInfs.Any(m => m.ItemCd == heisaZensinMasui.ItemCd))
                    {
                        masuiJikanInfs.Find(m => m.ItemCd == heisaZensinMasui.ItemCd).AddMinute(heisaZensinMasui.Suryo);
                    }
                    else
                    {
                        masuiJikanInfs.Add(
                            new MasuiJikanInf
                            {
                                ItemCd = heisaZensinMasui.ItemCd,
                                Ten = heisaZensinMasui.Ten,
                                KizamiTen = heisaZensinMasui.KizamiTen,
                                Minute = heisaZensinMasui.Suryo
                            });
                    }
                }

                if (masuiJikanInfs.Sum(m => m.Minute) >= conSyoteiMinute)
                {
                    // (ロ) 麻酔時間の基本となる２時間については、その点数の高い区分の麻酔時間から順に充当する。
                    masuiJikanInfs = masuiJikanInfs.OrderByDescending(s => s.Ten).ToList();
                    double min = conSyoteiMinute;
                    foreach (var masuiJikanInf in masuiJikanInfs)
                    {
                        min -= masuiJikanInf.UseMinute(min);
                        if (min == 0) break;
                    }

                    //  (ハ) (ロ)の計算を行った残りの時間について、それぞれ「注２」の規定に従い30分又はその端数を増すごとに加算を行う。
                    masuiJikanInfs = masuiJikanInfs.OrderByDescending(s => s.LeftMinute).ToList();
                    foreach (var masuiJikanInf in masuiJikanInfs)
                    {
                        while (masuiJikanInf.LeftMinute >= conKasanMinute)
                        {
                            kasanTensu += masuiJikanInf.KizamiTen;
                            masuiJikanInf.UseMinute(conKasanMinute);
                        }
                    }

                    //  (ニ) (ハ)の場合において、各々の区分に係る麻酔が30分を超えない場合については、それらの麻酔の実施時間を合計し、
                    //  その中で実施時間の長い区分から順に加算を算定する。
                    //  なお、いずれの麻酔の実施時間も等しい場合には、その中で最も高い点数の区分に係る加算を算定する。
                    //  ※詳細は点数本の事務連絡の例を参照、     
                    
                    //  ア. 残りの合算時間を求める。
                    min = masuiJikanInfs.Sum(s => s.LeftMinute);
                    //  イ. 残りの時間が長い順、更に時間が等しい場合は、点数が高い順に並べる。
                    masuiJikanInfs = masuiJikanInfs.Where(s => s.LeftMinute > 0).OrderByDescending(s => s.LeftMinute).ThenByDescending(s => s.Ten).ToList();
                    
                    foreach (var masuiJikanInf in masuiJikanInfs)
                    {
                        if (min > 0)
                        {
                            //  ウ. 残りの合算時間が0を超えれば、1項目ずつ算定する。
                            kasanTensu += masuiJikanInf.KizamiTen;
                        }
                        else
                        {
                            //  オ. 残りの合算時間が0以下になれば更なる加算は算定しない。
                            break;
                        }
                        //  エ. 算定の度に残りの合算時間から30分引いていく。
                        min -= conKasanMinute;
                    }
                    
                }

            }

            return (santeiItemCd, kihonTensu, kasanTensu);

        }

        /// <summary>
        /// 年齢加算（手術・輸血）
        /// </summary>
        /// <param name="odrDtls"></param>
        private void SyujyutuAgeKasan(List<OdrDtlTenModel> odrDtls)
        {
            string itemCd = "";

            if (odrDtls.Any(p => p.ItemCd == ItemCdConst.SyujyutuMijyuku))
            {
                // 未熟児加算がある場合、年齢加算自動算定しない
            }
            else
            {
                if (_common.IsSinseiJi && odrDtls.Any(p => p.LowWeightKbn == 1))
                {
                    itemCd = ItemCdConst.SyujyutuSinseiji;
                }
                else if (_common.IsNyuyoJi)
                {
                    itemCd = ItemCdConst.SyujyutuNyuyoji;
                }
                else if (_common.IsYoJi)
                {
                    itemCd = ItemCdConst.SyujyutuYoji;
                }

                if (itemCd != "")
                {
                    _common.Wrk.AppendNewWrkSinKouiDetail(itemCd, autoAdd: 1);
                }
            }
        }

        /// <summary>
        /// 年齢加算（麻酔）
        /// </summary>
        /// <param name="odrDtls"></param>
        private void MasuiAgeKasan(List<OdrDtlTenModel> odrDtls)
        {

            string itemCd = "";

            if (odrDtls.Any(p => p.ItemCd == ItemCdConst.MasuiMijyuku))
            {
                // 未熟児加算がある場合、年齢加算自動算定しない
            }
            else
            {
                if (_common.IsSinseiJi)
                {
                    itemCd = ItemCdConst.MasuiSinseiji;
                }
                else if (_common.IsNyuJi)
                {
                    itemCd = ItemCdConst.MasuiNyuji;
                }
                else if (_common.IsNyuyoJi)
                {
                    itemCd = ItemCdConst.MasuiYoji;
                }

                if (itemCd != "")
                {
                    _common.Wrk.AppendNewWrkSinKouiDetail(itemCd, autoAdd: 1);
                }
            }
        }

        /// <summary>
        /// 自費算定分を処理する
        /// </summary>
        /// <param name="hpId">HospitalID</param>
        private void CalculateJihi(int hpId)
        {
            const string conFncName = nameof(CalculateJihi);

            _common.CalculateJihi(
                hpId,
                OdrKouiKbnConst.SyujyutuMin,
                OdrKouiKbnConst.SyujyutuMax,
                ReceKouiKbn.Syujyutu,
                ReceSinId.Syujyutu,
                ReceSyukeisaki.OpeMasui,
                "JS");
        }
    }
}
