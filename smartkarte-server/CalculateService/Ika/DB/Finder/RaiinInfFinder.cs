﻿using CalculateService.Extensions;
using PostgreDataContext;
using Helper.Constants;
using CalculateService.Ika.Models;
using CalculateService.Interface;
using Domain.Constant;
using Entity.Tenant;

namespace CalculateService.Ika.DB.Finder
{
    public class RaiinInfFinder
    {
        private readonly TenantDataContext _tenantDataContext;
        private readonly ISystemConfigProvider _systemConfigProvider;
        private readonly IEmrLogger _emrLogger;
        public RaiinInfFinder(TenantDataContext tenantDataContext, ISystemConfigProvider systemConfigProvider, IEmrLogger emrLogger)
        {
            _tenantDataContext = tenantDataContext;
            _systemConfigProvider = systemConfigProvider;
            _emrLogger = emrLogger;
        }

        //来院情報取得
        /// <summary>
        /// 来院情報取得に診療科マスタを結合したデータを取得
        /// </summary>
        /// <param name="hpId">医療機関識別ID</param>
        /// <param name="ptId">患者ID</param>
        /// <param name="sinDate">診療日</param>
        /// <returns>
        /// 指定の患者の指定の診療日の来院情報
        /// SIN_START_TIME順にソート
        /// </returns>
        public List<RaiinInfModel> FindRaiinInfData(int hpId, long ptId, int sinDate)
        {
            var kaMsts = _tenantDataContext.KaMsts.FindListQueryableNoTrack(o =>
                o.HpId == hpId &&
                o.IsDeleted == DeleteStatus.None);
            var raiinInfs = _tenantDataContext.RaiinInfs.FindListQueryableNoTrack(p =>
                p.HpId == hpId &&
                p.PtId == ptId &&
                p.SinDate == sinDate &&
                //p.Status >= 5 &&
                p.IsDeleted == DeleteTypes.None);
            var reserveDetails = _tenantDataContext.ReserveDetails.FindListQueryableNoTrack();
            var ptInfs = _tenantDataContext.PtInfs.FindListQueryableNoTrack(p =>
                p.HpId == hpId &&
                p.PtId == ptId &&
                p.IsDelete == DeleteTypes.None);

            var joinQuery = (
                from raiinInf in raiinInfs
                join ptInf in ptInfs on
                    new { raiinInf.HpId, raiinInf.PtId } equals
                    new { ptInf.HpId, ptInf.PtId }
                join reserveDetail in reserveDetails on
                    new { raiinInf.ReserveDetailId } equals
                    new { ReserveDetailId = (int?)reserveDetail.ReserveDetailId } into rJoin
                from rj in rJoin.DefaultIfEmpty()
                join kaMst in kaMsts on
                    new { raiinInf.HpId, raiinInf.KaId } equals
                    new { kaMst.HpId, kaMst.KaId } into kaJoin
                from ka in kaJoin.DefaultIfEmpty()
                where
                    raiinInf.HpId == hpId &&
                    raiinInf.PtId == ptId &&
                    raiinInf.SinDate == sinDate &&
                    raiinInf.IsDeleted == DeleteTypes.None
                orderby
                    raiinInf.HpId, raiinInf.PtId, raiinInf.SinDate, ("000000" + raiinInf.SinStartTime ?? "").Substring((raiinInf.SinStartTime ?? "").Length, 6), raiinInf.OyaRaiinNo, raiinInf.RaiinNo
                select new
                {
                    raiinInf,
                    reserveDetail = rj,
                    kaMst = ka
                }
            );

            var entities = joinQuery.AsEnumerable().Select(
                data =>
                    new RaiinInfModel(data.raiinInf, data.kaMst, data.reserveDetail)
                )
                .ToList();

            List<RaiinInfModel> results = new List<RaiinInfModel>();

            entities?.ForEach(entity =>
            {
                results.Add(new RaiinInfModel(entity.RaiinInf, entity.KaMst, entity.ReserveDetail));
            });

            return results;
        }

        //来院情報取得
        /// <summary>
        /// 診療月に属する当該患者の全来院日を取得する
        /// </summary>
        /// <param name="hpId">医療機関識別ID</param>
        /// <param name="ptId">患者ID</param>
        /// <param name="sinDate">診療日</param>
        /// <returns>
        /// 指定の患者の指定の診療日に属する月の来院日情報
        /// </returns>
        public List<RaiinDaysModel> FindRaiinInfDays(int hpId, long ptId, int sinDate)
        {
            var raiinInfs = _tenantDataContext.RaiinInfs.FindListQueryableNoTrack(p =>
                p.HpId == hpId &&
                p.PtId == ptId &&
                p.SinDate >= sinDate / 100 * 100 + 1 &&
                p.SinDate <= sinDate / 100 * 100 + 31 &&
                p.Status >= RaiinState.ConsultationCompleted &&
                p.IsDeleted == DeleteTypes.None).AsEnumerable();

            var joinQuery = (
                from raiinInf in raiinInfs
                where
                    raiinInf.HpId == hpId &&
                    raiinInf.PtId == ptId &&
                    raiinInf.SinDate >= sinDate / 100 * 100 + 1 &&
                    raiinInf.SinDate <= sinDate / 100 * 100 + 31 &&
                    raiinInf.IsDeleted == DeleteTypes.None
                //group raiinInf by
                //    new { HpId = raiinInf.HpId, PtId = raiinInf.PtId, SinDate = raiinInf.SinDate } into A
                //orderby
                //    A.Key.HpId, A.Key.PtId, A.Key.SinDate
                select raiinInf
            );

            var raiinList = joinQuery.ToList();

            var result = raiinList
                .GroupBy(r => new { r.HpId, r.PtId, r.SinDate })
                .Select(r => new RaiinDaysModel(r.Key.HpId, r.Key.PtId, r.Key.SinDate))
                .ToList();

            //var entities =
            return result;

            //List<RaiinDaysModel> results = new List<RaiinDaysModel>();

            //entities?.ForEach(entity => {
            //    results.Add(new RaiinDaysModel(entity.HpId, entity.PtId, entity.SinDate));
            //});

            //return results;
        }

        //来院情報取得
        /// <summary>
        /// 請求年月に属する当該患者の全来院日を取得する
        /// </summary>
        /// <param name="hpId">医療機関識別ID</param>
        /// <param name="seikyuYm">請求年月</param>
        /// <param name="ptIds">患者ID</param>
        /// <returns>
        /// 指定の請求年月に属する月の来院日情報
        /// </returns>
        public List<RaiinDaysModel> FindRaiinInfDaysInMonth(int hpId, int seikyuYm, List<long> ptIds)
        {
            int fromSinDate = seikyuYm * 100 + 1;
            int toSinDate = seikyuYm * 100 + 99;

            var receSeikyus = _tenantDataContext.ReceSeikyus.FindListQueryableNoTrack(r => r.IsDeleted == DeleteStatus.None);

            var maxReceSeikyus = _tenantDataContext.ReceSeikyus.FindListQueryableNoTrack(
                r => r.IsDeleted == DeleteStatus.None
            ).GroupBy(
                r => new { r.HpId, r.SinYm, r.PtId, r.HokenId }
            ).Select(
                r => new
                {
                    r.Key.HpId,
                    r.Key.SinYm,
                    r.Key.PtId,
                    r.Key.HokenId,
                    SeikyuYm = r.Max(x => x.SeikyuYm)
                }
            );

            var raiinInfs = _tenantDataContext.RaiinInfs.FindListQueryableNoTrack();
            if (ptIds?.Count >= 1)
            {
                raiinInfs = raiinInfs.Where(r => ptIds.Contains(r.PtId));
            }

            var maxReceSeikyuList = maxReceSeikyus.Where(item => item.SeikyuYm == seikyuYm).ToList();
            var ptIdList = maxReceSeikyuList.Select(item => item.PtId).Distinct().ToList();
            var sinYmList = maxReceSeikyuList.Select(item => item.SinYm).Distinct().ToList();

            var raiinInfList = raiinInfs.Where(raiinInf => raiinInf.HpId == hpId &&
                                               raiinInf.Status >= RaiinState.ConsultationCompleted &&
                                               raiinInf.IsDeleted == DeleteTypes.None &&
                                               (
                                                   //当月分
                                                   (raiinInf.SinDate >= fromSinDate && raiinInf.SinDate <= toSinDate) ||
                                                   //月遅れ・返戻分
                                                   (
                                                   ptIdList.Contains(raiinInf.PtId) &&
                                                   sinYmList.Contains(raiinInf.SinDate / 100))
                                               ))
                                         .ToList();

            ptIdList = raiinInfList.Select(item => item.PtId).Distinct().ToList();
            sinYmList = raiinInfList.Select(item => (int)Math.Floor((double)item.SinDate / 100)).Distinct().ToList();
            var receSeikyuList = receSeikyus.Where(item => item.HpId == hpId
                                                           && ptIdList.Contains(item.PtId)
                                                           && sinYmList.Contains(item.SinYm))
                                            .ToList();

            var joinQuery = (
                from raiinInf in raiinInfList
                join rs in receSeikyuList on
                    new { raiinInf.HpId, raiinInf.PtId, SinYm = (int)Math.Floor((double)raiinInf.SinDate / 100) } equals
                    new { rs.HpId, rs.PtId, rs.SinYm } into rsJoin
                from receSeikyu in rsJoin.DefaultIfEmpty()
                where
                            raiinInf.HpId == hpId &&
                            raiinInf.Status >= RaiinState.ConsultationCompleted &&
                            raiinInf.IsDeleted == DeleteTypes.None &&
                            (
                                //当月分
                                (raiinInf.SinDate >= fromSinDate && raiinInf.SinDate <= toSinDate) ||
                                //月遅れ・返戻分
                                (
                                    (
                                        from rs1 in maxReceSeikyuList
                                        where
                                            rs1.HpId == hpId &&
                                            rs1.SeikyuYm == seikyuYm
                                        select rs1
                                    ).Any(
                                        r =>
                                            r.HpId == raiinInf.HpId &&
                                            r.PtId == raiinInf.PtId &&
                                            r.SinYm == raiinInf.SinDate / 100
                                    )
                                )
                            )
                //&&
                //(
                //    //当月の月遅れ・返戻分を除く
                //    !(
                //        from rs2 in receSeikyus
                //        where
                //            rs2.HpId == hpId &&
                //            rs2.SeikyuYm != seikyuYm
                //        select rs2
                //    ).Any(
                //        r =>
                //            r.HpId == raiinInf.HpId &&
                //            r.PtId == raiinInf.PtId &&
                //            r.SinYm == raiinInf.SinDate / 100
                //    )
                //)
                select raiinInf
            );

            var raiinList = joinQuery.ToList();

            var result =
                raiinList
                .GroupBy(r => new { r.HpId, r.PtId, r.SinDate })
                .Select(k => new RaiinDaysModel(k.Key.HpId, k.Key.PtId, k.Key.SinDate))
                .ToList();

            return result;
        }

        public ReserveDetail FindReserveDetail(int? reserveDetailId)
        {
            if (reserveDetailId == null)
            {
                return null;
            }
            else
            {
                var entities = _tenantDataContext.ReserveDetails.FindListQueryableNoTrack(p =>
                    p.ReserveDetailId == reserveDetailId &&
                    p.IsDeleted == DeleteStatus.None)
                    .FirstOrDefault();

                if (entities == null)
                {
                    return null;
                }
                return entities;
            }
        }
    }
}
