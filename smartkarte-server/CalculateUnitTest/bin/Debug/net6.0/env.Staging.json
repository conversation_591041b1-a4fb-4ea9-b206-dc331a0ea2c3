{"TenantDbWriter": "host=develop-smartkarte-postgres.ckthopedhq8w.ap-northeast-1.rds.amazonaws.com;port=5432;database=smartkarte;user id=postgres;password=************", "TenantDbReader": "host=develop-smartkarte-postgres.ckthopedhq8w.ap-northeast-1.rds.amazonaws.com;port=5432;database=smartkarte;user id=postgres;password=************", "DomainList": {"smartkarte.sotatek.works": "host=develop-smartkarte-postgres.ckthopedhq8w.ap-northeast-1.rds.amazonaws.com;port=5432;database=smartkarte;user id=postgres;password=************", "uat-tenant.smartkarte.org": "host=develop-smartkarte-postgres.ckthopedhq8w.ap-northeast-1.rds.amazonaws.com;port=5432;database=smartkarte_new;user id=postgres;password=************", "gmoho.smartkarte.org": "host=develop-smartkarte-postgres.ckthopedhq8w.ap-northeast-1.rds.amazonaws.com;port=5432;database=gmoho_smartkarte;user id=postgres;password=************"}, "AmazonS3": {"AwsAccessKeyId": "********************", "AwsSecretAccessKey": "hPyYCSSHmlK49TQ7blhrKzJurLA4eMVFeKiotVA9", "Region": "ap-southeast-1", "BucketName": "develop-smartkarte-images-bucket", "BaseAccessUrl": "https://develop-smartkarte-images-bucket.s3.ap-southeast-1.amazonaws.com"}, "DefaultImageDrugEmpty": "https://develop-smartkarte-images-bucket.s3.ap-southeast-1.amazonaws.com/image/defaultDrug/ImageDrugEmpty.png", "Karte2TemplateDefault": "https://develop-smartkarte-images-bucket.s3.ap-southeast-1.amazonaws.com/image/karte2Template/index.html", "PathImageDrugServer": "https://develop-smartkarte-images-bucket.s3.ap-southeast-1.amazonaws.com", "RenderPdf": {"BasePath": "https://smartkarte-report.sotatek.works/api/"}, "CalculateApi": {"BasePath": "https://smartkarte-calculate.sotatek.works/api/"}, "RenderKarte2ReportApi": {"BasePath": "https://demo.gotenberg.dev/forms/chromium/convert/html"}, "Redis": {"RedisHost": "develop-smartkarte-redis.jyjoo4.ng.0001.apne1.cache.amazonaws.com", "RedisPort": "6379"}}