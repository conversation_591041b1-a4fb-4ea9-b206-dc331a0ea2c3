#!/bin/sh

echo "Running pre-commit hook..."

# ステージされたファイルのリストを取得
staged_files=$(git diff --cached --name-only)

# GMO用の設定ファイルの変更を取り消す
for file in $staged_files; do
    if [[ "$file" == *"appsettings.json"* ]] || \
        [[ "$file" == *"launchSettings.json"* ]] || \
        [[ "$file" == *"env.Development.json"* ]] || \
        [[ "$file" == *"unittest.env.json"* ]]; then
            echo "Detected changes in $file, resetting changes..."
            git restore --staged $file
            git restore $file
    fi
done