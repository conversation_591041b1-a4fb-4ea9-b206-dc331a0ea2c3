﻿using Domain.Models.OrdInfDetails;
using Helper.Constants;

namespace CloudUnitTest.Model.OrdInf
{
    public class OrdInfDetailModelTest
    {
        [Test]
        public void OrdInfDetailModel_001_IsSpecialItem()
        {

            string masterSbt = "S";
            int sinKouiKbn = 20;
            int drugKbn = 0;
            string itemCd = "test";
            #region Data Example
            var ordInfDetail = new OrdInfDetailModel(
            hpId: 1,
            raiinNo: 123456789,
            rpNo: 987654321,
            rpEdaNo: 456789123,
            rowNo: 1,
            ptId: 123,
            sinDate: 20240320,
            sinKouiKbn: sinKouiKbn,
            itemCd: itemCd,
            itemName: "Example Item",
            suryo: 10.5,
            unitName: "Unit",
            unitSbt: 1,
            termVal: 5.5,
            kohatuKbn: 1,
            syohoKbn: 1,
            syohoLimitKbn: 1,
            drugKbn: drugKbn,
            yohoKbn: 1,
            kokuji1: "Kokuji1",
            kokuji2: "Kokuji2",
            isNodspRece: 0,
            ipnCd: "IPN123",
            ipnName: "Example IPN",
            jissiKbn: 1,
            jissiDate: DateTime.Now,
            jissiId: 123,
            jissiMachine: "Machine123",
            reqCd: "REQ123",
            bunkatu: "Bunkatu",
            cmtName: "Comment Name",
            cmtOpt: "Comment Option",
            fontColor: "Black",
            commentNewline: 1,
            masterSbt: masterSbt,
            inOutKbn: 1,
            yakka: 10.0,
            isGetPriceInYakka: true,
            refillSetting: 1,
            cmtCol1: 1,
            ten: 5.5,
            bunkatuKoui: 1,
            alternationIndex: 1,
            kensaGaichu: 1,
            odrTermVal: 5.5,
            cnvTermVal: 5.5,
            yjCd: "YJ123",
            yohoSets: new List<YohoSetMstModel>(),
            kasan1: 1,
            kasan2: 1,
            cnvUnitName: "CnvUnit",
            odrUnitName: "OdrUnit",
            centerItemCd1: "CenterItemCd1",
            centerItemCd2: "CenterItemCd2",
            cmtCol2: 1,
            cmtCol3: 1,
            cmtCol4: 1,
            cmtColKeta1: 1,
            cmtColKeta2: 1,
            cmtColKeta3: 1,
            cmtColKeta4: 1,
            handanGrpKbn: 1,
            isKensaMstEmpty: true,
            odrKouiKbn: 0,
            kensaMstModel: new()
            );
            #endregion
            Assert.True(ordInfDetail.IsSpecialItem);
        }

        [Test]
        public void OrdInfDetailModel_002_IsSpecialItem()
        {

            string masterSbt = "S";
            int sinKouiKbn = 20;
            int drugKbn = 0;
            string itemCd = ItemCdConst.Con_TouyakuOrSiBunkatu;
            #region Data Example
            var ordInfDetail = new OrdInfDetailModel(
            hpId: 1,
            raiinNo: 123456789,
            rpNo: 987654321,
            rpEdaNo: 456789123,
            rowNo: 1,
            ptId: 123,
            sinDate: 20240320,
            sinKouiKbn: sinKouiKbn,
            itemCd: itemCd,
            itemName: "Example Item",
            suryo: 10.5,
            unitName: "Unit",
            unitSbt: 1,
            termVal: 5.5,
            kohatuKbn: 1,
            syohoKbn: 1,
            syohoLimitKbn: 1,
            drugKbn: drugKbn,
            yohoKbn: 1,
            kokuji1: "Kokuji1",
            kokuji2: "Kokuji2",
            isNodspRece: 0,
            ipnCd: "IPN123",
            ipnName: "Example IPN",
            jissiKbn: 1,
            jissiDate: DateTime.Now,
            jissiId: 123,
            jissiMachine: "Machine123",
            reqCd: "REQ123",
            bunkatu: "Bunkatu",
            cmtName: "Comment Name",
            cmtOpt: "Comment Option",
            fontColor: "Black",
            commentNewline: 1,
            masterSbt: masterSbt,
            inOutKbn: 1,
            yakka: 10.0,
            isGetPriceInYakka: true,
            refillSetting: 1,
            cmtCol1: 1,
            ten: 5.5,
            bunkatuKoui: 1,
            alternationIndex: 1,
            kensaGaichu: 1,
            odrTermVal: 5.5,
            cnvTermVal: 5.5,
            yjCd: "YJ123",
            yohoSets: new List<YohoSetMstModel>(),
            kasan1: 1,
            kasan2: 1,
            cnvUnitName: "CnvUnit",
            odrUnitName: "OdrUnit",
            centerItemCd1: "CenterItemCd1",
            centerItemCd2: "CenterItemCd2",
            cmtCol2: 1,
            cmtCol3: 1,
            cmtCol4: 1,
            cmtColKeta1: 1,
            cmtColKeta2: 1,
            cmtColKeta3: 1,
            cmtColKeta4: 1,
            handanGrpKbn: 1,
            isKensaMstEmpty: true,
            odrKouiKbn: 0,
            kensaMstModel: new()
            );
            #endregion
            Assert.True(!ordInfDetail.IsSpecialItem);
        }

        [Test]
        public void OrdInfDetailModel_003_IsSpecialItem()
        {

            string masterSbt = "S";
            int sinKouiKbn = 20;
            int drugKbn = 0;
            string itemCd = ItemCdConst.Con_Refill;
            #region Data Example
            var ordInfDetail = new OrdInfDetailModel(
            hpId: 1,
            raiinNo: 123456789,
            rpNo: 987654321,
            rpEdaNo: 456789123,
            rowNo: 1,
            ptId: 123,
            sinDate: 20240320,
            sinKouiKbn: sinKouiKbn,
            itemCd: itemCd,
            itemName: "Example Item",
            suryo: 10.5,
            unitName: "Unit",
            unitSbt: 1,
            termVal: 5.5,
            kohatuKbn: 1,
            syohoKbn: 1,
            syohoLimitKbn: 1,
            drugKbn: drugKbn,
            yohoKbn: 1,
            kokuji1: "Kokuji1",
            kokuji2: "Kokuji2",
            isNodspRece: 0,
            ipnCd: "IPN123",
            ipnName: "Example IPN",
            jissiKbn: 1,
            jissiDate: DateTime.Now,
            jissiId: 123,
            jissiMachine: "Machine123",
            reqCd: "REQ123",
            bunkatu: "Bunkatu",
            cmtName: "Comment Name",
            cmtOpt: "Comment Option",
            fontColor: "Black",
            commentNewline: 1,
            masterSbt: masterSbt,
            inOutKbn: 1,
            yakka: 10.0,
            isGetPriceInYakka: true,
            refillSetting: 1,
            cmtCol1: 1,
            ten: 5.5,
            bunkatuKoui: 1,
            alternationIndex: 1,
            kensaGaichu: 1,
            odrTermVal: 5.5,
            cnvTermVal: 5.5,
            yjCd: "YJ123",
            yohoSets: new List<YohoSetMstModel>(),
            kasan1: 1,
            kasan2: 1,
            cnvUnitName: "CnvUnit",
            odrUnitName: "OdrUnit",
            centerItemCd1: "CenterItemCd1",
            centerItemCd2: "CenterItemCd2",
            cmtCol2: 1,
            cmtCol3: 1,
            cmtCol4: 1,
            cmtColKeta1: 1,
            cmtColKeta2: 1,
            cmtColKeta3: 1,
            cmtColKeta4: 1,
            handanGrpKbn: 1,
            isKensaMstEmpty: true,
            odrKouiKbn: 0,
            kensaMstModel: new()
            );
            #endregion
            Assert.True(!ordInfDetail.IsSpecialItem);
        }

        [Test]
        public void OrdInfDetailModel_004_IsDrugUsage()
        {
            #region Data Example         
            int yohoKbn = 1;
            string itemCd = "test";
            OrdInfDetailModel ordInfDetail = new OrdInfDetailModel(
            hpId: 1,
            raiinNo: 123456789,
            rpNo: 987654321,
            rpEdaNo: 456789123,
            rowNo: 1,
            ptId: 123,
            sinDate: 20240320,
            sinKouiKbn: 1,
            itemCd: itemCd,
            itemName: "Example Item",
            suryo: 10.5,
            unitName: "Unit",
            unitSbt: 1,
            termVal: 5.5,
            kohatuKbn: 1,
            syohoKbn: 1,
            syohoLimitKbn: 1,
            drugKbn: 0,
            yohoKbn: yohoKbn,
            kokuji1: "Kokuji1",
            kokuji2: "Kokuji2",
            isNodspRece: 0,
            ipnCd: "IPN123",
            ipnName: "Example IPN",
            jissiKbn: 1,
            jissiDate: DateTime.Now,
            jissiId: 123,
            jissiMachine: "Machine123",
            reqCd: "REQ123",
            bunkatu: "Bunkatu",
            cmtName: "Comment Name",
            cmtOpt: "Comment Option",
            fontColor: "Black",
            commentNewline: 1,
            masterSbt: "MasterSbt",
            inOutKbn: 1,
            yakka: 10.0,
            isGetPriceInYakka: true,
            refillSetting: 1,
            cmtCol1: 1,
            ten: 5.5,
            bunkatuKoui: 1,
            alternationIndex: 1,
            kensaGaichu: 1,
            odrTermVal: 5.5,
            cnvTermVal: 5.5,
            yjCd: "YJ123",
            yohoSets: new List<YohoSetMstModel>(),
            kasan1: 1,
            kasan2: 1,
            cnvUnitName: "CnvUnit",
            odrUnitName: "OdrUnit",
            centerItemCd1: "CenterItemCd1",
            centerItemCd2: "CenterItemCd2",
            cmtCol2: 1,
            cmtCol3: 1,
            cmtCol4: 1,
            cmtColKeta1: 1,
            cmtColKeta2: 1,
            cmtColKeta3: 1,
            cmtColKeta4: 1,
            handanGrpKbn: 1,
            isKensaMstEmpty: true,
            odrKouiKbn: 0,
            kensaMstModel: new()
            );

            #endregion
            Assert.True(ordInfDetail.IsDrugUsage);
        }

        [Test]
        public void OrdInfDetailModel_005_IsDrugUsage()
        {
            #region Data Example         
            int yohoKbn = 0;
            string itemCd = ItemCdConst.TouyakuChozaiNaiTon;
            OrdInfDetailModel ordInfDetail = new OrdInfDetailModel(
            hpId: 1,
            raiinNo: 123456789,
            rpNo: 987654321,
            rpEdaNo: 456789123,
            rowNo: 1,
            ptId: 123,
            sinDate: 20240320,
            sinKouiKbn: 1,
            itemCd: itemCd,
            itemName: "Example Item",
            suryo: 10.5,
            unitName: "Unit",
            unitSbt: 1,
            termVal: 5.5,
            kohatuKbn: 1,
            syohoKbn: 1,
            syohoLimitKbn: 1,
            drugKbn: 0,
            yohoKbn: yohoKbn,
            kokuji1: "Kokuji1",
            kokuji2: "Kokuji2",
            isNodspRece: 0,
            ipnCd: "IPN123",
            ipnName: "Example IPN",
            jissiKbn: 1,
            jissiDate: DateTime.Now,
            jissiId: 123,
            jissiMachine: "Machine123",
            reqCd: "REQ123",
            bunkatu: "Bunkatu",
            cmtName: "Comment Name",
            cmtOpt: "Comment Option",
            fontColor: "Black",
            commentNewline: 1,
            masterSbt: "MasterSbt",
            inOutKbn: 1,
            yakka: 10.0,
            isGetPriceInYakka: true,
            refillSetting: 1,
            cmtCol1: 1,
            ten: 5.5,
            bunkatuKoui: 1,
            alternationIndex: 1,
            kensaGaichu: 1,
            odrTermVal: 5.5,
            cnvTermVal: 5.5,
            yjCd: "YJ123",
            yohoSets: new List<YohoSetMstModel>(),
            kasan1: 1,
            kasan2: 1,
            cnvUnitName: "CnvUnit",
            odrUnitName: "OdrUnit",
            centerItemCd1: "CenterItemCd1",
            centerItemCd2: "CenterItemCd2",
            cmtCol2: 1,
            cmtCol3: 1,
            cmtCol4: 1,
            cmtColKeta1: 1,
            cmtColKeta2: 1,
            cmtColKeta3: 1,
            cmtColKeta4: 1,
            handanGrpKbn: 1,
            isKensaMstEmpty: true,
            odrKouiKbn: 0,
            kensaMstModel: new()
            );

            #endregion
            Assert.True(ordInfDetail.IsDrugUsage);
        }

        [Test]
        public void OrdInfDetailModel_006_IsDrugUsage()
        {
            #region Data Example         
            int yohoKbn = 0;
            string itemCd = ItemCdConst.TouyakuChozaiGai;
            OrdInfDetailModel ordInfDetail = new OrdInfDetailModel(
            hpId: 1,
            raiinNo: 123456789,
            rpNo: 987654321,
            rpEdaNo: 456789123,
            rowNo: 1,
            ptId: 123,
            sinDate: 20240320,
            sinKouiKbn: 1,
            itemCd: itemCd,
            itemName: "Example Item",
            suryo: 10.5,
            unitName: "Unit",
            unitSbt: 1,
            termVal: 5.5,
            kohatuKbn: 1,
            syohoKbn: 1,
            syohoLimitKbn: 1,
            drugKbn: 0,
            yohoKbn: yohoKbn,
            kokuji1: "Kokuji1",
            kokuji2: "Kokuji2",
            isNodspRece: 0,
            ipnCd: "IPN123",
            ipnName: "Example IPN",
            jissiKbn: 1,
            jissiDate: DateTime.Now,
            jissiId: 123,
            jissiMachine: "Machine123",
            reqCd: "REQ123",
            bunkatu: "Bunkatu",
            cmtName: "Comment Name",
            cmtOpt: "Comment Option",
            fontColor: "Black",
            commentNewline: 1,
            masterSbt: "MasterSbt",
            inOutKbn: 1,
            yakka: 10.0,
            isGetPriceInYakka: true,
            refillSetting: 1,
            cmtCol1: 1,
            ten: 5.5,
            bunkatuKoui: 1,
            alternationIndex: 1,
            kensaGaichu: 1,
            odrTermVal: 5.5,
            cnvTermVal: 5.5,
            yjCd: "YJ123",
            yohoSets: new List<YohoSetMstModel>(),
            kasan1: 1,
            kasan2: 1,
            cnvUnitName: "CnvUnit",
            odrUnitName: "OdrUnit",
            centerItemCd1: "CenterItemCd1",
            centerItemCd2: "CenterItemCd2",
            cmtCol2: 1,
            cmtCol3: 1,
            cmtCol4: 1,
            cmtColKeta1: 1,
            cmtColKeta2: 1,
            cmtColKeta3: 1,
            cmtColKeta4: 1,
            handanGrpKbn: 1,
            isKensaMstEmpty: true,
            odrKouiKbn: 0,
            kensaMstModel: new()
            );

            #endregion
            Assert.True(ordInfDetail.IsDrugUsage);
        }

        [Test]
        public void OrdInfDetailModel_007_IsDrug()
        {
            #region Data Example  
            int sinKouiKbn = 20;
            int drugKbn = 1;
            string itemCd = "test";
            OrdInfDetailModel ordInfDetail = new OrdInfDetailModel(
            hpId: 1,
            raiinNo: 123456789,
            rpNo: 987654321,
            rpEdaNo: 456789123,
            rowNo: 1,
            ptId: 123,
            sinDate: 20240320,
            sinKouiKbn: sinKouiKbn,
            itemCd: itemCd,
            itemName: "Example Item",
            suryo: 10.5,
            unitName: "Unit",
            unitSbt: 1,
            termVal: 5.5,
            kohatuKbn: 1,
            syohoKbn: 1,
            syohoLimitKbn: 1,
            drugKbn: drugKbn,
            yohoKbn: 1,
            kokuji1: "Kokuji1",
            kokuji2: "Kokuji2",
            isNodspRece: 0,
            ipnCd: "IPN123",
            ipnName: "Example IPN",
            jissiKbn: 1,
            jissiDate: DateTime.Now,
            jissiId: 123,
            jissiMachine: "Machine123",
            reqCd: "REQ123",
            bunkatu: "Bunkatu",
            cmtName: "Comment Name",
            cmtOpt: "Comment Option",
            fontColor: "Black",
            commentNewline: 1,
            masterSbt: "MasterSbt",
            inOutKbn: 1,
            yakka: 10.0,
            isGetPriceInYakka: true,
            refillSetting: 1,
            cmtCol1: 1,
            ten: 5.5,
            bunkatuKoui: 1,
            alternationIndex: 1,
            kensaGaichu: 1,
            odrTermVal: 5.5,
            cnvTermVal: 5.5,
            yjCd: "YJ123",
            yohoSets: new List<YohoSetMstModel>(),
            kasan1: 1,
            kasan2: 1,
            cnvUnitName: "CnvUnit",
            odrUnitName: "OdrUnit",
            centerItemCd1: "CenterItemCd1",
            centerItemCd2: "CenterItemCd2",
            cmtCol2: 1,
            cmtCol3: 1,
            cmtCol4: 1,
            cmtColKeta1: 1,
            cmtColKeta2: 1,
            cmtColKeta3: 1,
            cmtColKeta4: 1,
            handanGrpKbn: 1,
            isKensaMstEmpty: true,
            odrKouiKbn: 0,
            kensaMstModel: new()
            );

            #endregion
            Assert.True(ordInfDetail.IsDrug);
        }

        [Test]
        public void OrdInfDetailModel_008_IsDrug()
        {
            #region Data Example  
            int sinKouiKbn = 21;
            int drugKbn = 1;
            string itemCd = "test";
            OrdInfDetailModel ordInfDetail = new OrdInfDetailModel(
            hpId: 1,
            raiinNo: 123456789,
            rpNo: 987654321,
            rpEdaNo: 456789123,
            rowNo: 1,
            ptId: 123,
            sinDate: 20240320,
            sinKouiKbn: sinKouiKbn,
            itemCd: itemCd,
            itemName: "Example Item",
            suryo: 10.5,
            unitName: "Unit",
            unitSbt: 1,
            termVal: 5.5,
            kohatuKbn: 1,
            syohoKbn: 1,
            syohoLimitKbn: 1,
            drugKbn: drugKbn,
            yohoKbn: 1,
            kokuji1: "Kokuji1",
            kokuji2: "Kokuji2",
            isNodspRece: 0,
            ipnCd: "IPN123",
            ipnName: "Example IPN",
            jissiKbn: 1,
            jissiDate: DateTime.Now,
            jissiId: 123,
            jissiMachine: "Machine123",
            reqCd: "REQ123",
            bunkatu: "Bunkatu",
            cmtName: "Comment Name",
            cmtOpt: "Comment Option",
            fontColor: "Black",
            commentNewline: 1,
            masterSbt: "MasterSbt",
            inOutKbn: 1,
            yakka: 10.0,
            isGetPriceInYakka: true,
            refillSetting: 1,
            cmtCol1: 1,
            ten: 5.5,
            bunkatuKoui: 1,
            alternationIndex: 1,
            kensaGaichu: 1,
            odrTermVal: 5.5,
            cnvTermVal: 5.5,
            yjCd: "YJ123",
            yohoSets: new List<YohoSetMstModel>(),
            kasan1: 1,
            kasan2: 1,
            cnvUnitName: "CnvUnit",
            odrUnitName: "OdrUnit",
            centerItemCd1: "CenterItemCd1",
            centerItemCd2: "CenterItemCd2",
            cmtCol2: 1,
            cmtCol3: 1,
            cmtCol4: 1,
            cmtColKeta1: 1,
            cmtColKeta2: 1,
            cmtColKeta3: 1,
            cmtColKeta4: 1,
            handanGrpKbn: 1,
            isKensaMstEmpty: true,
            odrKouiKbn: 0,
            kensaMstModel: new()
            );

            #endregion
            Assert.True(!ordInfDetail.IsDrug);
        }

        [Test]
        public void OrdInfDetailModel_009_IsDrug()
        {
            #region Data Example  
            int sinKouiKbn = 20;
            int drugKbn = 0;
            string itemCd = ItemCdConst.TouyakuChozaiNaiTon;
            OrdInfDetailModel ordInfDetail = new OrdInfDetailModel(
            hpId: 1,
            raiinNo: 123456789,
            rpNo: 987654321,
            rpEdaNo: 456789123,
            rowNo: 1,
            ptId: 123,
            sinDate: 20240320,
            sinKouiKbn: sinKouiKbn,
            itemCd: itemCd,
            itemName: "Example Item",
            suryo: 10.5,
            unitName: "Unit",
            unitSbt: 1,
            termVal: 5.5,
            kohatuKbn: 1,
            syohoKbn: 1,
            syohoLimitKbn: 1,
            drugKbn: drugKbn,
            yohoKbn: 1,
            kokuji1: "Kokuji1",
            kokuji2: "Kokuji2",
            isNodspRece: 0,
            ipnCd: "IPN123",
            ipnName: "Example IPN",
            jissiKbn: 1,
            jissiDate: DateTime.Now,
            jissiId: 123,
            jissiMachine: "Machine123",
            reqCd: "REQ123",
            bunkatu: "Bunkatu",
            cmtName: "Comment Name",
            cmtOpt: "Comment Option",
            fontColor: "Black",
            commentNewline: 1,
            masterSbt: "MasterSbt",
            inOutKbn: 1,
            yakka: 10.0,
            isGetPriceInYakka: true,
            refillSetting: 1,
            cmtCol1: 1,
            ten: 5.5,
            bunkatuKoui: 1,
            alternationIndex: 1,
            kensaGaichu: 1,
            odrTermVal: 5.5,
            cnvTermVal: 5.5,
            yjCd: "YJ123",
            yohoSets: new List<YohoSetMstModel>(),
            kasan1: 1,
            kasan2: 1,
            cnvUnitName: "CnvUnit",
            odrUnitName: "OdrUnit",
            centerItemCd1: "CenterItemCd1",
            centerItemCd2: "CenterItemCd2",
            cmtCol2: 1,
            cmtCol3: 1,
            cmtCol4: 1,
            cmtColKeta1: 1,
            cmtColKeta2: 1,
            cmtColKeta3: 1,
            cmtColKeta4: 1,
            handanGrpKbn: 1,
            isKensaMstEmpty: true,
            odrKouiKbn: 0,
            kensaMstModel: new()
            );

            #endregion
            Assert.True(ordInfDetail.IsDrug);
        }

        [Test]
        public void OrdInfDetailModel_010_IsDrug()
        {
            #region Data Example  
            int sinKouiKbn = 20;
            int drugKbn = 0;
            string itemCd = ItemCdConst.TouyakuChozaiGai;
            OrdInfDetailModel ordInfDetail = new OrdInfDetailModel(
            hpId: 1,
            raiinNo: 123456789,
            rpNo: 987654321,
            rpEdaNo: 456789123,
            rowNo: 1,
            ptId: 123,
            sinDate: 20240320,
            sinKouiKbn: sinKouiKbn,
            itemCd: itemCd,
            itemName: "Example Item",
            suryo: 10.5,
            unitName: "Unit",
            unitSbt: 1,
            termVal: 5.5,
            kohatuKbn: 1,
            syohoKbn: 1,
            syohoLimitKbn: 1,
            drugKbn: drugKbn,
            yohoKbn: 1,
            kokuji1: "Kokuji1",
            kokuji2: "Kokuji2",
            isNodspRece: 0,
            ipnCd: "IPN123",
            ipnName: "Example IPN",
            jissiKbn: 1,
            jissiDate: DateTime.Now,
            jissiId: 123,
            jissiMachine: "Machine123",
            reqCd: "REQ123",
            bunkatu: "Bunkatu",
            cmtName: "Comment Name",
            cmtOpt: "Comment Option",
            fontColor: "Black",
            commentNewline: 1,
            masterSbt: "MasterSbt",
            inOutKbn: 1,
            yakka: 10.0,
            isGetPriceInYakka: true,
            refillSetting: 1,
            cmtCol1: 1,
            ten: 5.5,
            bunkatuKoui: 1,
            alternationIndex: 1,
            kensaGaichu: 1,
            odrTermVal: 5.5,
            cnvTermVal: 5.5,
            yjCd: "YJ123",
            yohoSets: new List<YohoSetMstModel>(),
            kasan1: 1,
            kasan2: 1,
            cnvUnitName: "CnvUnit",
            odrUnitName: "OdrUnit",
            centerItemCd1: "CenterItemCd1",
            centerItemCd2: "CenterItemCd2",
            cmtCol2: 1,
            cmtCol3: 1,
            cmtCol4: 1,
            cmtColKeta1: 1,
            cmtColKeta2: 1,
            cmtColKeta3: 1,
            cmtColKeta4: 1,
            handanGrpKbn: 1,
            isKensaMstEmpty: true,
            odrKouiKbn: 0,
            kensaMstModel: new()
            );

            #endregion
            Assert.True(ordInfDetail.IsDrug);
        }

        [Test]
        public void OrdInfDetailModel_011_IsDrug()
        {
            #region Data Example  
            int sinKouiKbn = 20;
            int drugKbn = 0;
            string itemCd = "Zalo";
            OrdInfDetailModel ordInfDetail = new OrdInfDetailModel(
            hpId: 1,
            raiinNo: 123456789,
            rpNo: 987654321,
            rpEdaNo: 456789123,
            rowNo: 1,
            ptId: 123,
            sinDate: 20240320,
            sinKouiKbn: sinKouiKbn,
            itemCd: itemCd,
            itemName: "Example Item",
            suryo: 10.5,
            unitName: "Unit",
            unitSbt: 1,
            termVal: 5.5,
            kohatuKbn: 1,
            syohoKbn: 1,
            syohoLimitKbn: 1,
            drugKbn: drugKbn,
            yohoKbn: 1,
            kokuji1: "Kokuji1",
            kokuji2: "Kokuji2",
            isNodspRece: 0,
            ipnCd: "IPN123",
            ipnName: "Example IPN",
            jissiKbn: 1,
            jissiDate: DateTime.Now,
            jissiId: 123,
            jissiMachine: "Machine123",
            reqCd: "REQ123",
            bunkatu: "Bunkatu",
            cmtName: "Comment Name",
            cmtOpt: "Comment Option",
            fontColor: "Black",
            commentNewline: 1,
            masterSbt: "MasterSbt",
            inOutKbn: 1,
            yakka: 10.0,
            isGetPriceInYakka: true,
            refillSetting: 1,
            cmtCol1: 1,
            ten: 5.5,
            bunkatuKoui: 1,
            alternationIndex: 1,
            kensaGaichu: 1,
            odrTermVal: 5.5,
            cnvTermVal: 5.5,
            yjCd: "YJ123",
            yohoSets: new List<YohoSetMstModel>(),
            kasan1: 1,
            kasan2: 1,
            cnvUnitName: "CnvUnit",
            odrUnitName: "OdrUnit",
            centerItemCd1: "CenterItemCd1",
            centerItemCd2: "CenterItemCd2",
            cmtCol2: 1,
            cmtCol3: 1,
            cmtCol4: 1,
            cmtColKeta1: 1,
            cmtColKeta2: 1,
            cmtColKeta3: 1,
            cmtColKeta4: 1,
            handanGrpKbn: 1,
            isKensaMstEmpty: true,
            odrKouiKbn: 0,
            kensaMstModel: new()
            );

            #endregion
            Assert.True(ordInfDetail.IsDrug);
        }

        [Test]
        public void OrdInfDetailModel_012_IsDrug()
        {
            #region Data Example  
            int sinKouiKbn = 19;
            int drugKbn = 0;
            string itemCd = "Zalo";
            OrdInfDetailModel ordInfDetail = new OrdInfDetailModel(
            hpId: 1,
            raiinNo: 123456789,
            rpNo: 987654321,
            rpEdaNo: 456789123,
            rowNo: 1,
            ptId: 123,
            sinDate: 20240320,
            sinKouiKbn: sinKouiKbn,
            itemCd: itemCd,
            itemName: "Example Item",
            suryo: 10.5,
            unitName: "Unit",
            unitSbt: 1,
            termVal: 5.5,
            kohatuKbn: 1,
            syohoKbn: 1,
            syohoLimitKbn: 1,
            drugKbn: drugKbn,
            yohoKbn: 1,
            kokuji1: "Kokuji1",
            kokuji2: "Kokuji2",
            isNodspRece: 0,
            ipnCd: "IPN123",
            ipnName: "Example IPN",
            jissiKbn: 1,
            jissiDate: DateTime.Now,
            jissiId: 123,
            jissiMachine: "Machine123",
            reqCd: "REQ123",
            bunkatu: "Bunkatu",
            cmtName: "Comment Name",
            cmtOpt: "Comment Option",
            fontColor: "Black",
            commentNewline: 1,
            masterSbt: "MasterSbt",
            inOutKbn: 1,
            yakka: 10.0,
            isGetPriceInYakka: true,
            refillSetting: 1,
            cmtCol1: 1,
            ten: 5.5,
            bunkatuKoui: 1,
            alternationIndex: 1,
            kensaGaichu: 1,
            odrTermVal: 5.5,
            cnvTermVal: 5.5,
            yjCd: "YJ123",
            yohoSets: new List<YohoSetMstModel>(),
            kasan1: 1,
            kasan2: 1,
            cnvUnitName: "CnvUnit",
            odrUnitName: "OdrUnit",
            centerItemCd1: "CenterItemCd1",
            centerItemCd2: "CenterItemCd2",
            cmtCol2: 1,
            cmtCol3: 1,
            cmtCol4: 1,
            cmtColKeta1: 1,
            cmtColKeta2: 1,
            cmtColKeta3: 1,
            cmtColKeta4: 1,
            handanGrpKbn: 1,
            isKensaMstEmpty: true,
            odrKouiKbn: 0,
            kensaMstModel: new()
            );

            #endregion
            Assert.True(!ordInfDetail.IsDrug);
        }

        [Test]
        public void OrdInfDetailModel_013_IsInjection()
        {
            #region Data Example  
            int sinKouiKbn = 30;
            string masterSbt = "A";
            OrdInfDetailModel ordInfDetail = new OrdInfDetailModel(
            hpId: 1,
            raiinNo: 123456789,
            rpNo: 987654321,
            rpEdaNo: 456789123,
            rowNo: 1,
            ptId: 123,
            sinDate: 20240320,
            sinKouiKbn: sinKouiKbn,
            itemCd: "test",
            itemName: "Example Item",
            suryo: 10.5,
            unitName: "Unit",
            unitSbt: 1,
            termVal: 5.5,
            kohatuKbn: 1,
            syohoKbn: 1,
            syohoLimitKbn: 1,
            drugKbn: 0,
            yohoKbn: 1,
            kokuji1: "Kokuji1",
            kokuji2: "Kokuji2",
            isNodspRece: 0,
            ipnCd: "IPN123",
            ipnName: "Example IPN",
            jissiKbn: 1,
            jissiDate: DateTime.Now,
            jissiId: 123,
            jissiMachine: "Machine123",
            reqCd: "REQ123",
            bunkatu: "Bunkatu",
            cmtName: "Comment Name",
            cmtOpt: "Comment Option",
            fontColor: "Black",
            commentNewline: 1,
            masterSbt: masterSbt,
            inOutKbn: 1,
            yakka: 10.0,
            isGetPriceInYakka: true,
            refillSetting: 1,
            cmtCol1: 1,
            ten: 5.5,
            bunkatuKoui: 1,
            alternationIndex: 1,
            kensaGaichu: 1,
            odrTermVal: 5.5,
            cnvTermVal: 5.5,
            yjCd: "YJ123",
            yohoSets: new List<YohoSetMstModel>(),
            kasan1: 1,
            kasan2: 1,
            cnvUnitName: "CnvUnit",
            odrUnitName: "OdrUnit",
            centerItemCd1: "CenterItemCd1",
            centerItemCd2: "CenterItemCd2",
            cmtCol2: 1,
            cmtCol3: 1,
            cmtCol4: 1,
            cmtColKeta1: 1,
            cmtColKeta2: 1,
            cmtColKeta3: 1,
            cmtColKeta4: 1,
            handanGrpKbn: 1,
            isKensaMstEmpty: true,
            odrKouiKbn: 0,
            kensaMstModel: new()
            );

            #endregion
            Assert.True(ordInfDetail.IsInjection);
        }

        [Test]
        public void OrdInfDetailModel_014_IsInjection()
        {
            #region Data Example  
            int sinKouiKbn = 30;
            string masterSbt = "S";
            OrdInfDetailModel ordInfDetail = new OrdInfDetailModel(
            hpId: 1,
            raiinNo: 123456789,
            rpNo: 987654321,
            rpEdaNo: 456789123,
            rowNo: 1,
            ptId: 123,
            sinDate: 20240320,
            sinKouiKbn: sinKouiKbn,
            itemCd: "test",
            itemName: "Example Item",
            suryo: 10.5,
            unitName: "Unit",
            unitSbt: 1,
            termVal: 5.5,
            kohatuKbn: 1,
            syohoKbn: 1,
            syohoLimitKbn: 1,
            drugKbn: 0,
            yohoKbn: 1,
            kokuji1: "Kokuji1",
            kokuji2: "Kokuji2",
            isNodspRece: 0,
            ipnCd: "IPN123",
            ipnName: "Example IPN",
            jissiKbn: 1,
            jissiDate: DateTime.Now,
            jissiId: 123,
            jissiMachine: "Machine123",
            reqCd: "REQ123",
            bunkatu: "Bunkatu",
            cmtName: "Comment Name",
            cmtOpt: "Comment Option",
            fontColor: "Black",
            commentNewline: 1,
            masterSbt: masterSbt,
            inOutKbn: 1,
            yakka: 10.0,
            isGetPriceInYakka: true,
            refillSetting: 1,
            cmtCol1: 1,
            ten: 5.5,
            bunkatuKoui: 1,
            alternationIndex: 1,
            kensaGaichu: 1,
            odrTermVal: 5.5,
            cnvTermVal: 5.5,
            yjCd: "YJ123",
            yohoSets: new List<YohoSetMstModel>(),
            kasan1: 1,
            kasan2: 1,
            cnvUnitName: "CnvUnit",
            odrUnitName: "OdrUnit",
            centerItemCd1: "CenterItemCd1",
            centerItemCd2: "CenterItemCd2",
            cmtCol2: 1,
            cmtCol3: 1,
            cmtCol4: 1,
            cmtColKeta1: 1,
            cmtColKeta2: 1,
            cmtColKeta3: 1,
            cmtColKeta4: 1,
            handanGrpKbn: 1,
            isKensaMstEmpty: true,
            odrKouiKbn: 0,
            kensaMstModel: new()
            );

            #endregion
            Assert.True(!ordInfDetail.IsInjection);
        }

        [Test]
        public void OrdInfDetailModel_015_IsInjection()
        {

            List<int> sinKouiKbn = new List<int>() { 29, 31 };
            string masterSbt = "A";
            foreach (int i in sinKouiKbn)
            {
                #region Data Example
                OrdInfDetailModel ordInfDetail = new OrdInfDetailModel(
                hpId: 1,
                raiinNo: 123456789,
                rpNo: 987654321,
                rpEdaNo: 456789123,
                rowNo: 1,
                ptId: 123,
                sinDate: 20240320,
                sinKouiKbn: i,
                itemCd: "test",
                itemName: "Example Item",
                suryo: 10.5,
                unitName: "Unit",
                unitSbt: 1,
                termVal: 5.5,
                kohatuKbn: 1,
                syohoKbn: 1,
                syohoLimitKbn: 1,
                drugKbn: 0,
                yohoKbn: 1,
                kokuji1: "Kokuji1",
                kokuji2: "Kokuji2",
                isNodspRece: 0,
                ipnCd: "IPN123",
                ipnName: "Example IPN",
                jissiKbn: 1,
                jissiDate: DateTime.Now,
                jissiId: 123,
                jissiMachine: "Machine123",
                reqCd: "REQ123",
                bunkatu: "Bunkatu",
                cmtName: "Comment Name",
                cmtOpt: "Comment Option",
                fontColor: "Black",
                commentNewline: 1,
                masterSbt: masterSbt,
                inOutKbn: 1,
                yakka: 10.0,
                isGetPriceInYakka: true,
                refillSetting: 1,
                cmtCol1: 1,
                ten: 5.5,
                bunkatuKoui: 1,
                alternationIndex: 1,
                kensaGaichu: 1,
                odrTermVal: 5.5,
                cnvTermVal: 5.5,
                yjCd: "YJ123",
                yohoSets: new List<YohoSetMstModel>(),
                kasan1: 1,
                kasan2: 1,
                cnvUnitName: "CnvUnit",
                odrUnitName: "OdrUnit",
                centerItemCd1: "CenterItemCd1",
                centerItemCd2: "CenterItemCd2",
                cmtCol2: 1,
                cmtCol3: 1,
                cmtCol4: 1,
                cmtColKeta1: 1,
                cmtColKeta2: 1,
                cmtColKeta3: 1,
                cmtColKeta4: 1,
                handanGrpKbn: 1,
                isKensaMstEmpty: true,
                odrKouiKbn: 0,
                kensaMstModel: new()
                );
                #endregion
                Assert.True(!ordInfDetail.IsInjection);
            }

        }

        [Test]
        public void OrdInfDetailModel_016_IsStandardUsage()
        {
            #region Data Example  
            int yohoKbn = 1;
            string itemCd = "test";
            OrdInfDetailModel ordInfDetail = new OrdInfDetailModel(
            hpId: 1,
            raiinNo: 123456789,
            rpNo: 987654321,
            rpEdaNo: 456789123,
            rowNo: 1,
            ptId: 123,
            sinDate: 20240320,
            sinKouiKbn: 1,
            itemCd: itemCd,
            itemName: "Example Item",
            suryo: 10.5,
            unitName: "Unit",
            unitSbt: 1,
            termVal: 5.5,
            kohatuKbn: 1,
            syohoKbn: 1,
            syohoLimitKbn: 1,
            drugKbn: 0,
            yohoKbn: yohoKbn,
            kokuji1: "Kokuji1",
            kokuji2: "Kokuji2",
            isNodspRece: 0,
            ipnCd: "IPN123",
            ipnName: "Example IPN",
            jissiKbn: 1,
            jissiDate: DateTime.Now,
            jissiId: 123,
            jissiMachine: "Machine123",
            reqCd: "REQ123",
            bunkatu: "Bunkatu",
            cmtName: "Comment Name",
            cmtOpt: "Comment Option",
            fontColor: "Black",
            commentNewline: 1,
            masterSbt: "MasterSbt",
            inOutKbn: 1,
            yakka: 10.0,
            isGetPriceInYakka: true,
            refillSetting: 1,
            cmtCol1: 1,
            ten: 5.5,
            bunkatuKoui: 1,
            alternationIndex: 1,
            kensaGaichu: 1,
            odrTermVal: 5.5,
            cnvTermVal: 5.5,
            yjCd: "YJ123",
            yohoSets: new List<YohoSetMstModel>(),
            kasan1: 1,
            kasan2: 1,
            cnvUnitName: "CnvUnit",
            odrUnitName: "OdrUnit",
            centerItemCd1: "CenterItemCd1",
            centerItemCd2: "CenterItemCd2",
            cmtCol2: 1,
            cmtCol3: 1,
            cmtCol4: 1,
            cmtColKeta1: 1,
            cmtColKeta2: 1,
            cmtColKeta3: 1,
            cmtColKeta4: 1,
            handanGrpKbn: 1,
            isKensaMstEmpty: true,
            odrKouiKbn: 0,
            kensaMstModel: new()
            );

            #endregion
            Assert.True(ordInfDetail.IsStandardUsage);
        }

        [Test]
        public void OrdInfDetailModel_017_IsStandardUsage()
        {
            #region Data Example  
            int yohoKbn = 0;
            string itemCd = ItemCdConst.TouyakuChozaiNaiTon;
            OrdInfDetailModel ordInfDetail = new OrdInfDetailModel(
            hpId: 1,
            raiinNo: 123456789,
            rpNo: 987654321,
            rpEdaNo: 456789123,
            rowNo: 1,
            ptId: 123,
            sinDate: 20240320,
            sinKouiKbn: 1,
            itemCd: itemCd,
            itemName: "Example Item",
            suryo: 10.5,
            unitName: "Unit",
            unitSbt: 1,
            termVal: 5.5,
            kohatuKbn: 1,
            syohoKbn: 1,
            syohoLimitKbn: 1,
            drugKbn: 0,
            yohoKbn: yohoKbn,
            kokuji1: "Kokuji1",
            kokuji2: "Kokuji2",
            isNodspRece: 0,
            ipnCd: "IPN123",
            ipnName: "Example IPN",
            jissiKbn: 1,
            jissiDate: DateTime.Now,
            jissiId: 123,
            jissiMachine: "Machine123",
            reqCd: "REQ123",
            bunkatu: "Bunkatu",
            cmtName: "Comment Name",
            cmtOpt: "Comment Option",
            fontColor: "Black",
            commentNewline: 1,
            masterSbt: "MasterSbt",
            inOutKbn: 1,
            yakka: 10.0,
            isGetPriceInYakka: true,
            refillSetting: 1,
            cmtCol1: 1,
            ten: 5.5,
            bunkatuKoui: 1,
            alternationIndex: 1,
            kensaGaichu: 1,
            odrTermVal: 5.5,
            cnvTermVal: 5.5,
            yjCd: "YJ123",
            yohoSets: new List<YohoSetMstModel>(),
            kasan1: 1,
            kasan2: 1,
            cnvUnitName: "CnvUnit",
            odrUnitName: "OdrUnit",
            centerItemCd1: "CenterItemCd1",
            centerItemCd2: "CenterItemCd2",
            cmtCol2: 1,
            cmtCol3: 1,
            cmtCol4: 1,
            cmtColKeta1: 1,
            cmtColKeta2: 1,
            cmtColKeta3: 1,
            cmtColKeta4: 1,
            handanGrpKbn: 1,
            isKensaMstEmpty: true,
            odrKouiKbn: 0,
            kensaMstModel: new()
            );

            #endregion
            Assert.True(ordInfDetail.IsStandardUsage);
        }

        [Test]
        public void OrdInfDetailModel_018_IsStandardUsage()
        {
            #region Data Example  
            int yohoKbn = 0;
            string itemCd = ItemCdConst.TouyakuChozaiGai;
            OrdInfDetailModel ordInfDetail = new OrdInfDetailModel(
            hpId: 1,
            raiinNo: 123456789,
            rpNo: 987654321,
            rpEdaNo: 456789123,
            rowNo: 1,
            ptId: 123,
            sinDate: 20240320,
            sinKouiKbn: 1,
            itemCd: itemCd,
            itemName: "Example Item",
            suryo: 10.5,
            unitName: "Unit",
            unitSbt: 1,
            termVal: 5.5,
            kohatuKbn: 1,
            syohoKbn: 1,
            syohoLimitKbn: 1,
            drugKbn: 0,
            yohoKbn: yohoKbn,
            kokuji1: "Kokuji1",
            kokuji2: "Kokuji2",
            isNodspRece: 0,
            ipnCd: "IPN123",
            ipnName: "Example IPN",
            jissiKbn: 1,
            jissiDate: DateTime.Now,
            jissiId: 123,
            jissiMachine: "Machine123",
            reqCd: "REQ123",
            bunkatu: "Bunkatu",
            cmtName: "Comment Name",
            cmtOpt: "Comment Option",
            fontColor: "Black",
            commentNewline: 1,
            masterSbt: "MasterSbt",
            inOutKbn: 1,
            yakka: 10.0,
            isGetPriceInYakka: true,
            refillSetting: 1,
            cmtCol1: 1,
            ten: 5.5,
            bunkatuKoui: 1,
            alternationIndex: 1,
            kensaGaichu: 1,
            odrTermVal: 5.5,
            cnvTermVal: 5.5,
            yjCd: "YJ123",
            yohoSets: new List<YohoSetMstModel>(),
            kasan1: 1,
            kasan2: 1,
            cnvUnitName: "CnvUnit",
            odrUnitName: "OdrUnit",
            centerItemCd1: "CenterItemCd1",
            centerItemCd2: "CenterItemCd2",
            cmtCol2: 1,
            cmtCol3: 1,
            cmtCol4: 1,
            cmtColKeta1: 1,
            cmtColKeta2: 1,
            cmtColKeta3: 1,
            cmtColKeta4: 1,
            handanGrpKbn: 1,
            isKensaMstEmpty: true,
            odrKouiKbn: 0,
            kensaMstModel: new()
            );

            #endregion
            Assert.True(ordInfDetail.IsStandardUsage);
        }

        [Test]
        public void OrdInfDetailModel_019_IsStandardUsage()
        {
            List<int> yohoKbn = new List<int>() { 0, 2 };
            string itemCd = "test";
            foreach (int i in yohoKbn)
            {
                #region Data Example
                OrdInfDetailModel ordInfDetail = new OrdInfDetailModel(
                hpId: 1,
                raiinNo: 123456789,
                rpNo: 987654321,
                rpEdaNo: 456789123,
                rowNo: 1,
                ptId: 123,
                sinDate: 20240320,
                sinKouiKbn: 1,
                itemCd: itemCd,
                itemName: "Example Item",
                suryo: 10.5,
                unitName: "Unit",
                unitSbt: 1,
                termVal: 5.5,
                kohatuKbn: 1,
                syohoKbn: 1,
                syohoLimitKbn: 1,
                drugKbn: 0,
                yohoKbn: i,
                kokuji1: "Kokuji1",
                kokuji2: "Kokuji2",
                isNodspRece: 0,
                ipnCd: "IPN123",
                ipnName: "Example IPN",
                jissiKbn: 1,
                jissiDate: DateTime.Now,
                jissiId: 123,
                jissiMachine: "Machine123",
                reqCd: "REQ123",
                bunkatu: "Bunkatu",
                cmtName: "Comment Name",
                cmtOpt: "Comment Option",
                fontColor: "Black",
                commentNewline: 1,
                masterSbt: "MasterSbt",
                inOutKbn: 1,
                yakka: 10.0,
                isGetPriceInYakka: true,
                refillSetting: 1,
                cmtCol1: 1,
                ten: 5.5,
                bunkatuKoui: 1,
                alternationIndex: 1,
                kensaGaichu: 1,
                odrTermVal: 5.5,
                cnvTermVal: 5.5,
                yjCd: "YJ123",
                yohoSets: new List<YohoSetMstModel>(),
                kasan1: 1,
                kasan2: 1,
                cnvUnitName: "CnvUnit",
                odrUnitName: "OdrUnit",
                centerItemCd1: "CenterItemCd1",
                centerItemCd2: "CenterItemCd2",
                cmtCol2: 1,
                cmtCol3: 1,
                cmtCol4: 1,
                cmtColKeta1: 1,
                cmtColKeta2: 1,
                cmtColKeta3: 1,
                cmtColKeta4: 1,
                handanGrpKbn: 1,
                isKensaMstEmpty: true,
                odrKouiKbn: 0,
                kensaMstModel: new()
                );
                #endregion
                Assert.True(!ordInfDetail.IsStandardUsage);
            }
        }

        [Test]
        public void OrdInfDetailModel_020_IsInjectionUsage()
        {

            List<int> sinKouiKbn = new List<int>() { 31, 32, 33, 34 };
            string masterSbt = string.Empty;
            string itemCd = ItemCdConst.TouyakuChozaiGai;
            foreach (int i in sinKouiKbn)
            {
                #region Data Example  
                OrdInfDetailModel ordInfDetail = new OrdInfDetailModel(
                hpId: 1,
                raiinNo: 123456789,
                rpNo: 987654321,
                rpEdaNo: 456789123,
                rowNo: 1,
                ptId: 123,
                sinDate: 20240320,
                sinKouiKbn: i,
                itemCd: itemCd,
                itemName: "Example Item",
                suryo: 10.5,
                unitName: "Unit",
                unitSbt: 1,
                termVal: 5.5,
                kohatuKbn: 1,
                syohoKbn: 1,
                syohoLimitKbn: 1,
                drugKbn: 0,
                yohoKbn: 0,
                kokuji1: "Kokuji1",
                kokuji2: "Kokuji2",
                isNodspRece: 0,
                ipnCd: "IPN123",
                ipnName: "Example IPN",
                jissiKbn: 1,
                jissiDate: DateTime.Now,
                jissiId: 123,
                jissiMachine: "Machine123",
                reqCd: "REQ123",
                bunkatu: "Bunkatu",
                cmtName: "Comment Name",
                cmtOpt: "Comment Option",
                fontColor: "Black",
                commentNewline: 1,
                masterSbt: masterSbt,
                inOutKbn: 1,
                yakka: 10.0,
                isGetPriceInYakka: true,
                refillSetting: 1,
                cmtCol1: 1,
                ten: 5.5,
                bunkatuKoui: 1,
                alternationIndex: 1,
                kensaGaichu: 1,
                odrTermVal: 5.5,
                cnvTermVal: 5.5,
                yjCd: "YJ123",
                yohoSets: new List<YohoSetMstModel>(),
                kasan1: 1,
                kasan2: 1,
                cnvUnitName: "CnvUnit",
                odrUnitName: "OdrUnit",
                centerItemCd1: "CenterItemCd1",
                centerItemCd2: "CenterItemCd2",
                cmtCol2: 1,
                cmtCol3: 1,
                cmtCol4: 1,
                cmtColKeta1: 1,
                cmtColKeta2: 1,
                cmtColKeta3: 1,
                cmtColKeta4: 1,
                handanGrpKbn: 1,
                isKensaMstEmpty: true,
                odrKouiKbn: 0,
                kensaMstModel: new()
                );
                #endregion
                Assert.True(ordInfDetail.IsInjectionUsage);
            }
        }

        [Test]
        public void OrdInfDetailModel_021_IsInjectionUsage()
        {

            int sinKouiKbn = 30;
            string masterSbt = "S";
            string itemCd = "Zalo";
            #region Data Example  
            OrdInfDetailModel ordInfDetail = new OrdInfDetailModel(
            hpId: 1,
            raiinNo: 123456789,
            rpNo: 987654321,
            rpEdaNo: 456789123,
            rowNo: 1,
            ptId: 123,
            sinDate: 20240320,
            sinKouiKbn: sinKouiKbn,
            itemCd: itemCd,
            itemName: "Example Item",
            suryo: 10.5,
            unitName: "Unit",
            unitSbt: 1,
            termVal: 5.5,
            kohatuKbn: 1,
            syohoKbn: 1,
            syohoLimitKbn: 1,
            drugKbn: 0,
            yohoKbn: 0,
            kokuji1: "Kokuji1",
            kokuji2: "Kokuji2",
            isNodspRece: 0,
            ipnCd: "IPN123",
            ipnName: "Example IPN",
            jissiKbn: 1,
            jissiDate: DateTime.Now,
            jissiId: 123,
            jissiMachine: "Machine123",
            reqCd: "REQ123",
            bunkatu: "Bunkatu",
            cmtName: "Comment Name",
            cmtOpt: "Comment Option",
            fontColor: "Black",
            commentNewline: 1,
            masterSbt: masterSbt,
            inOutKbn: 1,
            yakka: 10.0,
            isGetPriceInYakka: true,
            refillSetting: 1,
            cmtCol1: 1,
            ten: 5.5,
            bunkatuKoui: 1,
            alternationIndex: 1,
            kensaGaichu: 1,
            odrTermVal: 5.5,
            cnvTermVal: 5.5,
            yjCd: "YJ123",
            yohoSets: new List<YohoSetMstModel>(),
            kasan1: 1,
            kasan2: 1,
            cnvUnitName: "CnvUnit",
            odrUnitName: "OdrUnit",
            centerItemCd1: "CenterItemCd1",
            centerItemCd2: "CenterItemCd2",
            cmtCol2: 1,
            cmtCol3: 1,
            cmtCol4: 1,
            cmtColKeta1: 1,
            cmtColKeta2: 1,
            cmtColKeta3: 1,
            cmtColKeta4: 1,
            handanGrpKbn: 1,
            isKensaMstEmpty: true,
            odrKouiKbn: 0,
            kensaMstModel: new()
            );
            #endregion
            Assert.True(ordInfDetail.IsInjectionUsage);
        }

        [Test]
        public void OrdInfDetailModel_022_IsInjectionUsage()
        {

            int sinKouiKbn = 30;
            string masterSbt = "S";
            string itemCd = "test";
            #region Data Example  
            OrdInfDetailModel ordInfDetail = new OrdInfDetailModel(
            hpId: 1,
            raiinNo: 123456789,
            rpNo: 987654321,
            rpEdaNo: 456789123,
            rowNo: 1,
            ptId: 123,
            sinDate: 20240320,
            sinKouiKbn: sinKouiKbn,
            itemCd: itemCd,
            itemName: "Example Item",
            suryo: 10.5,
            unitName: "Unit",
            unitSbt: 1,
            termVal: 5.5,
            kohatuKbn: 1,
            syohoKbn: 1,
            syohoLimitKbn: 1,
            drugKbn: 0,
            yohoKbn: 0,
            kokuji1: "Kokuji1",
            kokuji2: "Kokuji2",
            isNodspRece: 0,
            ipnCd: "IPN123",
            ipnName: "Example IPN",
            jissiKbn: 1,
            jissiDate: DateTime.Now,
            jissiId: 123,
            jissiMachine: "Machine123",
            reqCd: "REQ123",
            bunkatu: "Bunkatu",
            cmtName: "Comment Name",
            cmtOpt: "Comment Option",
            fontColor: "Black",
            commentNewline: 1,
            masterSbt: masterSbt,
            inOutKbn: 1,
            yakka: 10.0,
            isGetPriceInYakka: true,
            refillSetting: 1,
            cmtCol1: 1,
            ten: 5.5,
            bunkatuKoui: 1,
            alternationIndex: 1,
            kensaGaichu: 1,
            odrTermVal: 5.5,
            cnvTermVal: 5.5,
            yjCd: "YJ123",
            yohoSets: new List<YohoSetMstModel>(),
            kasan1: 1,
            kasan2: 1,
            cnvUnitName: "CnvUnit",
            odrUnitName: "OdrUnit",
            centerItemCd1: "CenterItemCd1",
            centerItemCd2: "CenterItemCd2",
            cmtCol2: 1,
            cmtCol3: 1,
            cmtCol4: 1,
            cmtColKeta1: 1,
            cmtColKeta2: 1,
            cmtColKeta3: 1,
            cmtColKeta4: 1,
            handanGrpKbn: 1,
            isKensaMstEmpty: true,
            odrKouiKbn: 0,
            kensaMstModel: new()
            );
            #endregion
            Assert.True(!ordInfDetail.IsInjectionUsage);
        }

        [Test]
        public void OrdInfDetailModel_023_IsInjectionUsage()
        {

            int sinKouiKbn = 30;
            string masterSbt = "A";
            string itemCd = "Zalo";
            #region Data Example  
            OrdInfDetailModel ordInfDetail = new OrdInfDetailModel(
            hpId: 1,
            raiinNo: 123456789,
            rpNo: 987654321,
            rpEdaNo: 456789123,
            rowNo: 1,
            ptId: 123,
            sinDate: 20240320,
            sinKouiKbn: sinKouiKbn,
            itemCd: itemCd,
            itemName: "Example Item",
            suryo: 10.5,
            unitName: "Unit",
            unitSbt: 1,
            termVal: 5.5,
            kohatuKbn: 1,
            syohoKbn: 1,
            syohoLimitKbn: 1,
            drugKbn: 0,
            yohoKbn: 0,
            kokuji1: "Kokuji1",
            kokuji2: "Kokuji2",
            isNodspRece: 0,
            ipnCd: "IPN123",
            ipnName: "Example IPN",
            jissiKbn: 1,
            jissiDate: DateTime.Now,
            jissiId: 123,
            jissiMachine: "Machine123",
            reqCd: "REQ123",
            bunkatu: "Bunkatu",
            cmtName: "Comment Name",
            cmtOpt: "Comment Option",
            fontColor: "Black",
            commentNewline: 1,
            masterSbt: masterSbt,
            inOutKbn: 1,
            yakka: 10.0,
            isGetPriceInYakka: true,
            refillSetting: 1,
            cmtCol1: 1,
            ten: 5.5,
            bunkatuKoui: 1,
            alternationIndex: 1,
            kensaGaichu: 1,
            odrTermVal: 5.5,
            cnvTermVal: 5.5,
            yjCd: "YJ123",
            yohoSets: new List<YohoSetMstModel>(),
            kasan1: 1,
            kasan2: 1,
            cnvUnitName: "CnvUnit",
            odrUnitName: "OdrUnit",
            centerItemCd1: "CenterItemCd1",
            centerItemCd2: "CenterItemCd2",
            cmtCol2: 1,
            cmtCol3: 1,
            cmtCol4: 1,
            cmtColKeta1: 1,
            cmtColKeta2: 1,
            cmtColKeta3: 1,
            cmtColKeta4: 1,
            handanGrpKbn: 1,
            isKensaMstEmpty: true,
            odrKouiKbn: 0,
            kensaMstModel: new()
            );
            #endregion
            Assert.True(!ordInfDetail.IsInjectionUsage);
        }

        [Test]
        public void OrdInfDetailModel_024_IsInjectionUsage()
        {

            int sinKouiKbn = 29;
            string masterSbt = "S";
            string itemCd = "Zalo";
            #region Data Example  
            OrdInfDetailModel ordInfDetail = new OrdInfDetailModel(
            hpId: 1,
            raiinNo: 123456789,
            rpNo: 987654321,
            rpEdaNo: 456789123,
            rowNo: 1,
            ptId: 123,
            sinDate: 20240320,
            sinKouiKbn: sinKouiKbn,
            itemCd: itemCd,
            itemName: "Example Item",
            suryo: 10.5,
            unitName: "Unit",
            unitSbt: 1,
            termVal: 5.5,
            kohatuKbn: 1,
            syohoKbn: 1,
            syohoLimitKbn: 1,
            drugKbn: 0,
            yohoKbn: 0,
            kokuji1: "Kokuji1",
            kokuji2: "Kokuji2",
            isNodspRece: 0,
            ipnCd: "IPN123",
            ipnName: "Example IPN",
            jissiKbn: 1,
            jissiDate: DateTime.Now,
            jissiId: 123,
            jissiMachine: "Machine123",
            reqCd: "REQ123",
            bunkatu: "Bunkatu",
            cmtName: "Comment Name",
            cmtOpt: "Comment Option",
            fontColor: "Black",
            commentNewline: 1,
            masterSbt: masterSbt,
            inOutKbn: 1,
            yakka: 10.0,
            isGetPriceInYakka: true,
            refillSetting: 1,
            cmtCol1: 1,
            ten: 5.5,
            bunkatuKoui: 1,
            alternationIndex: 1,
            kensaGaichu: 1,
            odrTermVal: 5.5,
            cnvTermVal: 5.5,
            yjCd: "YJ123",
            yohoSets: new List<YohoSetMstModel>(),
            kasan1: 1,
            kasan2: 1,
            cnvUnitName: "CnvUnit",
            odrUnitName: "OdrUnit",
            centerItemCd1: "CenterItemCd1",
            centerItemCd2: "CenterItemCd2",
            cmtCol2: 1,
            cmtCol3: 1,
            cmtCol4: 1,
            cmtColKeta1: 1,
            cmtColKeta2: 1,
            cmtColKeta3: 1,
            cmtColKeta4: 1,
            handanGrpKbn: 1,
            isKensaMstEmpty: true,
            odrKouiKbn: 0,
            kensaMstModel: new()
            );
            #endregion
            Assert.True(!ordInfDetail.IsInjectionUsage);
        }

        [Test]
        public void OrdInfDetailModel_025_IsSuppUsage()
        {
            int yohoKbn = 2;
            #region Data Example  
            OrdInfDetailModel ordInfDetail = new OrdInfDetailModel(
            hpId: 1,
            raiinNo: 123456789,
            rpNo: 987654321,
            rpEdaNo: 456789123,
            rowNo: 1,
            ptId: 123,
            sinDate: 20240320,
            sinKouiKbn: 0,
            itemCd: "test",
            itemName: "Example Item",
            suryo: 10.5,
            unitName: "Unit",
            unitSbt: 1,
            termVal: 5.5,
            kohatuKbn: 1,
            syohoKbn: 1,
            syohoLimitKbn: 1,
            drugKbn: 0,
            yohoKbn: yohoKbn,
            kokuji1: "Kokuji1",
            kokuji2: "Kokuji2",
            isNodspRece: 0,
            ipnCd: "IPN123",
            ipnName: "Example IPN",
            jissiKbn: 1,
            jissiDate: DateTime.Now,
            jissiId: 123,
            jissiMachine: "Machine123",
            reqCd: "REQ123",
            bunkatu: "Bunkatu",
            cmtName: "Comment Name",
            cmtOpt: "Comment Option",
            fontColor: "Black",
            commentNewline: 1,
            masterSbt: "test",
            inOutKbn: 1,
            yakka: 10.0,
            isGetPriceInYakka: true,
            refillSetting: 1,
            cmtCol1: 1,
            ten: 5.5,
            bunkatuKoui: 1,
            alternationIndex: 1,
            kensaGaichu: 1,
            odrTermVal: 5.5,
            cnvTermVal: 5.5,
            yjCd: "YJ123",
            yohoSets: new List<YohoSetMstModel>(),
            kasan1: 1,
            kasan2: 1,
            cnvUnitName: "CnvUnit",
            odrUnitName: "OdrUnit",
            centerItemCd1: "CenterItemCd1",
            centerItemCd2: "CenterItemCd2",
            cmtCol2: 1,
            cmtCol3: 1,
            cmtCol4: 1,
            cmtColKeta1: 1,
            cmtColKeta2: 1,
            cmtColKeta3: 1,
            cmtColKeta4: 1,
            handanGrpKbn: 1,
            isKensaMstEmpty: true,
            odrKouiKbn: 0,
            kensaMstModel: new()
            );
            #endregion
            Assert.True(ordInfDetail.IsSuppUsage);
        }

        [Test]
        public void OrdInfDetailModel_026_IsSuppUsage()
        {
            List<int> yohoKbn = new List<int>() { 1, 3 };
            foreach (int i in yohoKbn)
            {
                #region Data Example  
                OrdInfDetailModel ordInfDetail = new OrdInfDetailModel(
                hpId: 1,
                raiinNo: 123456789,
                rpNo: 987654321,
                rpEdaNo: 456789123,
                rowNo: 1,
                ptId: 123,
                sinDate: 20240320,
                sinKouiKbn: 0,
                itemCd: "test",
                itemName: "Example Item",
                suryo: 10.5,
                unitName: "Unit",
                unitSbt: 1,
                termVal: 5.5,
                kohatuKbn: 1,
                syohoKbn: 1,
                syohoLimitKbn: 1,
                drugKbn: 0,
                yohoKbn: i,
                kokuji1: "Kokuji1",
                kokuji2: "Kokuji2",
                isNodspRece: 0,
                ipnCd: "IPN123",
                ipnName: "Example IPN",
                jissiKbn: 1,
                jissiDate: DateTime.Now,
                jissiId: 123,
                jissiMachine: "Machine123",
                reqCd: "REQ123",
                bunkatu: "Bunkatu",
                cmtName: "Comment Name",
                cmtOpt: "Comment Option",
                fontColor: "Black",
                commentNewline: 1,
                masterSbt: "test",
                inOutKbn: 1,
                yakka: 10.0,
                isGetPriceInYakka: true,
                refillSetting: 1,
                cmtCol1: 1,
                ten: 5.5,
                bunkatuKoui: 1,
                alternationIndex: 1,
                kensaGaichu: 1,
                odrTermVal: 5.5,
                cnvTermVal: 5.5,
                yjCd: "YJ123",
                yohoSets: new List<YohoSetMstModel>(),
                kasan1: 1,
                kasan2: 1,
                cnvUnitName: "CnvUnit",
                odrUnitName: "OdrUnit",
                centerItemCd1: "CenterItemCd1",
                centerItemCd2: "CenterItemCd2",
                cmtCol2: 1,
                cmtCol3: 1,
                cmtCol4: 1,
                cmtColKeta1: 1,
                cmtColKeta2: 1,
                cmtColKeta3: 1,
                cmtColKeta4: 1,
                handanGrpKbn: 1,
                isKensaMstEmpty: true,
                odrKouiKbn: 0,
                kensaMstModel: new()
                );
                #endregion
                Assert.True(!ordInfDetail.IsSuppUsage);
            }
        }

        [Test]
        public void OrdInfDetailModel_027_IsDrugOrInjection()
        {
            List<int> drugKbn = new List<int>() { 1, 4, 6 };
            foreach (int i in drugKbn)
            {
                #region Data Example  
                OrdInfDetailModel ordInfDetail = new OrdInfDetailModel(
                hpId: 1,
                raiinNo: 123456789,
                rpNo: 987654321,
                rpEdaNo: 456789123,
                rowNo: 1,
                ptId: 123,
                sinDate: 20240320,
                sinKouiKbn: 0,
                itemCd: "test",
                itemName: "Example Item",
                suryo: 10.5,
                unitName: "Unit",
                unitSbt: 1,
                termVal: 5.5,
                kohatuKbn: 1,
                syohoKbn: 1,
                syohoLimitKbn: 1,
                drugKbn: i,
                yohoKbn: 0,
                kokuji1: "Kokuji1",
                kokuji2: "Kokuji2",
                isNodspRece: 0,
                ipnCd: "IPN123",
                ipnName: "Example IPN",
                jissiKbn: 1,
                jissiDate: DateTime.Now,
                jissiId: 123,
                jissiMachine: "Machine123",
                reqCd: "REQ123",
                bunkatu: "Bunkatu",
                cmtName: "Comment Name",
                cmtOpt: "Comment Option",
                fontColor: "Black",
                commentNewline: 1,
                masterSbt: "test",
                inOutKbn: 1,
                yakka: 10.0,
                isGetPriceInYakka: true,
                refillSetting: 1,
                cmtCol1: 1,
                ten: 5.5,
                bunkatuKoui: 1,
                alternationIndex: 1,
                kensaGaichu: 1,
                odrTermVal: 5.5,
                cnvTermVal: 5.5,
                yjCd: "YJ123",
                yohoSets: new List<YohoSetMstModel>(),
                kasan1: 1,
                kasan2: 1,
                cnvUnitName: "CnvUnit",
                odrUnitName: "OdrUnit",
                centerItemCd1: "CenterItemCd1",
                centerItemCd2: "CenterItemCd2",
                cmtCol2: 1,
                cmtCol3: 1,
                cmtCol4: 1,
                cmtColKeta1: 1,
                cmtColKeta2: 1,
                cmtColKeta3: 1,
                cmtColKeta4: 1,
                handanGrpKbn: 1,
                isKensaMstEmpty: true,
                odrKouiKbn: 0,
                kensaMstModel: new()
                );
                #endregion
                Assert.True(ordInfDetail.IsDrugOrInjection);
            }


        }

        [Test]
        public void OrdInfDetailModel_028_IsDrugOrInjection()
        {
            List<int> drugKbn = new List<int>() { 0, 2, 3, 5, 7 };
            foreach (int i in drugKbn)
            {
                #region Data Example  
                OrdInfDetailModel ordInfDetail = new OrdInfDetailModel(
                hpId: 1,
                raiinNo: 123456789,
                rpNo: 987654321,
                rpEdaNo: 456789123,
                rowNo: 1,
                ptId: 123,
                sinDate: 20240320,
                sinKouiKbn: 0,
                itemCd: "test",
                itemName: "Example Item",
                suryo: 10.5,
                unitName: "Unit",
                unitSbt: 1,
                termVal: 5.5,
                kohatuKbn: 1,
                syohoKbn: 1,
                syohoLimitKbn: 1,
                drugKbn: i,
                yohoKbn: 0,
                kokuji1: "Kokuji1",
                kokuji2: "Kokuji2",
                isNodspRece: 0,
                ipnCd: "IPN123",
                ipnName: "Example IPN",
                jissiKbn: 1,
                jissiDate: DateTime.Now,
                jissiId: 123,
                jissiMachine: "Machine123",
                reqCd: "REQ123",
                bunkatu: "Bunkatu",
                cmtName: "Comment Name",
                cmtOpt: "Comment Option",
                fontColor: "Black",
                commentNewline: 1,
                masterSbt: "test",
                inOutKbn: 1,
                yakka: 10.0,
                isGetPriceInYakka: true,
                refillSetting: 1,
                cmtCol1: 1,
                ten: 5.5,
                bunkatuKoui: 1,
                alternationIndex: 1,
                kensaGaichu: 1,
                odrTermVal: 5.5,
                cnvTermVal: 5.5,
                yjCd: "YJ123",
                yohoSets: new List<YohoSetMstModel>(),
                kasan1: 1,
                kasan2: 1,
                cnvUnitName: "CnvUnit",
                odrUnitName: "OdrUnit",
                centerItemCd1: "CenterItemCd1",
                centerItemCd2: "CenterItemCd2",
                cmtCol2: 1,
                cmtCol3: 1,
                cmtCol4: 1,
                cmtColKeta1: 1,
                cmtColKeta2: 1,
                cmtColKeta3: 1,
                cmtColKeta4: 1,
                handanGrpKbn: 1,
                isKensaMstEmpty: true,
                odrKouiKbn: 0,
                kensaMstModel: new()
                );
                #endregion
                Assert.True(!ordInfDetail.IsDrugOrInjection);
            }


        }

        [Test]
        public void OrdInfDetailModel_029_IsShugi()
        {
            for (int sinKouiKbn = 80; sinKouiKbn <= 89; sinKouiKbn++)
            {
                #region Data Example  
                OrdInfDetailModel ordInfDetail = new OrdInfDetailModel(
                hpId: 1,
                raiinNo: 123456789,
                rpNo: 987654321,
                rpEdaNo: 456789123,
                rowNo: 1,
                ptId: 123,
                sinDate: 20240320,
                sinKouiKbn: sinKouiKbn,
                itemCd: "test",
                itemName: "Example Item",
                suryo: 10.5,
                unitName: "Unit",
                unitSbt: 1,
                termVal: 5.5,
                kohatuKbn: 1,
                syohoKbn: 1,
                syohoLimitKbn: 1,
                drugKbn: 0,
                yohoKbn: 0,
                kokuji1: "Kokuji1",
                kokuji2: "Kokuji2",
                isNodspRece: 0,
                ipnCd: "IPN123",
                ipnName: "Example IPN",
                jissiKbn: 1,
                jissiDate: DateTime.Now,
                jissiId: 123,
                jissiMachine: "Machine123",
                reqCd: "REQ123",
                bunkatu: "Bunkatu",
                cmtName: "Comment Name",
                cmtOpt: "Comment Option",
                fontColor: "Black",
                commentNewline: 1,
                masterSbt: "test",
                inOutKbn: 1,
                yakka: 10.0,
                isGetPriceInYakka: true,
                refillSetting: 1,
                cmtCol1: 1,
                ten: 5.5,
                bunkatuKoui: 1,
                alternationIndex: 1,
                kensaGaichu: 1,
                odrTermVal: 5.5,
                cnvTermVal: 5.5,
                yjCd: "YJ123",
                yohoSets: new List<YohoSetMstModel>(),
                kasan1: 1,
                kasan2: 1,
                cnvUnitName: "CnvUnit",
                odrUnitName: "OdrUnit",
                centerItemCd1: "CenterItemCd1",
                centerItemCd2: "CenterItemCd2",
                cmtCol2: 1,
                cmtCol3: 1,
                cmtCol4: 1,
                cmtColKeta1: 1,
                cmtColKeta2: 1,
                cmtColKeta3: 1,
                cmtColKeta4: 1,
                handanGrpKbn: 1,
                isKensaMstEmpty: true,
                odrKouiKbn: 0,
                kensaMstModel: new()
                );
                #endregion
                Assert.True(ordInfDetail.IsShugi);
            }
        }

        [Test]
        public void OrdInfDetailModel_030_IsShugi()
        {
            List<int> sinKouiKbn = new List<int>() { 79, 90 };
            foreach (int i in sinKouiKbn)
            {
                #region Data Example  
                OrdInfDetailModel ordInfDetail = new OrdInfDetailModel(
                hpId: 1,
                raiinNo: 123456789,
                rpNo: 987654321,
                rpEdaNo: 456789123,
                rowNo: 1,
                ptId: 123,
                sinDate: 20240320,
                sinKouiKbn: i,
                itemCd: "test",
                itemName: "Example Item",
                suryo: 10.5,
                unitName: "Unit",
                unitSbt: 1,
                termVal: 5.5,
                kohatuKbn: 1,
                syohoKbn: 1,
                syohoLimitKbn: 1,
                drugKbn: 0,
                yohoKbn: 0,
                kokuji1: "Kokuji1",
                kokuji2: "Kokuji2",
                isNodspRece: 0,
                ipnCd: "IPN123",
                ipnName: "Example IPN",
                jissiKbn: 1,
                jissiDate: DateTime.Now,
                jissiId: 123,
                jissiMachine: "Machine123",
                reqCd: "REQ123",
                bunkatu: "Bunkatu",
                cmtName: "Comment Name",
                cmtOpt: "Comment Option",
                fontColor: "Black",
                commentNewline: 1,
                masterSbt: "test",
                inOutKbn: 1,
                yakka: 10.0,
                isGetPriceInYakka: true,
                refillSetting: 1,
                cmtCol1: 1,
                ten: 5.5,
                bunkatuKoui: 1,
                alternationIndex: 1,
                kensaGaichu: 1,
                odrTermVal: 5.5,
                cnvTermVal: 5.5,
                yjCd: "YJ123",
                yohoSets: new List<YohoSetMstModel>(),
                kasan1: 1,
                kasan2: 1,
                cnvUnitName: "CnvUnit",
                odrUnitName: "OdrUnit",
                centerItemCd1: "CenterItemCd1",
                centerItemCd2: "CenterItemCd2",
                cmtCol2: 1,
                cmtCol3: 1,
                cmtCol4: 1,
                cmtColKeta1: 1,
                cmtColKeta2: 1,
                cmtColKeta3: 1,
                cmtColKeta4: 1,
                handanGrpKbn: 1,
                isKensaMstEmpty: true,
                odrKouiKbn: 0,
                kensaMstModel: new()
                );
                #endregion
                Assert.True(!ordInfDetail.IsShugi);
            }
        }
    }
}
