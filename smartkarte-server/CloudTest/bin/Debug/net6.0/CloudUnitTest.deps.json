{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"CloudUnitTest/1.0.0": {"dependencies": {"DocumentFormat.OpenXml": "2.19.0", "Infrastructure": "1.0.0", "Interactor": "1.0.0", "Microsoft.Extensions.Configuration": "7.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "7.0.0", "Microsoft.Extensions.Configuration.Json": "7.0.0", "Microsoft.Extensions.FileProviders.Physical": "7.0.0", "Microsoft.NET.Test.Sdk": "17.1.0", "Moq": "4.18.4", "NUnit": "3.13.3", "NUnit.Analyzers": "3.3.0", "NUnit3TestAdapter": "4.2.1", "PostgreDataContext": "1.0.0", "Reporting": "1.0.0", "coverlet.collector": "3.1.2"}, "runtime": {"CloudUnitTest.dll": {}}}, "AWSSDK.CloudFront/3.7.300": {"dependencies": {"AWSSDK.Core": "3.7.300.6"}, "runtime": {"lib/netcoreapp3.1/AWSSDK.CloudFront.dll": {"assemblyVersion": "3.3.4.0", "fileVersion": "3.7.300.0"}}}, "AWSSDK.CloudWatch/3.7.300": {"dependencies": {"AWSSDK.Core": "3.7.300.6"}, "runtime": {"lib/netcoreapp3.1/AWSSDK.CloudWatch.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.7.300.0"}}}, "AWSSDK.Core/3.7.300.6": {"runtime": {"lib/netcoreapp3.1/AWSSDK.Core.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.7.300.6"}}}, "AWSSDK.RDS/3.7.300.1": {"dependencies": {"AWSSDK.Core": "3.7.300.6"}, "runtime": {"lib/netcoreapp3.1/AWSSDK.RDS.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.7.300.1"}}}, "AWSSDK.Route53/3.7.300": {"dependencies": {"AWSSDK.Core": "3.7.300.6"}, "runtime": {"lib/netcoreapp3.1/AWSSDK.Route53.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.7.300.0"}}}, "AWSSDK.S3/3.7.301.1": {"dependencies": {"AWSSDK.Core": "3.7.300.6"}, "runtime": {"lib/netcoreapp3.1/AWSSDK.S3.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.7.301.1"}}}, "Castle.Core/5.1.1": {"dependencies": {"System.Diagnostics.EventLog": "6.0.0"}, "runtime": {"lib/net6.0/Castle.Core.dll": {"assemblyVersion": "*******", "fileVersion": "5.1.1.0"}}}, "Castle.Windsor/6.0.0": {"dependencies": {"Castle.Core": "5.1.1", "Microsoft.Extensions.DependencyModel": "6.0.0"}, "runtime": {"lib/net6.0/Castle.Windsor.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "coverlet.collector/3.1.2": {}, "CsvHelper/30.0.1": {"runtime": {"lib/net6.0/CsvHelper.dll": {"assemblyVersion": "30.0.0.0", "fileVersion": "30.0.1.0"}}}, "DocumentFormat.OpenXml/2.19.0": {"dependencies": {"System.IO.Packaging": "4.7.0"}, "runtime": {"lib/net6.0/DocumentFormat.OpenXml.dll": {"assemblyVersion": "2.19.0.0", "fileVersion": "2.19.0.0"}}}, "GraphQL.Client/6.0.0": {"dependencies": {"GraphQL.Client.Abstractions": "6.0.0", "GraphQL.Client.Abstractions.Websocket": "6.0.0", "System.Reactive": "5.0.0"}, "runtime": {"lib/netstandard2.0/GraphQL.Client.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "GraphQL.Client.Abstractions/6.0.0": {"dependencies": {"GraphQL.Primitives": "6.0.0"}, "runtime": {"lib/netstandard2.0/GraphQL.Client.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "GraphQL.Client.Abstractions.Websocket/6.0.0": {"dependencies": {"GraphQL.Client.Abstractions": "6.0.0"}, "runtime": {"lib/netstandard2.0/GraphQL.Client.Abstractions.Websocket.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "GraphQL.Client.Serializer.Newtonsoft/6.0.0": {"dependencies": {"GraphQL.Client.Abstractions.Websocket": "6.0.0", "Newtonsoft.Json": "13.0.2"}, "runtime": {"lib/netstandard2.0/GraphQL.Client.Serializer.Newtonsoft.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "GraphQL.Primitives/6.0.0": {"runtime": {"lib/netstandard2.0/GraphQL.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Kana.NET/1.0.6": {"runtime": {"lib/net6.0/Umayadia.Kana.dll": {"assemblyVersion": "1.0.6.0", "fileVersion": "1.0.6.0"}}}, "Konscious.Security.Cryptography.Argon2/1.3.0": {"dependencies": {"Konscious.Security.Cryptography.Blake2": "1.1.0", "System.Memory": "4.5.4"}, "runtime": {"lib/net6.0/Konscious.Security.Cryptography.Argon2.dll": {"assemblyVersion": "1.3.0.0", "fileVersion": "1.3.0.0"}}}, "Konscious.Security.Cryptography.Blake2/1.1.0": {"runtime": {"lib/net6.0/Konscious.Security.Cryptography.Blake2.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.AspNetCore.Authentication.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Authentication.Core/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Core.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Authorization/2.2.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Authorization.Policy/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Authorization": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.Policy.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Connections.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.IO.Pipelines": "5.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Connections.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Hosting.Abstractions": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Http/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.ObjectPool": "9.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Net.Http.Headers": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "7.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Http.Connections/1.1.0": {"dependencies": {"Microsoft.AspNetCore.Authorization.Policy": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Connections.Common": "1.1.0", "Microsoft.AspNetCore.Routing": "2.2.0", "Microsoft.AspNetCore.WebSockets": "2.2.0", "Newtonsoft.Json": "13.0.2", "System.Security.Principal.Windows": "4.7.0"}, "runtime": {"lib/netcoreapp2.2/Microsoft.AspNetCore.Http.Connections.dll": {"assemblyVersion": "*******", "fileVersion": "1.1.0.18316"}}}, "Microsoft.AspNetCore.Http.Connections.Common/1.1.0": {"dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "2.2.0", "Newtonsoft.Json": "13.0.2", "System.Buffers": "4.5.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Connections.Common.dll": {"assemblyVersion": "*******", "fileVersion": "1.1.0.18316"}}}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "7.0.0", "Microsoft.Net.Http.Headers": "2.2.0", "System.Buffers": "4.5.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Mvc.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.AspNetCore.Mvc.Core/2.2.5": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.2.0", "Microsoft.AspNetCore.Authorization.Policy": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.Mvc.Abstractions": "2.2.0", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "2.2.0", "Microsoft.AspNetCore.Routing": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.DependencyModel": "6.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "System.Diagnostics.DiagnosticSource": "4.5.0", "System.Threading.Tasks.Extensions": "4.5.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Core.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.5.19109"}}}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.ResponseCaching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Routing/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.ObjectPool": "9.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/netcoreapp2.2/Microsoft.AspNetCore.Routing.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Routing.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Routing.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.SignalR/1.1.0": {"dependencies": {"Microsoft.AspNetCore.Http.Connections": "1.1.0", "Microsoft.AspNetCore.SignalR.Core": "1.1.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.SignalR.dll": {"assemblyVersion": "*******", "fileVersion": "1.1.0.18316"}}}, "Microsoft.AspNetCore.SignalR.Common/1.1.0": {"dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "8.0.0", "Newtonsoft.Json": "13.0.2", "System.Buffers": "4.5.1"}, "runtime": {"lib/netcoreapp2.2/Microsoft.AspNetCore.SignalR.Common.dll": {"assemblyVersion": "*******", "fileVersion": "1.1.0.18316"}}}, "Microsoft.AspNetCore.SignalR.Core/1.1.0": {"dependencies": {"Microsoft.AspNetCore.Authorization": "2.2.0", "Microsoft.AspNetCore.SignalR.Common": "1.1.0", "Microsoft.AspNetCore.SignalR.Protocols.Json": "1.1.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "System.Reflection.Emit": "4.3.0", "System.Threading.Channels": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.SignalR.Core.dll": {"assemblyVersion": "*******", "fileVersion": "1.1.0.18316"}}}, "Microsoft.AspNetCore.SignalR.Protocols.Json/1.1.0": {"dependencies": {"Microsoft.AspNetCore.SignalR.Common": "1.1.0", "Newtonsoft.Json": "13.0.2"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.SignalR.Protocols.Json.dll": {"assemblyVersion": "*******", "fileVersion": "1.1.0.18316"}}}, "Microsoft.AspNetCore.WebSockets/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Net.WebSockets.WebSocketProtocol": "4.5.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"dependencies": {"Microsoft.Net.Http.Headers": "2.2.0", "System.Text.Encodings.Web": "7.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.CodeCoverage/17.1.0": {"runtime": {"lib/netcoreapp1.0/Microsoft.VisualStudio.CodeCoverage.Shim.dll": {"assemblyVersion": "********", "fileVersion": "17.100.221.61401"}}}, "Microsoft.CSharp/4.5.0": {}, "Microsoft.Diagnostics.NETCore.Client/0.2.510501": {"dependencies": {"Microsoft.Extensions.Logging": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Diagnostics.NETCore.Client.dll": {"assemblyVersion": "0.2.10.10501", "fileVersion": "0.2.10.10501"}}}, "Microsoft.EntityFrameworkCore/7.0.1": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "7.0.1", "Microsoft.EntityFrameworkCore.Analyzers": "7.0.1", "Microsoft.Extensions.Caching.Memory": "7.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "7.0.1.0", "fileVersion": "7.0.122.55819"}}}, "Microsoft.EntityFrameworkCore.Abstractions/7.0.1": {"runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "7.0.1.0", "fileVersion": "7.0.122.55819"}}}, "Microsoft.EntityFrameworkCore.Analyzers/7.0.1": {}, "Microsoft.EntityFrameworkCore.DynamicLinq/6.2.20": {"dependencies": {"Microsoft.EntityFrameworkCore": "7.0.1", "System.Linq.Dynamic.Core": "1.2.20"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.DynamicLinq.dll": {"assemblyVersion": "6.2.20.0", "fileVersion": "6.2.20.0"}}}, "Microsoft.EntityFrameworkCore.Relational/7.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "7.0.1", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51807"}}}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Configuration/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Configuration.FileExtensions/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "7.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Physical": "7.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Configuration.Json/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "7.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "7.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "7.0.0", "System.Text.Json": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.DependencyModel/6.0.0": {"dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "7.0.0", "System.Text.Json": "7.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.FileProviders.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.FileProviders.Physical/7.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "7.0.0", "Microsoft.Extensions.FileSystemGlobbing": "7.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.FileSystemGlobbing/7.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Hosting.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.Extensions.Logging/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.ObjectPool/9.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}}, "Microsoft.Extensions.Options/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Primitives/8.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.IdentityModel.Abstractions/6.34.0": {"runtime": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "6.34.0.41017"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.34.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "6.34.0", "System.Text.Encoding": "4.3.0", "System.Text.Encodings.Web": "7.0.0", "System.Text.Json": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "********", "fileVersion": "6.34.0.41017"}}}, "Microsoft.IdentityModel.Logging/6.34.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.34.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "********", "fileVersion": "6.34.0.41017"}}}, "Microsoft.IdentityModel.Tokens/6.34.0": {"dependencies": {"Microsoft.CSharp": "4.5.0", "Microsoft.IdentityModel.Logging": "6.34.0", "System.Security.Cryptography.Cng": "4.5.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "********", "fileVersion": "6.34.0.41017"}}}, "Microsoft.Net.Http.Headers/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0", "System.Buffers": "4.5.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Net.Http.Headers.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.NET.Test.Sdk/17.1.0": {"dependencies": {"Microsoft.CodeCoverage": "17.1.0", "Microsoft.TestPlatform.TestHost": "17.1.0"}}, "Microsoft.NETCore.Platforms/3.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.TestPlatform.ObjectModel/17.1.0": {"dependencies": {"NuGet.Frameworks": "5.11.0", "System.Reflection.Metadata": "1.6.0"}, "runtime": {"lib/netcoreapp2.1/Microsoft.TestPlatform.CoreUtilities.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/netcoreapp2.1/Microsoft.TestPlatform.PlatformAbstractions.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/netcoreapp2.1/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "resources": {"lib/netcoreapp2.1/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "cs"}, "lib/netcoreapp2.1/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "cs"}, "lib/netcoreapp2.1/de/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "de"}, "lib/netcoreapp2.1/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "de"}, "lib/netcoreapp2.1/es/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "es"}, "lib/netcoreapp2.1/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "es"}, "lib/netcoreapp2.1/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "fr"}, "lib/netcoreapp2.1/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "fr"}, "lib/netcoreapp2.1/it/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "it"}, "lib/netcoreapp2.1/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "it"}, "lib/netcoreapp2.1/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ja"}, "lib/netcoreapp2.1/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ja"}, "lib/netcoreapp2.1/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ko"}, "lib/netcoreapp2.1/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ko"}, "lib/netcoreapp2.1/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "pl"}, "lib/netcoreapp2.1/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "pl"}, "lib/netcoreapp2.1/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp2.1/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp2.1/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ru"}, "lib/netcoreapp2.1/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ru"}, "lib/netcoreapp2.1/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "tr"}, "lib/netcoreapp2.1/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "tr"}, "lib/netcoreapp2.1/zh-Hans/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp2.1/zh-Hans/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp2.1/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp2.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.TestPlatform.TestHost/17.1.0": {"dependencies": {"Microsoft.TestPlatform.ObjectModel": "17.1.0", "Newtonsoft.Json": "13.0.2"}, "runtime": {"lib/netcoreapp2.1/Microsoft.TestPlatform.CommunicationUtilities.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/netcoreapp2.1/Microsoft.TestPlatform.CrossPlatEngine.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/netcoreapp2.1/Microsoft.TestPlatform.Utilities.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/netcoreapp2.1/Microsoft.VisualStudio.TestPlatform.Common.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/netcoreapp2.1/testhost.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "resources": {"lib/netcoreapp2.1/cs/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "cs"}, "lib/netcoreapp2.1/cs/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "cs"}, "lib/netcoreapp2.1/cs/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "cs"}, "lib/netcoreapp2.1/de/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "de"}, "lib/netcoreapp2.1/de/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "de"}, "lib/netcoreapp2.1/de/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "de"}, "lib/netcoreapp2.1/es/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "es"}, "lib/netcoreapp2.1/es/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "es"}, "lib/netcoreapp2.1/es/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "es"}, "lib/netcoreapp2.1/fr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "fr"}, "lib/netcoreapp2.1/fr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "fr"}, "lib/netcoreapp2.1/fr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "fr"}, "lib/netcoreapp2.1/it/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "it"}, "lib/netcoreapp2.1/it/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "it"}, "lib/netcoreapp2.1/it/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "it"}, "lib/netcoreapp2.1/ja/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ja"}, "lib/netcoreapp2.1/ja/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ja"}, "lib/netcoreapp2.1/ja/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ja"}, "lib/netcoreapp2.1/ko/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ko"}, "lib/netcoreapp2.1/ko/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ko"}, "lib/netcoreapp2.1/ko/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ko"}, "lib/netcoreapp2.1/pl/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "pl"}, "lib/netcoreapp2.1/pl/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "pl"}, "lib/netcoreapp2.1/pl/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "pl"}, "lib/netcoreapp2.1/pt-BR/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp2.1/pt-BR/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp2.1/pt-BR/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp2.1/ru/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ru"}, "lib/netcoreapp2.1/ru/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ru"}, "lib/netcoreapp2.1/ru/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ru"}, "lib/netcoreapp2.1/tr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "tr"}, "lib/netcoreapp2.1/tr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "tr"}, "lib/netcoreapp2.1/tr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "tr"}, "lib/netcoreapp2.1/zh-Hans/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp2.1/zh-Hans/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp2.1/zh-Hans/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp2.1/zh-Hant/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp2.1/zh-Hant/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp2.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.Win32.Registry/4.7.0": {"dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}}, "Microsoft.Win32.SystemEvents/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Moq/4.18.4": {"dependencies": {"Castle.Core": "5.1.1"}, "runtime": {"lib/net6.0/Moq.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MyNihongo.KanaConverter/1.0.3": {"dependencies": {"Microsoft.Extensions.ObjectPool": "9.0.0"}, "runtime": {"lib/netstandard2.0/MyNihongo.KanaConverter.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NETStandard.Library/2.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0"}}, "Newtonsoft.Json/13.0.2": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.2.27524"}}}, "Npgsql/7.0.7": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Npgsql.dll": {"assemblyVersion": "7.0.7.0", "fileVersion": "7.0.7.0"}}}, "Npgsql.EntityFrameworkCore.PostgreSQL/7.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "7.0.1", "Microsoft.EntityFrameworkCore.Abstractions": "7.0.1", "Microsoft.EntityFrameworkCore.Relational": "7.0.0", "Npgsql": "7.0.7"}, "runtime": {"lib/net6.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NuGet.Frameworks/5.11.0": {"runtime": {"lib/netstandard2.0/NuGet.Frameworks.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "NUnit/3.13.3": {"dependencies": {"NETStandard.Library": "2.0.0"}, "runtime": {"lib/netstandard2.0/nunit.framework.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "NUnit.Analyzers/3.3.0": {}, "NUnit3TestAdapter/4.2.1": {}, "Pipelines.Sockets.Unofficial/2.2.2": {"dependencies": {"System.IO.Pipelines": "5.0.1"}, "runtime": {"lib/net5.0/Pipelines.Sockets.Unofficial.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.2.34088"}}}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-arm64/native/sni.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x64/native/sni.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x86/native/sni.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Sentry/4.13.0": {"runtime": {"lib/net6.0/Sentry.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Sentry.Profiling/4.13.0": {"dependencies": {"Microsoft.Diagnostics.NETCore.Client": "0.2.510501", "Sentry": "4.13.0"}, "runtime": {"lib/net6.0/Microsoft.Diagnostics.FastSerialization.dll": {"assemblyVersion": "3.1.15.0", "fileVersion": "3.1.15.0"}, "lib/net6.0/Microsoft.Diagnostics.Tracing.TraceEvent.dll": {"assemblyVersion": "3.1.15.0", "fileVersion": "3.1.15.0"}, "lib/net6.0/Sentry.Profiling.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "SharpCompress/0.34.2": {"dependencies": {"ZstdSharp.Port": "0.7.2"}, "runtime": {"lib/net6.0/SharpCompress.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Spire.PDF/11.3.0": {"dependencies": {"System.Drawing.Common": "6.0.0", "System.Text.Encoding.CodePages": "7.0.0"}, "runtime": {"lib/net6.0/Spire.Pdf.dll": {"assemblyVersion": "********", "fileVersion": "11.3.0.1360"}}}, "StackExchange.Redis/2.6.111": {"dependencies": {"Pipelines.Sockets.Unofficial": "2.2.2"}, "runtime": {"lib/net5.0/StackExchange.Redis.dll": {"assemblyVersion": "*******", "fileVersion": "2.6.111.64013"}}}, "System.Buffers/4.5.1": {}, "System.Data.SqlClient/4.8.6": {"dependencies": {"Microsoft.Win32.Registry": "4.7.0", "System.Security.Principal.Windows": "4.7.0", "runtime.native.System.Data.SqlClient.sni": "4.7.0"}, "runtime": {"lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.23.52603"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.23.52603"}, "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.23.52603"}}}, "System.Diagnostics.DiagnosticSource/4.5.0": {}, "System.Diagnostics.EventLog/6.0.0": {"runtime": {"lib/net6.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Diagnostics.EventLog.Messages.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}, "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Drawing.Common/6.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}, "runtime": {"lib/net6.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/System.Drawing.Common.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}, "runtimes/win/lib/net6.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.IdentityModel.Tokens.Jwt/6.34.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.34.0", "Microsoft.IdentityModel.Tokens": "6.34.0"}, "runtime": {"lib/net6.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "********", "fileVersion": "6.34.0.41017"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Packaging/4.7.0": {"runtime": {"lib/netstandard2.0/System.IO.Packaging.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.IO.Pipelines/5.0.1": {"runtime": {"lib/netcoreapp3.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.120.57516"}}}, "System.Linq.Dynamic.Core/1.2.20": {"runtime": {"lib/net6.0/System.Linq.Dynamic.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "System.Memory/4.5.4": {}, "System.Net.WebSockets.WebSocketProtocol/4.5.1": {"runtime": {"lib/netcoreapp2.1/System.Net.WebSockets.WebSocketProtocol.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26606.5"}}}, "System.Reactive/5.0.0": {"runtime": {"lib/net5.0/System.Reactive.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Metadata/1.6.0": {}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Security.AccessControl/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Security.Principal.Windows": "4.7.0"}}, "System.Security.Cryptography.Cng/4.5.0": {}, "System.Security.Principal.Windows/4.7.0": {}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.CodePages/7.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Text.Encodings.Web/7.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll": {"rid": "browser", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Text.Json/7.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "7.0.0"}, "runtime": {"lib/net6.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Threading.Channels/4.5.0": {}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.5.1": {}, "ZstdSharp.Port/0.7.2": {"runtime": {"lib/net6.0/ZstdSharp.dll": {"assemblyVersion": "0.7.2.0", "fileVersion": "0.7.2.0"}}}, "AWSSDK/1.0.0": {"dependencies": {"AWSSDK.CloudFront": "3.7.300", "AWSSDK.CloudWatch": "3.7.300", "AWSSDK.Core": "3.7.300.6", "AWSSDK.RDS": "3.7.300.1", "AWSSDK.Route53": "3.7.300", "AWSSDK.S3": "3.7.301.1", "CsvHelper": "30.0.1", "Helper": "1.0.0", "Npgsql": "7.0.7", "UseCase": "1.0.0"}, "runtime": {"AWSSDK.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "CalculateService/1.0.0": {"dependencies": {"Entity": "1.0.0", "Helper": "1.0.0", "Infrastructure": "1.0.0", "Microsoft.Extensions.Logging": "8.0.0", "PostgreDataContext": "1.0.0"}, "runtime": {"CalculateService.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "CommonChecker/1.0.0": {"dependencies": {"Helper": "1.0.0", "Infrastructure": "1.0.0", "PostgreDataContext": "1.0.0"}, "runtime": {"CommonChecker.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "Domain/1.0.0": {"dependencies": {"AWSSDK.S3": "3.7.301.1", "Entity": "1.0.0", "Helper": "1.0.0", "Microsoft.AspNetCore.Http.Features": "2.2.0"}, "runtime": {"Domain.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "Entity/1.0.0": {"dependencies": {"Helper": "1.0.0", "Microsoft.EntityFrameworkCore.Abstractions": "7.0.1"}, "runtime": {"Entity.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "ErrorCodeGenerator/1.0.0": {"dependencies": {"Newtonsoft.Json": "13.0.2"}, "runtime": {"ErrorCodeGenerator.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "EventProcessor/1.0.0": {"dependencies": {"Infrastructure": "1.0.0", "PostgreDataContext": "1.0.0"}, "runtime": {"EventProcessor.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "Helper/1.0.0": {"dependencies": {"ErrorCodeGenerator": "1.0.0", "Kana.NET": "1.0.6", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "MyNihongo.KanaConverter": "1.0.3", "StackExchange.Redis": "2.6.111", "System.Text.Encoding.CodePages": "7.0.0"}, "runtime": {"Helper.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "Infrastructure/1.0.0": {"dependencies": {"AWSSDK.S3": "3.7.301.1", "Domain": "1.0.0", "GraphQL.Client": "6.0.0", "GraphQL.Client.Serializer.Newtonsoft": "6.0.0", "Konscious.Security.Cryptography.Argon2": "1.3.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.Mvc.Abstractions": "2.2.0", "Microsoft.AspNetCore.Mvc.Core": "2.2.5", "Microsoft.EntityFrameworkCore.DynamicLinq": "6.2.20", "PostgreDataContext": "1.0.0", "Sentry": "4.13.0", "Sentry.Profiling": "4.13.0", "System.IdentityModel.Tokens.Jwt": "6.34.0"}, "runtime": {"Infrastructure.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "Interactor/1.0.0": {"dependencies": {"AWSSDK": "1.0.0", "CommonChecker": "1.0.0", "DocumentFormat.OpenXml": "2.19.0", "Domain": "1.0.0", "EventProcessor": "1.0.0", "Infrastructure": "1.0.0", "Microsoft.AspNetCore.SignalR": "1.1.0", "Newtonsoft.Json": "13.0.2", "Reporting": "1.0.0", "SharpCompress": "0.34.2", "UseCase": "1.0.0"}, "runtime": {"Interactor.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "PostgreDataContext/1.0.0": {"dependencies": {"Entity": "1.0.0", "Microsoft.EntityFrameworkCore": "7.0.1", "Npgsql.EntityFrameworkCore.PostgreSQL": "7.0.0"}, "runtime": {"PostgreDataContext.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "Reporting/1.0.0": {"dependencies": {"CalculateService": "1.0.0", "Castle.Windsor": "6.0.0", "Domain": "1.0.0", "Entity": "1.0.0", "Infrastructure": "1.0.0", "Newtonsoft.Json": "13.0.2", "PostgreDataContext": "1.0.0", "Spire.PDF": "11.3.0", "System.Data.SqlClient": "4.8.6", "System.Text.Encoding.CodePages": "7.0.0"}, "runtime": {"Reporting.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "UseCase/1.0.0": {"dependencies": {"CommonChecker": "1.0.0", "Domain": "1.0.0", "Reporting": "1.0.0"}, "runtime": {"UseCase.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "Domain.Core/*******": {"runtime": {"Domain.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "UseCase.Core/*******": {"runtime": {"UseCase.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"CloudUnitTest/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AWSSDK.CloudFront/3.7.300": {"type": "package", "serviceable": true, "sha512": "sha512-4UHNqGFRhDqh44czGZBuqhjj2t9kczx1bntws/NbSqsQ7s+niTI+ojuFuQky+5P53hSTX7X9WEQdQKYK6o/8iQ==", "path": "awssdk.cloudfront/3.7.300", "hashPath": "awssdk.cloudfront.3.7.300.nupkg.sha512"}, "AWSSDK.CloudWatch/3.7.300": {"type": "package", "serviceable": true, "sha512": "sha512-qlWTXofrnWpPrLd7PRYXKckDUf9zttWKEL1k2vO4xYGodTFOoND2peGVg/wrw3QV3IVDCBhI9+4VvPFh0qNkUw==", "path": "awssdk.cloudwatch/3.7.300", "hashPath": "awssdk.cloudwatch.3.7.300.nupkg.sha512"}, "AWSSDK.Core/3.7.300.6": {"type": "package", "serviceable": true, "sha512": "sha512-i/Z1MrzE4hMgbmBthh2KavwcEqSYaFpYFc2WBfzF/SCLWciaw5Pj7RtyF3/d1QbRhN2Rb3n0vIzI3ZQS96Hz4A==", "path": "awssdk.core/3.7.300.6", "hashPath": "awssdk.core.3.7.300.6.nupkg.sha512"}, "AWSSDK.RDS/3.7.300.1": {"type": "package", "serviceable": true, "sha512": "sha512-p8czhr0wSeCwHkolSI4A6egsmJaXcpyJ2apKrlJjJrUBpg6LkZrAjddUrbo/zYvA7D01TUEfpH0CLYRl8hmzXw==", "path": "awssdk.rds/3.7.300.1", "hashPath": "awssdk.rds.3.7.300.1.nupkg.sha512"}, "AWSSDK.Route53/3.7.300": {"type": "package", "serviceable": true, "sha512": "sha512-xzcUXpmpej6WOa0BR4UhcjbzlLYwDmavNzX3hOTtK0+H03VQoZNmeXoRh2uyO8kMJClnx2BccQ2wsnJjl5sjdQ==", "path": "awssdk.route53/3.7.300", "hashPath": "awssdk.route53.3.7.300.nupkg.sha512"}, "AWSSDK.S3/3.7.301.1": {"type": "package", "serviceable": true, "sha512": "sha512-Uv4ZaADXHc9RX5LW0hUyYCAvboYKw2tOhDrU8O9UZ4C3XaZ2SF0KbKF8mz5ejIsbjDDtPmabNsTejhyeXc6jnA==", "path": "awssdk.s3/3.7.301.1", "hashPath": "awssdk.s3.3.7.301.1.nupkg.sha512"}, "Castle.Core/5.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-rpYtIczkzGpf+EkZgDr9CClTdemhsrwA/W5hMoPjLkRFnXzH44zDLoovXeKtmxb1ykXK9aJVODSpiJml8CTw2g==", "path": "castle.core/5.1.1", "hashPath": "castle.core.5.1.1.nupkg.sha512"}, "Castle.Windsor/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yvKY5DXpqWEp7a5Xar9hihAcgVVzBftLqvf/Fo6xd10jAsZUpaAE5WwkuhiiYSA/yVaPzul8rRCvmYLkqh2Hkw==", "path": "castle.windsor/6.0.0", "hashPath": "castle.windsor.6.0.0.nupkg.sha512"}, "coverlet.collector/3.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-wuLDIDKD5XMt0A7lE31JPenT7QQwZPFkP5rRpdJeblyXZ9MGLI8rYjvm5fvAKln+2/X+4IxxQDxBtwdrqKNLZw==", "path": "coverlet.collector/3.1.2", "hashPath": "coverlet.collector.3.1.2.nupkg.sha512"}, "CsvHelper/30.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-rcZtgbWR+As4G3Vpgx0AMNmShGuQLFjkHAPIIflzrfkJCx8/AOd4m96ZRmiU1Wi39qS5UVjV0P8qdgqOo5Cwyg==", "path": "csvhelper/30.0.1", "hashPath": "csvhelper.30.0.1.nupkg.sha512"}, "DocumentFormat.OpenXml/2.19.0": {"type": "package", "serviceable": true, "sha512": "sha512-BLFqodowHZHR/A/sKU/6e+63J5ONWDdv8hEuSZOUiBfGFuVmsG9P3o+LMSq+dajNWE1q+lqVSa1H5FuhLX8/fA==", "path": "documentformat.openxml/2.19.0", "hashPath": "documentformat.openxml.2.19.0.nupkg.sha512"}, "GraphQL.Client/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-8yPNBbuVBpTptivyAlak4GZvbwbUcjeQTL4vN1HKHRuOykZ4r7l5fcLS6vpyPyLn0x8FsL31xbOIKyxbmR9rbA==", "path": "graphql.client/6.0.0", "hashPath": "graphql.client.6.0.0.nupkg.sha512"}, "GraphQL.Client.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-h7uzWFORHZ+CCjwr/ThAyXMr0DPpzEANDa4Uo54wqCQ+j7qUKwqYTgOrb1W40sqbvNaZm9v/X7It31SUw0maHA==", "path": "graphql.client.abstractions/6.0.0", "hashPath": "graphql.client.abstractions.6.0.0.nupkg.sha512"}, "GraphQL.Client.Abstractions.Websocket/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nr9bPf8gIOvLuXpqEpqr9z9jslYFJOvd0feHth3/kPqeR3uMbjF5pjiwh4jxyMcxHdr8Pb6QiXkV3hsSyt0v7A==", "path": "graphql.client.abstractions.websocket/6.0.0", "hashPath": "graphql.client.abstractions.websocket.6.0.0.nupkg.sha512"}, "GraphQL.Client.Serializer.Newtonsoft/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-6AFZjq4MZhR2e1ZZOi++NXMTq0y3Q+sT3S/UAIcLRJ7H6lQ8nxoAbzrlToui/m2j5gO9UL4Pmc+CdvM/H+t8Xg==", "path": "graphql.client.serializer.newtonsoft/6.0.0", "hashPath": "graphql.client.serializer.newtonsoft.6.0.0.nupkg.sha512"}, "GraphQL.Primitives/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yg72rrYDapfsIUrul7aF6wwNnTJBOFvuA9VdDTQpPa8AlAriHbufeXYLBcodKjfUdkCnaiggX1U/nEP08Zb5GA==", "path": "graphql.primitives/6.0.0", "hashPath": "graphql.primitives.6.0.0.nupkg.sha512"}, "Kana.NET/1.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-xc9teFuWQJ1h0YddJZxVWNTfvNPK648Wed4k8JYViNZN3txcV/5RXW/13nZsKjY/qndFSzJEta7mVr4jsVyXgg==", "path": "kana.net/1.0.6", "hashPath": "kana.net.1.0.6.nupkg.sha512"}, "Konscious.Security.Cryptography.Argon2/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/pNPlNmjx7r7CGvw0WqO8Rk5vHHyUuzAWhB/PRKLZ2cmhQXoM6TYUooJcuES9zE9D+6yTOl+4LmFV2nKtgDyFg==", "path": "konscious.security.cryptography.argon2/1.3.0", "hashPath": "konscious.security.cryptography.argon2.1.3.0.nupkg.sha512"}, "Konscious.Security.Cryptography.Blake2/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-enWOzNAlBjFcKoiBNVVLsa1ZzOciExznu+sUURE8dx322GI1LzrBxzEuZqjeOoK9oo9RubLtg96/7Y4JtUk9zA==", "path": "konscious.security.cryptography.blake2/1.1.0", "hashPath": "konscious.security.cryptography.blake2.1.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-VloMLDJMf3n/9ic5lCBOa42IBYJgyB1JhzLsL68Zqg+2bEPWfGBj/xCJy/LrKTArN0coOcZp3wyVTZlx0y9pHQ==", "path": "microsoft.aspnetcore.authentication.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Core/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-XlVJzJ5wPOYW+Y0J6Q/LVTEyfS4ssLXmt60T0SPP+D8abVhBTl+cgw2gDHlyKYIkcJg7btMVh383NDkMVqD/fg==", "path": "microsoft.aspnetcore.authentication.core/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.core.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-/L0W8H3jMYWyaeA9gBJqS/tSWBegP9aaTM0mjRhxTttBY9z4RVDRYJ2CwPAmAXIuPr3r1sOw+CS8jFVRGHRezQ==", "path": "microsoft.aspnetcore.authorization/2.2.0", "hashPath": "microsoft.aspnetcore.authorization.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization.Policy/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-aJCo6niDRKuNg2uS2WMEmhJTooQUGARhV2ENQ2tO5443zVHUo19MSgrgGo9FIrfD+4yKPF8Q+FF33WkWfPbyKw==", "path": "microsoft.aspnetcore.authorization.policy/2.2.0", "hashPath": "microsoft.aspnetcore.authorization.policy.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Connections.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Aqr/16Cu5XmGv7mLKJvXRxhhd05UJ7cTTSaUV4MZ3ynAzfgWjsAdpIU8FWuxwAjmVdmI8oOWuVDrbs+sRkhKnA==", "path": "microsoft.aspnetcore.connections.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.connections.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ubycklv+ZY7Kutdwuy1W4upWcZ6VFR8WUXU7l7B2+mvbDBBPAcfpi+E+Y5GFe+Q157YfA3C49D2GCjAZc7Mobw==", "path": "microsoft.aspnetcore.hosting.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-1PMijw8RMtuQF60SsD/JlKtVfvh4NORAhF4wjysdABhlhTrYmtgssqyncR0Stq5vqtjplZcj6kbT4LRTglt9IQ==", "path": "microsoft.aspnetcore.hosting.server.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.server.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-YogBSMotWPAS/X5967pZ+yyWPQkThxhmzAwyCHCSSldzYBkW5W5d6oPfBaPqQOnSHYTpSOSOkpZoAce0vwb6+A==", "path": "microsoft.aspnetcore.http/2.2.0", "hashPath": "microsoft.aspnetcore.http.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Connections/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZcwAM9rE5yjGC+vtiNAK0INybpKIqnvB+/rntZn2/CPtyiBAtovVrEp4UZOoC31zH5t0P78ix9gLNJzII/ODsA==", "path": "microsoft.aspnetcore.http.connections/1.1.0", "hashPath": "microsoft.aspnetcore.http.connections.1.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Connections.Common/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-mYk5QUUjyXQmlyDHWDjkLYDArt97plwe6KsDsNVhDEQ+HgZMKGjISyM6YSA7BERQNR25kXBTbIYfSy1vePGQgg==", "path": "microsoft.aspnetcore.http.connections.common/1.1.0", "hashPath": "microsoft.aspnetcore.http.connections.common.1.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-2DgZ9rWrJtuR7RYiew01nGRzuQBDaGHGmK56Rk54vsLLsCdzuFUPqbDTJCS1qJQWTbmbIQ9wGIOjpxA1t0l7/w==", "path": "microsoft.aspnetcore.http.extensions/2.2.0", "hashPath": "microsoft.aspnetcore.http.extensions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "path": "microsoft.aspnetcore.http.features/2.2.0", "hashPath": "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ET6uZpfVbGR1NjCuLaLy197cQ3qZUjzl7EG5SL4GfJH/c9KRE89MMBrQegqWsh0w1iRUB/zQaK0anAjxa/pz4g==", "path": "microsoft.aspnetcore.mvc.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Core/2.2.5": {"type": "package", "serviceable": true, "sha512": "sha512-/8sr8ixIUD57UFwUntha9bOwex7/AkZfdk1f9oNJG1Ek7p/uuKVa7fuHmYZpQOf35Oxrt+2Ku4WPwMSbNxOuWg==", "path": "microsoft.aspnetcore.mvc.core/2.2.5", "hashPath": "microsoft.aspnetcore.mvc.core.2.2.5.nupkg.sha512"}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-CIHWEKrHzZfFp7t57UXsueiSA/raku56TgRYauV/W1+KAQq6vevz60zjEKaazt3BI76zwMz3B4jGWnCwd8kwQw==", "path": "microsoft.aspnetcore.responsecaching.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.responsecaching.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Routing/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-jAhDBy0wryOnMhhZTtT9z63gJbvCzFuLm8yC6pHzuVu9ZD1dzg0ltxIwT4cfwuNkIL/TixdKsm3vpVOpG8euWQ==", "path": "microsoft.aspnetcore.routing/2.2.0", "hashPath": "microsoft.aspnetcore.routing.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Routing.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-lRRaPN7jDlUCVCp9i0W+PB0trFaKB0bgMJD7hEJS9Uo4R9MXaMC8X2tJhPLmeVE3SGDdYI4QNKdVmhNvMJGgPQ==", "path": "microsoft.aspnetcore.routing.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.routing.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-V5X5XkeAHaFyyBOGPrddVeqTNo6zRPJNS5PRhlzEyBXiNG9AtqUbMyWFdZahQyMiIWJau550z59A4kdC9g5I9A==", "path": "microsoft.aspnetcore.signalr/1.1.0", "hashPath": "microsoft.aspnetcore.signalr.1.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Common/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-TyLgQ4y4RVUIxiYFnHT181/rJ33/tL/NcBWC9BwLpulDt5/yGCG4EvsToZ49EBQ7256zj+R6OGw6JF+jj6MdPQ==", "path": "microsoft.aspnetcore.signalr.common/1.1.0", "hashPath": "microsoft.aspnetcore.signalr.common.1.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Core/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-mk69z50oFk2e89d3F/AfKeAvP3kvGG7MHG4ErydZiUd3ncSRq0kl0czq/COn/QVKYua9yGr2LIDwuR1C6/pu8Q==", "path": "microsoft.aspnetcore.signalr.core/1.1.0", "hashPath": "microsoft.aspnetcore.signalr.core.1.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Protocols.Json/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-BOsjatDJnvnnXCMajOlC0ISmiFnJi/EyJzMo0i//5fZJVCLrQ4fyV/HzrhhAhSJuwJOQDdDozKQ9MB9jHq84pg==", "path": "microsoft.aspnetcore.signalr.protocols.json/1.1.0", "hashPath": "microsoft.aspnetcore.signalr.protocols.json.1.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.WebSockets/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZpOcg2V0rCwU9ErfDb9y3Hcjoe7rU42XlmUS0mO4pVZQSgJVqR+DfyZtYd5LDa11F7bFNS2eezI9cBM3CmfGhw==", "path": "microsoft.aspnetcore.websockets/2.2.0", "hashPath": "microsoft.aspnetcore.websockets.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ErxAAKaDzxXASB/b5uLEkLgUWv1QbeVxyJYEHQwMaxXOeFFVkQxiq8RyfVcifLU7NR0QY0p3acqx4ZpYfhHDg==", "path": "microsoft.aspnetcore.webutilities/2.2.0", "hashPath": "microsoft.aspnetcore.webutilities.2.2.0.nupkg.sha512"}, "Microsoft.CodeCoverage/17.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-0N/ZJ71ncCxQWhgtkEYKOgu2oMHa8h1tsOUbhmIKXF8UwtSUCe4vHAsJ3DVcNWRwNfQzSTy263ZE+QF6MdIhhQ==", "path": "microsoft.codecoverage/17.1.0", "hashPath": "microsoft.codecoverage.17.1.0.nupkg.sha512"}, "Microsoft.CSharp/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-kaj6Wb4qoMuH3HySFJhxwQfe8R/sJsNJnANrvv8WdFPMoNbKY5htfNscv+LHCu5ipz+49m2e+WQXpLXr9XYemQ==", "path": "microsoft.csharp/4.5.0", "hashPath": "microsoft.csharp.4.5.0.nupkg.sha512"}, "Microsoft.Diagnostics.NETCore.Client/0.2.510501": {"type": "package", "serviceable": true, "sha512": "sha512-juoqJYMDs+lRrrZyOkXXMImJHneCF23cuvO4waFRd2Ds7j+ZuGIPbJm0Y/zz34BdeaGiiwGWraMUlln05W1PCQ==", "path": "microsoft.diagnostics.netcore.client/0.2.510501", "hashPath": "microsoft.diagnostics.netcore.client.0.2.510501.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/7.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-uvzJkdmqbB50COlCiNaBjWc4cl3kHBO9P7glPk6wK8xyLrli4xeVip+pmMyT/kYc2shWm1YdxegxFqKeCvQXAA==", "path": "microsoft.entityframeworkcore/7.0.1", "hashPath": "microsoft.entityframeworkcore.7.0.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/7.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-pPaoi2RHAkrsJ2kcGZGBiJFUXSS1LC+qET6HCn4hXP+X/kvIriJLv8lx6ddI6gTluGvm/lDgPAA0zZDXYaKf3A==", "path": "microsoft.entityframeworkcore.abstractions/7.0.1", "hashPath": "microsoft.entityframeworkcore.abstractions.7.0.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/7.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-4A4NFkPFoRMdry24Hjdb5/sOH3tU3hzFePTGnSg8P+VBrFp/EO8kcA4L0hBzwbXj2HGNL8I/quYyS5GVtb4EOg==", "path": "microsoft.entityframeworkcore.analyzers/7.0.1", "hashPath": "microsoft.entityframeworkcore.analyzers.7.0.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.DynamicLinq/6.2.20": {"type": "package", "serviceable": true, "sha512": "sha512-RD72FFL8F5KTDj6vyM/tbSrUICpkYtpr+6CLnNd4USACQ/CxuzsLQBD15cWTtlBlXIheJwKuak2xew70WrBe8g==", "path": "microsoft.entityframeworkcore.dynamiclinq/6.2.20", "hashPath": "microsoft.entityframeworkcore.dynamiclinq.6.2.20.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eQiYygtR2xZ0Uy7KtiFRHpoEx/U8xNwbNRgu1pEJgSxbJLtg6tDL1y2YcIbSuIRSNEljXIIHq/apEhGm1QL70g==", "path": "microsoft.entityframeworkcore.relational/7.0.0", "hashPath": "microsoft.entityframeworkcore.relational.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IeimUd0TNbhB4ded3AbgBLQv2SnsiVugDyGV1MvspQFVlA07nDC7Zul7kcwH5jWN3JiTcp/ySE83AIJo8yfKjg==", "path": "microsoft.extensions.caching.abstractions/7.0.0", "hashPath": "microsoft.extensions.caching.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xpidBs2KCE2gw1JrD0quHE72kvCaI3xFql5/Peb2GRtUuZX+dYPoK/NTdVMiM67Svym0M0Df9A3xyU0FbMQhHw==", "path": "microsoft.extensions.caching.memory/7.0.0", "hashPath": "microsoft.extensions.caching.memory.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tldQUBWt/xeH2K7/hMPPo5g8zuLc3Ro9I5d4o/XrxvxOCA2EZBtW7bCHHTc49fcBtvB8tLAb/Qsmfrq+2SJ4vA==", "path": "microsoft.extensions.configuration/7.0.0", "hashPath": "microsoft.extensions.configuration.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-f34u2eaqIjNO9YLHBz8rozVZ+TcFiFs0F3r7nUJd7FRkVSxk8u4OpoK226mi49MwexHOR2ibP9MFvRUaLilcQQ==", "path": "microsoft.extensions.configuration.abstractions/7.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xk2lRJ1RDuqe57BmgvRPyCt6zyePKUmvT6iuXqiHR+/OIIgWVR8Ff5k2p6DwmqY8a17hx/OnrekEhziEIeQP6Q==", "path": "microsoft.extensions.configuration.fileextensions/7.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LDNYe3uw76W35Jci+be4LDf2lkQZe0A7EEYQVChFbc509CpZ4Iupod8li4PUXPBhEUOFI/rlQNf5xkzJRQGvtA==", "path": "microsoft.extensions.configuration.json/7.0.0", "hashPath": "microsoft.extensions.configuration.json.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "path": "microsoft.extensions.dependencyinjection/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-TD5QHg98m3+QhgEV1YVoNMl5KtBw/4rjfxLHO0e/YV9bPUBDKntApP4xdrVtGgCeQZHVfC2EXIGsdpRNrr87Pg==", "path": "microsoft.extensions.dependencymodel/6.0.0", "hashPath": "microsoft.extensions.dependencymodel.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NyawiW9ZT/liQb34k9YqBSNPLuuPkrjMgQZ24Y/xXX1RoiBkLUdPMaQTmxhZ5TYu8ZKZ9qayzil75JX95vGQUg==", "path": "microsoft.extensions.fileproviders.abstractions/7.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-K8D2MTR+EtzkbZ8z80LrG7Ur64R7ZZdRLt1J5cgpc/pUWl0C6IkAUapPuK28oionHueCPELUqq0oYEvZfalNdg==", "path": "microsoft.extensions.fileproviders.physical/7.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2jONjKHiF+E92ynz2ZFcr9OvxIw+rTGMPEH+UZGeHTEComVav93jQUWGkso8yWwVBcEJGcNcZAaqY01FFJcj7w==", "path": "microsoft.extensions.filesystemglobbing/7.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-+k4AEn68HOJat5gj1TWa6X28WlirNQO9sPIIeQbia+91n03esEtMSSoekSTpMjUzjqtJWQN3McVx0GvSPFHF/Q==", "path": "microsoft.extensions.hosting.abstractions/2.2.0", "hashPath": "microsoft.extensions.hosting.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "path": "microsoft.extensions.logging/8.0.0", "hashPath": "microsoft.extensions.logging.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "path": "microsoft.extensions.logging.abstractions/8.0.0", "hashPath": "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UbsU/gYe4nv1DeqMXIVzDfNNek7Sk2kKuAOXL/Y+sLcAR0HwFUqzg1EPiU88jeHNe0g81aPvvHbvHarQr3r9IA==", "path": "microsoft.extensions.objectpool/9.0.0", "hashPath": "microsoft.extensions.objectpool.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JOVOfqpnqlVLUzINQ2fox8evY2SKLYJ3BV8QDe/Jyp21u1T7r45x/R/5QdteURMR5r01GxeJSBBUOCOyaNXA3g==", "path": "microsoft.extensions.options/8.0.0", "hashPath": "microsoft.extensions.options.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/6.34.0": {"type": "package", "serviceable": true, "sha512": "sha512-5nInt1KKSpKQBlhe6gXz4yKxRzRUQa21vCvSIIKKzAI2e1r9PHQOZc7aRzBA8L/JCvBxLbCxelvUqun6qwWPJg==", "path": "microsoft.identitymodel.abstractions/6.34.0", "hashPath": "microsoft.identitymodel.abstractions.6.34.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/6.34.0": {"type": "package", "serviceable": true, "sha512": "sha512-CZMom/ZoWcgjxLMxmCmcEkuoA0OA4swN1CGeMBQyxF/hEZgRbWK9EnWVJ9/oMUq3D1+OGJjnbN+W6gFq9kZcEg==", "path": "microsoft.identitymodel.jsonwebtokens/6.34.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.6.34.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/6.34.0": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON>0AbluNkI30/VKa96PxJhhFZDx/NGYIXFrRIRq1N5/V0TToaiuc3hM90QLFszT2BBQefnp/wjm12ilSudmt9bg==", "path": "microsoft.identitymodel.logging/6.34.0", "hashPath": "microsoft.identitymodel.logging.6.34.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/6.34.0": {"type": "package", "serviceable": true, "sha512": "sha512-PEPcGMqbEwEwbpQ6nTld9Nqq6V5BPZSOfk71qXZ7h7DuGuxa13bWvjImhJba5Ko88YvIuZuOBJWFZmjLfwbNXA==", "path": "microsoft.identitymodel.tokens/6.34.0", "hashPath": "microsoft.identitymodel.tokens.6.34.0.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-iZNkjYqlo8sIOI0bQfpsSoMTmB/kyvmV2h225ihyZT33aTp48ZpF6qYnXxzSXmHt8DpBAwBTX+1s1UFLbYfZKg==", "path": "microsoft.net.http.headers/2.2.0", "hashPath": "microsoft.net.http.headers.2.2.0.nupkg.sha512"}, "Microsoft.NET.Test.Sdk/17.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-MVKvOsHIfrZrvg+8aqOF5dknO/qWrR1sWZjMPQ1N42MKMlL/zQL30FQFZxPeWfmVKWUWAOmAHYsqB5OerTKziw==", "path": "microsoft.net.test.sdk/17.1.0", "hashPath": "microsoft.net.test.sdk.17.1.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-z7aeg8oHln2CuNulfhiLYxCVMPEwBl3rzicjvIX+4sUuCwvXw5oXQEtbiU2c0z4qYL5L3Kmx0mMA/+t/SbY67w==", "path": "microsoft.netcore.platforms/3.1.0", "hashPath": "microsoft.netcore.platforms.3.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.TestPlatform.ObjectModel/17.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-OMo/FYnKGy3lZEK0gfitskRM3ga/YBt6MyCyFPq0xNLeybGOQ6HnYNAAvzyePo5WPuMiw3LX+HiuRWNjnas1fA==", "path": "microsoft.testplatform.objectmodel/17.1.0", "hashPath": "microsoft.testplatform.objectmodel.17.1.0.nupkg.sha512"}, "Microsoft.TestPlatform.TestHost/17.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-JS0JDLniDhIzkSPLHz7N/x1CG8ywJOtwInFDYA3KQvbz+ojGoT5MT2YDVReL1b86zmNRV8339vsTSm/zh0RcMg==", "path": "microsoft.testplatform.testhost/17.1.0", "hashPath": "microsoft.testplatform.testhost.17.1.0.nupkg.sha512"}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "path": "microsoft.win32.registry/4.7.0", "hashPath": "microsoft.win32.registry.4.7.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A==", "path": "microsoft.win32.systemevents/6.0.0", "hashPath": "microsoft.win32.systemevents.6.0.0.nupkg.sha512"}, "Moq/4.18.4": {"type": "package", "serviceable": true, "sha512": "sha512-IOo+W51+7Afnb0noltJrKxPBSfsgMzTKCw+Re5AMx8l/vBbAbMDOynLik4+lBYIWDJSO0uV7Zdqt7cNb6RZZ+A==", "path": "moq/4.18.4", "hashPath": "moq.4.18.4.nupkg.sha512"}, "MyNihongo.KanaConverter/1.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-/HK2iltRWiYKGhFGKvcfMAyixzwiOkwQ1Ppp6/DZJQxPG0bKBYMYYIJd5oRyb0hDy11nDNea4UG/0xSsVGolRA==", "path": "mynihongo.kanaconverter/1.0.3", "hashPath": "mynihongo.kanaconverter.1.0.3.nupkg.sha512"}, "NETStandard.Library/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7jnbRU+L08FXKMxqUflxEXtVymWvNOrS8yHgu9s6EM8Anr6T/wIX4nZ08j/u3Asz+tCufp3YVwFSEvFTPYmBPA==", "path": "netstandard.library/2.0.0", "hashPath": "netstandard.library.2.0.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-R2pZ3B0UjeyHShm9vG+Tu0EBb2lC8b0dFzV9gVn50ofHXh9Smjk6kTn7A/FdAsC8B5cKib1OnGYOXxRBz5XQDg==", "path": "newtonsoft.json/13.0.2", "hashPath": "newtonsoft.json.13.0.2.nupkg.sha512"}, "Npgsql/7.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-A3BjyNebHjw/MahM6n3VnKbxrj/hQnMROEkUNUTjhUVv7yvcGnfBgw/FszahT9uCR23AVD2N/0Tvw1aHgxr9Gg==", "path": "npgsql/7.0.7", "hashPath": "npgsql.7.0.7.nupkg.sha512"}, "Npgsql.EntityFrameworkCore.PostgreSQL/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CyUNlFZmtX2Kmw8XK5Tlx5eVUCzWJ+zJHErxZiMo2Y8zCRuH9+/OMGwG+9Mmp5zD5p3Ifbi5Pp3btsqoDDkSZQ==", "path": "npgsql.entityframeworkcore.postgresql/7.0.0", "hashPath": "npgsql.entityframeworkcore.postgresql.7.0.0.nupkg.sha512"}, "NuGet.Frameworks/5.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-eaiXkUjC4NPcquGWzAGMXjuxvLwc6XGKMptSyOGQeT0X70BUZObuybJFZLA0OfTdueLd3US23NBPTBb6iF3V1Q==", "path": "nuget.frameworks/5.11.0", "hashPath": "nuget.frameworks.5.11.0.nupkg.sha512"}, "NUnit/3.13.3": {"type": "package", "serviceable": true, "sha512": "sha512-KNPDpls6EfHwC3+nnA67fh5wpxeLb3VLFAfLxrug6JMYDLHH6InaQIWR7Sc3y75d/9IKzMksH/gi08W7XWbmnQ==", "path": "nunit/3.13.3", "hashPath": "nunit.3.13.3.nupkg.sha512"}, "NUnit.Analyzers/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-gyRc0qmXUIjHaTcHTWZDHK5ccOF6cLEOGQJ6Fj5JWKh8/W1XzPFC6zGXRu5sDNSxfKaNeQRmkdz3M73ArQkY1A==", "path": "nunit.analyzers/3.3.0", "hashPath": "nunit.analyzers.3.3.0.nupkg.sha512"}, "NUnit3TestAdapter/4.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-kgH8VKsrcZZgNGQXRpVCrM7TnNz9li3b/snH+YmnXUNqsaWa1Xw9EQWHpbzq4Li2FbTjTE/E5N5HdLNXzZ8BpQ==", "path": "nunit3testadapter/4.2.1", "hashPath": "nunit3testadapter.4.2.1.nupkg.sha512"}, "Pipelines.Sockets.Unofficial/2.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-Bhk0FWxH1paI+18zr1g5cTL+ebeuDcBCR+rRFO+fKEhretgjs7MF2Mc1P64FGLecWp4zKCUOPzngBNrqVyY7Zg==", "path": "pipelines.sockets.unofficial/2.2.2", "hashPath": "pipelines.sockets.unofficial.2.2.2.nupkg.sha512"}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-9kyFSIdN3T0qjDQ2R0HRXYIhS3l5psBzQi6qqhdLz+SzFyEy4sVxNOke+yyYv8Cu8rPER12c3RDjLT8wF3WBYQ==", "path": "runtime.native.system.data.sqlclient.sni/4.7.0", "hashPath": "runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512"}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "Sentry/4.13.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wfw3M1WpFcrYaGzPm7QyUTfIOYkVXQ1ry6p4WYjhbLz9fPwV23SGQZTFDpdox67NHM0V0g1aoQ4YKLm4ANtEEg==", "path": "sentry/4.13.0", "hashPath": "sentry.4.13.0.nupkg.sha512"}, "Sentry.Profiling/4.13.0": {"type": "package", "serviceable": true, "sha512": "sha512-FJM5SDwHGN2+ceVGCeVQLHXNSBQEHSpvPrBVqVdbbduIdf7yWvaCdCn8MZyCJsvOuKMpwbrle6lXtxrKOZ2ESA==", "path": "sentry.profiling/4.13.0", "hashPath": "sentry.profiling.4.13.0.nupkg.sha512"}, "SharpCompress/0.34.2": {"type": "package", "serviceable": true, "sha512": "sha512-x+L5dw5D44EtN1m+0kkTHPi0v+u7S3iyj6nMcfns1y3uKQVb/nflmlroPWIILcxUnbTV2xRqwU335sMkRhDM7A==", "path": "sharpcompress/0.34.2", "hashPath": "sharpcompress.0.34.2.nupkg.sha512"}, "Spire.PDF/11.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-q5EoqN3a40e26PvKAomshHVKRsktblXKrT+XdiDq/EK5oo0xTX5vEQdA3NGYIrr0ZBfTVzfC6ZtsXq1Q/PrGuA==", "path": "spire.pdf/11.3.0", "hashPath": "spire.pdf.11.3.0.nupkg.sha512"}, "StackExchange.Redis/2.6.111": {"type": "package", "serviceable": true, "sha512": "sha512-49NlwihVG9I1YaPqYBx2e2yPqC4ecXMog8zVXMC3rjj2kufGkC3ofqvhOPBKP0c9ZQdJ3hhzduM7ckOQTE+gxg==", "path": "stackexchange.redis/2.6.111", "hashPath": "stackexchange.redis.2.6.111.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.Data.SqlClient/4.8.6": {"type": "package", "serviceable": true, "sha512": "sha512-2Ij/LCaTQRyAi5lAv7UUTV9R2FobC8xN9mE0fXBZohum/xLl8IZVmE98Rq5ugQHjCgTBRKqpXRb4ORulRdA6Ig==", "path": "system.data.sqlclient/4.8.6", "hashPath": "system.data.sqlclient.4.8.6.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-eIHRELiYDQvsMToML81QFkXEEYXUSUT2F28t1SGrevWqP+epFdw80SyAXIKTXOHrIEXReFOEnEr7XlGiC2GgOg==", "path": "system.diagnostics.diagnosticsource/4.5.0", "hashPath": "system.diagnostics.diagnosticsource.4.5.0.nupkg.sha512"}, "System.Diagnostics.EventLog/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lcyUiXTsETK2ALsZrX+nWuHSIQeazhqPphLfaRxzdGaG93+0kELqpgEHtwWOlQe7+jSFnKwaCAgL4kjeZCQJnw==", "path": "system.diagnostics.eventlog/6.0.0", "hashPath": "system.diagnostics.eventlog.6.0.0.nupkg.sha512"}, "System.Drawing.Common/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "path": "system.drawing.common/6.0.0", "hashPath": "system.drawing.common.6.0.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/6.34.0": {"type": "package", "serviceable": true, "sha512": "sha512-c0misfmFT3QxKY+a16PGlj+DtiUzoPaf26m2avyPZaLRc9vlIdLtmovfRY5MqN+y/SEoBSRXrgVaeZGPgFQQ6w==", "path": "system.identitymodel.tokens.jwt/6.34.0", "hashPath": "system.identitymodel.tokens.jwt.6.34.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Packaging/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-9VV4KAbgRQZ79iEoG40KIeZy38O30oWwewScAST879+oki8g/Wa2HXZQgrhDDxQM4GkP1PnRJll05NMiVPbYAw==", "path": "system.io.packaging/4.7.0", "hashPath": "system.io.packaging.4.7.0.nupkg.sha512"}, "System.IO.Pipelines/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-qEePWsaq9LoEEIqhbGe6D5J8c9IqQOUuTzzV6wn1POlfdLkJliZY3OlB0j0f17uMWlqZYjH7txj+2YbyrIA8Yg==", "path": "system.io.pipelines/5.0.1", "hashPath": "system.io.pipelines.5.0.1.nupkg.sha512"}, "System.Linq.Dynamic.Core/1.2.20": {"type": "package", "serviceable": true, "sha512": "sha512-J<PERSON>ggojPaq2QM603WCwghxxwNmi8ESDLCSTTjfq/TVFvj0J2XjXfvfa0X7pe8NOruF4CbPG2K46AWmgvk0Umoqg==", "path": "system.linq.dynamic.core/1.2.20", "hashPath": "system.linq.dynamic.core.1.2.20.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Net.WebSockets.WebSocketProtocol/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-FquLjdb/0CeMqb15u9Px6TwnyFl306WztKWu6sKKc5kWPYMdpi5BFEkdxzGoieYFp9UksyGwJnCw4KKAUfJjrw==", "path": "system.net.websockets.websocketprotocol/4.5.1", "hashPath": "system.net.websockets.websocketprotocol.4.5.1.nupkg.sha512"}, "System.Reactive/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-erBZjkQHWL9jpasCE/0qKAryzVBJFxGHVBAvgRN1bzM0q2s1S4oYREEEL0Vb+1kA/6BKb5FjUZMp5VXmy+gzkQ==", "path": "system.reactive/5.0.0", "hashPath": "system.reactive.5.0.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "path": "system.reflection.emit/4.3.0", "hashPath": "system.reflection.emit.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Metadata/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-COC1aiAJjCoA5GBF+QKL2uLqEBew4JsCkQmoHKbN3TlOZKa2fKLz5CpiRQKDz0RsAOEGsVKqOD5bomsXq/4STQ==", "path": "system.reflection.metadata/1.6.0", "hashPath": "system.reflection.metadata.1.6.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Security.AccessControl/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-JECvTt5aFF3WT3gHpfofL2MNNP6v84sxtXxpqhLBCcDRzqsPBmHhQ6shv4DwwN2tRlzsUxtb3G9M3763rbXKDg==", "path": "system.security.accesscontrol/4.7.0", "hashPath": "system.security.accesscontrol.4.7.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-WG3r7EyjUe9CMPFSs6bty5doUqT+q9pbI80hlNzo2SkPkZ4VTuZkGWjpp77JB8+uaL4DFPRdBsAY+DX3dBK92A==", "path": "system.security.cryptography.cng/4.5.0", "hashPath": "system.security.cryptography.cng.4.5.0.nupkg.sha512"}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "path": "system.security.principal.windows/4.7.0", "hashPath": "system.security.principal.windows.4.7.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LSyCblMpvOe0N3E+8e0skHcrIhgV2huaNcjUUEa8hRtgEAm36aGkRoC8Jxlb6Ra6GSfF29ftduPNywin8XolzQ==", "path": "system.text.encoding.codepages/7.0.0", "hashPath": "system.text.encoding.codepages.7.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OP6umVGxc0Z0MvZQBVigj4/U31Pw72ITihDWP9WiWDm+q5aoe0GaJivsfYGq53o6dxH7DcXWiCTl7+0o2CGdmg==", "path": "system.text.encodings.web/7.0.0", "hashPath": "system.text.encodings.web.7.0.0.nupkg.sha512"}, "System.Text.Json/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-DaGSsVqKsn/ia6RG8frjwmJonfos0srquhw09TlT8KRw5I43E+4gs+/bZj4K0vShJ5H9imCuXupb4RmS+dBy3w==", "path": "system.text.json/7.0.0", "hashPath": "system.text.json.7.0.0.nupkg.sha512"}, "System.Threading.Channels/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-MEH06N0rIGmRT4LOKQ2BmUO0IxfvmIY/PaouSq+DFQku72OL8cxfw8W99uGpTCFf2vx2QHLRSh374iSM3asdTA==", "path": "system.threading.channels/4.5.0", "hashPath": "system.threading.channels.4.5.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-WSKUTtLhPR8gllzIWO2x6l4lmAIfbyMAiTlyXAis4QBDonXK4b4S6F8zGARX4/P8wH3DH+sLdhamCiHn+fTU1A==", "path": "system.threading.tasks.extensions/4.5.1", "hashPath": "system.threading.tasks.extensions.4.5.1.nupkg.sha512"}, "ZstdSharp.Port/0.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-mT6Pb7qzDGapJ2ic4dAFqbzHpM+qgnasQIGR/Sofrk0fmKTQSTljG55OFXHbDJczLZWnUAFsg4bKR81uHiKzPg==", "path": "zstdsharp.port/0.7.2", "hashPath": "zstdsharp.port.0.7.2.nupkg.sha512"}, "AWSSDK/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "CalculateService/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "CommonChecker/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Domain/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Entity/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "ErrorCodeGenerator/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "EventProcessor/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Helper/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Infrastructure/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Interactor/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "PostgreDataContext/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Reporting/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "UseCase/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Domain.Core/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "UseCase.Core/*******": {"type": "reference", "serviceable": false, "sha512": ""}}}