﻿using CloudUnitTest.SampleData;
using Domain.Models.Santei;
using Entity.Tenant;
using Infrastructure.Repositories;

namespace CloudUnitTest.Repository.Santei;

public class SanteiInfRepositoryTest : BaseUT
{
    #region Get List SanteiInf
    [Test]
    public void TC_001_GetListSanteiInf_TestSuccess()
    {
        // Arrange
        var tenant = TenantProvider.GetNoTrackingDataContext();

        var santeiInfs = ReadDataSanteiInf.ReadSanteiInf();
        tenant.SanteiInfs.AddRange(santeiInfs);

        var santeiInfDetails = ReadDataSanteiInf.ReadSanteiInfDetail();
        tenant.SanteiInfDetails.AddRange(santeiInfDetails);

        var orderInfs = ReadDataSanteiInf.ReadOrderInf();
        tenant.OdrInfs.AddRange(orderInfs);

        var orderInfDetails = ReadDataSanteiInf.ReadOrderInfDetail();
        tenant.OdrInfDetails.AddRange(orderInfDetails);

        var tenMsts = ReadDataSanteiInf.ReadTenMst();
        tenant.TenMsts.AddRange(tenMsts);

        SanteiInfRepository santeiInfRepository = new SanteiInfRepository(TenantProvider);

        // Assert
        try
        {
            tenant.SaveChanges();

            // Act
            long ptId = 123456789;
            var resultQuery = santeiInfRepository.GetListSanteiInf(1, ptId, 20221212);

            Assert.True(CompareListSanteiInf(ptId, resultQuery, santeiInfs, santeiInfDetails, orderInfs, orderInfDetails, tenMsts));
        }
        finally
        {
            santeiInfRepository.ReleaseResource();
            tenant.SanteiInfs.RemoveRange(santeiInfs);
            tenant.SanteiInfDetails.RemoveRange(santeiInfDetails);
            tenant.OdrInfs.RemoveRange(orderInfs);
            tenant.OdrInfDetails.RemoveRange(orderInfDetails);
            tenant.TenMsts.RemoveRange(tenMsts);
            tenant.SaveChanges();
        }
    }

    [Test]
    public void TC_002_GetListSanteiInf_TestCoverLineSuccess()
    {
        // Arrange
        var tenant = TenantProvider.GetNoTrackingDataContext();

        var santeiInfs = ReadDataSanteiInf.ReadSanteiInf();
        tenant.SanteiInfs.AddRange(santeiInfs);

        var santeiInfDetail = ReadDataSanteiInf.ReadSanteiInfDetail().FirstOrDefault();
        SanteiInfDetail santeiInfDetail2 = santeiInfDetail!.Clone();
        santeiInfDetail2.KisanDate = santeiInfDetail.KisanDate + 1;
        tenant.SanteiInfDetails.Add(santeiInfDetail);
        tenant.SanteiInfDetails.Add(santeiInfDetail2);

        var orderInfs = ReadDataSanteiInf.ReadOrderInf();
        tenant.OdrInfs.AddRange(orderInfs);

        var orderInfDetails = ReadDataSanteiInf.ReadOrderInfDetail();
        tenant.OdrInfDetails.AddRange(orderInfDetails);

        var tenMsts = ReadDataSanteiInf.ReadTenMst();
        tenant.TenMsts.AddRange(tenMsts);

        SanteiInfRepository santeiInfRepository = new SanteiInfRepository(TenantProvider);

        // Assert
        try
        {
            tenant.SaveChanges();

            // Act
            long ptId = 123456789;
            var resultQuery = santeiInfRepository.GetListSanteiInf(1, ptId, 20221212);

            Assert.True(CompareListSanteiInf(ptId, resultQuery, santeiInfs, new() { santeiInfDetail }, orderInfs, orderInfDetails, tenMsts));
        }
        finally
        {
            santeiInfRepository.ReleaseResource();
            tenant.SanteiInfs.RemoveRange(santeiInfs);
            tenant.SanteiInfDetails.Remove(santeiInfDetail);
            tenant.SanteiInfDetails.Remove(santeiInfDetail2);
            tenant.OdrInfs.RemoveRange(orderInfs);
            tenant.OdrInfDetails.RemoveRange(orderInfDetails);
            tenant.TenMsts.RemoveRange(tenMsts);
            tenant.SaveChanges();
        }
    }

    [Test]
    public void TC_003_GetListSanteiInfDetail_TestSuccess()
    {
        // Arrange
        var tenant = TenantProvider.GetNoTrackingDataContext();

        var santeiInfDetails = ReadDataSanteiInf.ReadSanteiInfDetail();
        tenant.SanteiInfDetails.AddRange(santeiInfDetails);
        tenant.SaveChanges();

        SanteiInfRepository santeiInfRepository = new SanteiInfRepository(TenantProvider);

        // Act
        long ptId = 123456789;
        var resultQuery = santeiInfRepository.GetListSanteiInfDetails(1, ptId);

        // Assert
        try
        {
            bool result = false;
            long id = santeiInfDetails.FirstOrDefault()?.Id ?? 0;
            var santeiInfDetailModel = resultQuery.FirstOrDefault(item => item.Id == id);
            if (santeiInfDetailModel == null)
            {
                result = false;
            }
            else
            {
                result = CompareListSanteiInfDetail(ptId, santeiInfDetailModel, santeiInfDetails);
            }
            Assert.True(result);
        }
        finally
        {
            santeiInfRepository.ReleaseResource();
            tenant.SanteiInfDetails.RemoveRange(santeiInfDetails);
            tenant.SaveChanges();
        }
    }

    [Test]
    public void TC_004_GetOnlyListSanteiInf_TestSuccess()
    {
        // Arrange
        var tenant = TenantProvider.GetNoTrackingDataContext();
        var santeiInfs = ReadDataSanteiInf.ReadSanteiInf();
        tenant.SanteiInfs.AddRange(santeiInfs);
        tenant.SaveChanges();

        SanteiInfRepository santeiInfRepository = new SanteiInfRepository(TenantProvider);

        // Act
        long ptId = 123456789;
        var resultQuery = santeiInfRepository.GetOnlyListSanteiInf(1, ptId);

        // Assert
        try
        {
            Assert.True(CompareOnlySanteiInf(ptId, resultQuery, santeiInfs));
        }
        finally
        {
            santeiInfRepository.ReleaseResource();
            tenant.SanteiInfs.RemoveRange(santeiInfs);
            tenant.SaveChanges();
        }
    }

    [Test]
    public void TC_005_CheckExistItemCd_TestSuccess()
    {
        // Arrange
        var tenant = TenantProvider.GetNoTrackingDataContext();

        var tenMsts = ReadDataSanteiInf.ReadTenMst();
        tenant.TenMsts.AddRange(tenMsts);

        SanteiInfRepository santeiInfRepository = new SanteiInfRepository(TenantProvider);

        // Assert
        try
        {
            tenant.SaveChanges();

            // Act
            bool result = false;
            List<string> listItemCds = tenMsts.Select(item => item.ItemCd ?? string.Empty).ToList();
            result = santeiInfRepository.CheckExistItemCd(1, listItemCds);

            Assert.True(result);
        }
        finally
        {
            santeiInfRepository.ReleaseResource();
            tenant.TenMsts.RemoveRange(tenMsts);
            tenant.SaveChanges();
        }
    }

    [Test]
    public void TC_006_CheckExistItemCd_TestItemCdListNotExistFalse()
    {
        // Arrange
        SanteiInfRepository santeiInfRepository = new SanteiInfRepository(TenantProvider);

        // Act
        var result = santeiInfRepository.CheckExistItemCd(1, new());

        // Assert
        try
        {
            Assert.True(!result);
        }
        finally
        {
            santeiInfRepository.ReleaseResource();
        }
    }

    [Test]
    public void TC_007_CheckExistItemCd_TestInvalidHpId()
    {
        // Arrange
        var tenant = TenantProvider.GetNoTrackingDataContext();

        var tenMsts = ReadDataSanteiInf.ReadTenMst();
        tenant.TenMsts.AddRange(tenMsts);

        SanteiInfRepository santeiInfRepository = new SanteiInfRepository(TenantProvider);

        // Assert
        try
        {
            tenant.SaveChanges();

            // Act
            bool result = false;
            List<string> listItemCds = tenMsts.Select(item => item.ItemCd ?? string.Empty).ToList();
            listItemCds.Add("itemCdNotExist");
            result = santeiInfRepository.CheckExistItemCd(1, listItemCds);

            Assert.True(!result);
        }
        finally
        {
            santeiInfRepository.ReleaseResource();
            tenant.TenMsts.RemoveRange(tenMsts);
            tenant.SaveChanges();
        }
    }
    #endregion

    #region SaveSantei
    [Test]
    public void TC_008_SaveSantei_TestCreateNewSuccess()
    {
        // Arrange
        Random random = new();
        long ptId = long.MaxValue;
        string itemCd = "ItemCdTest";
        int alertDays = int.MaxValue;
        int alertTerm = random.Next(1, 6);
        int seqNo = random.Next(1, int.MaxValue);
        var tenant = TenantProvider.GetNoTrackingDataContext();
        var listSanteiModels = new List<SanteiInfModel>() { new SanteiInfModel(
                                                                                    0,
                                                                                    ptId,
                                                                                    itemCd,
                                                                                    alertDays,
                                                                                    alertTerm,
                                                                                    seqNo,
                                                                                    new(),
                                                                                    false
                                                                                )
                                                            };

        SanteiInfRepository santeiInfRepository = new SanteiInfRepository(TenantProvider);

        // Act
        var resultComman = santeiInfRepository.SaveSantei(1, 1, ptId, listSanteiModels);
        var santeiInf = tenant.SanteiInfs.OrderBy(item => item.Id)
                                         .LastOrDefault(item => item.PtId == ptId
                                                             && item.AlertDays == alertDays
                                                             && item.AlertTerm == alertTerm
                                                             && item.ItemCd == itemCd);
        var listSanteiInfs = new List<SanteiInf>() { santeiInf ?? new SanteiInf() };
        if (listSanteiInfs == null)
        {
            resultComman = false;
        }
        else
        {
            listSanteiModels = new List<SanteiInfModel>() { new SanteiInfModel(
                                                                                    santeiInf != null ? santeiInf.Id : 0,
                                                                                    ptId,
                                                                                    itemCd,
                                                                                    alertDays,
                                                                                    alertTerm,
                                                                                    seqNo,
                                                                                    new(),
                                                                                    false
                                                                                )
                                                            };
            resultComman = CompareOnlySanteiInf(ptId, listSanteiModels, listSanteiInfs);
        }

        // Assert
        try
        {
            Assert.True(resultComman);
        }
        finally
        {
            santeiInfRepository.ReleaseResource();
            if (listSanteiInfs != null)
            {
                tenant.SanteiInfs.RemoveRange(listSanteiInfs);
                tenant.SaveChanges();
            }
        }
    }

    [Test]
    public void TC_009_SaveSantei_TestUpdateSuccess()
    {
        // Arrange
        Random random = new();
        long ptId = long.MaxValue;
        string itemCd = "ItemCdTest";
        int alertDays = int.MaxValue;
        int alertTerm = random.Next(1, 6);
        int newAlertDays = random.Next(int.MaxValue);
        int seqNo = random.Next(1, int.MaxValue);
        int newAlertTerm = random.Next(1, 6);
        var tenant = TenantProvider.GetNoTrackingDataContext();
        var santeiInfUnitTest = new SanteiInf()
        {
            HpId = 1,
            PtId = ptId,
            ItemCd = itemCd,
            AlertDays = alertDays,
            AlertTerm = alertTerm,
            CreateDate = DateTime.UtcNow,
            CreateId = 1,
            UpdateDate = DateTime.UtcNow,
            UpdateId = 1
        };
        tenant.SanteiInfs.Add(santeiInfUnitTest);

        SanteiInfRepository santeiInfRepository = new SanteiInfRepository(TenantProvider);

        // Assert
        try
        {
            tenant.SaveChanges();

            // Act
            var listSanteiUpdateModels = new List<SanteiInfModel>() { new SanteiInfModel(
                                                                                    santeiInfUnitTest.Id,
                                                                                    ptId,
                                                                                    itemCd,
                                                                                    newAlertDays,
                                                                                    newAlertTerm,
                                                                                    seqNo,
                                                                                    new(),
                                                                                    false
                                                                                )
                                                            };
            var resultComman = santeiInfRepository.SaveSantei(1, 1, ptId, listSanteiUpdateModels);
            var santeiInfCheck = tenant.SanteiInfs.OrderBy(item => item.Id)
                                                  .LastOrDefault(item => item.Id == santeiInfUnitTest.Id);
            var listSanteiInfChecks = new List<SanteiInf>() { santeiInfCheck ?? new SanteiInf() };
            if (listSanteiInfChecks == null)
            {
                resultComman = false;
            }
            else
            {
                resultComman = CompareOnlySanteiInf(ptId, listSanteiUpdateModels, listSanteiInfChecks);
            }

            Assert.True(resultComman);
        }
        finally
        {
            santeiInfRepository.ReleaseResource();

            tenant.SanteiInfs.Remove(santeiInfUnitTest);
            tenant.SaveChanges();
        }
    }

    [Test]
    public void TC_010_SaveSantei_TestDeleteSuccess()
    {
        // Arrange
        Random random = new();
        long ptId = long.MaxValue;
        string itemCd = "ItemCdTest";
        int alertDays = int.MaxValue;
        int alertTerm = random.Next(1, 6);
        int seqNo = random.Next(1, int.MaxValue);
        var tenant = TenantProvider.GetNoTrackingDataContext();
        var santeiInfUnitTest = new SanteiInf()
        {
            HpId = 1,
            PtId = ptId,
            ItemCd = itemCd,
            AlertDays = alertDays,
            AlertTerm = alertTerm,
            CreateDate = DateTime.UtcNow,
            CreateId = 1,
            UpdateDate = DateTime.UtcNow,
            UpdateId = 1
        };
        tenant.SanteiInfs.Add(santeiInfUnitTest);

        SanteiInfRepository santeiInfRepository = new SanteiInfRepository(TenantProvider);
        SanteiInf? santeiInfCheck = null;

        // Assert
        try
        {
            tenant.SaveChanges();

            // Act
            var listSanteiUpdateModels = new List<SanteiInfModel>() { new SanteiInfModel(
                                                                                    santeiInfUnitTest.Id,
                                                                                    ptId,
                                                                                    itemCd,
                                                                                    alertDays,
                                                                                    alertTerm,
                                                                                    seqNo,
                                                                                    new(),
                                                                                    true
                                                                                )
                                                            };
            var resultComman = santeiInfRepository.SaveSantei(1, 1, ptId, listSanteiUpdateModels);
            santeiInfCheck = tenant.SanteiInfs.OrderBy(item => item.Id)
                                                 .LastOrDefault(item => item.Id == santeiInfUnitTest.Id);

            Assert.True(resultComman && santeiInfCheck == null);
        }
        finally
        {
            santeiInfRepository.ReleaseResource();
            if (santeiInfCheck != null)
            {
                tenant.SanteiInfs.Remove(santeiInfUnitTest);
                tenant.SaveChanges();
            }
        }
    }

    [Test]
    public void TC_011_SaveListSanteiInfDetail_TestCreateNewSuccess()
    {
        // Arrange
        Random random = new();
        long ptId = long.MaxValue;
        string itemCd = "ItemCdTest";
        int endDate = 20221212;
        int kisanSbt = random.Next(1, 6);
        int kisanDate = 20221214;
        string byomei = "byomeiForUnitTest";
        string hosokuComment = "hosokuCommentUnitTest";
        string comment = "commentUnitTest";
        var tenant = TenantProvider.GetNoTrackingDataContext();
        var listSanteiDetailModels = new List<SanteiInfDetailModel>() { new SanteiInfDetailModel(
                                                                                    0,
                                                                                    ptId,
                                                                                    itemCd,
                                                                                    endDate,
                                                                                    kisanSbt,
                                                                                    kisanDate,
                                                                                    byomei,
                                                                                    hosokuComment,
                                                                                    comment
                                                                                )
                                                                        };

        SanteiInfRepository santeiInfRepository = new SanteiInfRepository(TenantProvider);

        // Act
        var resultComman = santeiInfRepository.SaveListSanteiInfDetail(1, 1, ptId, listSanteiDetailModels);

        var santeiInfDetail = tenant.SanteiInfDetails.OrderBy(item => item.Id)
                                                     .LastOrDefault(item => item.PtId == ptId
                                                                                         && item.ItemCd == itemCd
                                                                                         && item.EndDate == endDate
                                                                                         && item.KisanSbt == kisanSbt
                                                                                         && item.KisanDate == kisanDate
                                                                                         && item.Byomei == byomei
                                                                                         && item.HosokuComment == hosokuComment
                                                                                         && item.Comment == comment);
        var listSanteiInfDetails = new List<SanteiInfDetail>() { santeiInfDetail ?? new SanteiInfDetail() };
        if (listSanteiInfDetails == null)
        {
            resultComman = false;
        }
        else
        {
            var santeiDetailModel = new SanteiInfDetailModel(
                                                                santeiInfDetail != null ? santeiInfDetail.Id : 0,
                                                                ptId,
                                                                itemCd,
                                                                endDate,
                                                                kisanSbt,
                                                                kisanDate,
                                                                byomei,
                                                                hosokuComment,
                                                                comment
                                                            );
            resultComman = CompareListSanteiInfDetail(ptId, santeiDetailModel, listSanteiInfDetails);
        }

        // Assert
        try
        {
            Assert.True(resultComman);
        }
        finally
        {
            santeiInfRepository.ReleaseResource();
            if (santeiInfDetail != null)
            {
                tenant.SanteiInfDetails.Remove(santeiInfDetail);
                tenant.SaveChanges();
            }
        }
    }

    [Test]
    public void TC_012_SaveListSanteiInfDetail_TestUpdateSuccess()
    {
        // Arrange
        Random random = new();
        long ptId = long.MaxValue;
        string itemCd = "ItemCdTest";
        int endDate = 20221212;
        int kisanSbt = random.Next(1, 6);
        int kisanDate = 20221214;
        string byomei = "byomeiForUnitTest";
        string hosokuComment = "hosokuCommentUnitTest";
        string comment = "commentUnitTest";
        int endDateNew = 20221112;
        int kisanSbtNew = random.Next(1, 6);
        int kisanDateNew = 20011214;
        string byomeiNew = "byomeiNewForUnitTest";
        string hosokuCommentNew = "hosokuCommentNewUnitTest";
        string commentNew = "commentNewUnitTest";
        var tenant = TenantProvider.GetNoTrackingDataContext();
        int hpId = 1;
        var santeiInfDetailUnitTest = new SanteiInfDetail()
        {
            HpId = hpId,
            PtId = ptId,
            ItemCd = itemCd,
            EndDate = endDate,
            KisanSbt = kisanSbt,
            KisanDate = kisanDate,
            Byomei = byomei,
            HosokuComment = hosokuComment,
            Comment = comment
        };
        tenant.SanteiInfDetails.Add(santeiInfDetailUnitTest);

        SanteiInfRepository santeiInfRepository = new SanteiInfRepository(TenantProvider);

        try
        {
            tenant.SaveChanges();

            // Act
            var santeiInfDetailModel = new SanteiInfDetailModel(
                                                                    santeiInfDetailUnitTest.Id,
                                                                    ptId,
                                                                    itemCd,
                                                                    endDateNew,
                                                                    kisanSbtNew,
                                                                    kisanDateNew,
                                                                    byomeiNew,
                                                                    hosokuCommentNew,
                                                                    commentNew
                                                                );
            var resultComman = santeiInfRepository.SaveListSanteiInfDetail(1, hpId, ptId, new List<SanteiInfDetailModel>() { santeiInfDetailModel });

            var santeiInfDetail = tenant.SanteiInfDetails.OrderBy(item => item.Id)
                                                         .LastOrDefault(item => item.Id == santeiInfDetailUnitTest.Id);
            var listSanteiInfDetails = new List<SanteiInfDetail>() { santeiInfDetail ?? new SanteiInfDetail() };
            if (listSanteiInfDetails == null)
            {
                resultComman = false;
            }
            else
            {
                resultComman = CompareListSanteiInfDetail(ptId, santeiInfDetailModel, listSanteiInfDetails);
            }

            // Assert
            Assert.True(resultComman);
        }
        finally
        {
            santeiInfRepository.ReleaseResource();
            if (santeiInfDetailUnitTest != null)
            {
                tenant.SanteiInfDetails.Remove(santeiInfDetailUnitTest);
                tenant.SaveChanges();
            }
        }
    }

    [Test]
    public void TC_013_SaveListSanteiInfDetail_TestDeleteSuccess()
    {
        // Arrange
        Random random = new();
        long ptId = long.MaxValue;
        string itemCd = "ItemCdTest";
        int endDate = 20221212;
        int kisanSbt = random.Next(1, 6);
        int kisanDate = 20221214;
        string byomei = "byomeiForUnitTest";
        string hosokuComment = "hosokuCommentUnitTest";
        string comment = "commentUnitTest";
        var tenant = TenantProvider.GetNoTrackingDataContext();
        int hpId = 1;
        var santeiInfDetailUnitTest = new SanteiInfDetail()
        {
            HpId = hpId,
            PtId = ptId,
            ItemCd = itemCd,
            EndDate = endDate,
            KisanSbt = kisanSbt,
            KisanDate = kisanDate,
            Byomei = byomei,
            HosokuComment = hosokuComment,
            Comment = comment
        };
        tenant.SanteiInfDetails.Add(santeiInfDetailUnitTest);

        SanteiInfRepository santeiInfRepository = new SanteiInfRepository(TenantProvider);

        try
        {
            tenant.SaveChanges();

            // Act
            var santeiInfDetailModel = new SanteiInfDetailModel(
                                                                    santeiInfDetailUnitTest.Id,
                                                                    ptId,
                                                                    itemCd,
                                                                    endDate,
                                                                    kisanSbt,
                                                                    kisanDate,
                                                                    byomei,
                                                                    hosokuComment,
                                                                    comment,
                                                                    true
                                                                );
            var resultComman = santeiInfRepository.SaveListSanteiInfDetail(1, hpId, ptId, new List<SanteiInfDetailModel>() { santeiInfDetailModel });

            var santeiInfDetail = tenant.SanteiInfDetails.OrderBy(item => item.Id)
                                                         .LastOrDefault(item => item.Id == santeiInfDetailUnitTest.Id);
            if (santeiInfDetail == null)
            {
                resultComman = false;
            }
            else
            {
                resultComman = santeiInfDetail.IsDeleted == 1;
            }

            // Assert
            Assert.True(resultComman);
        }
        finally
        {
            santeiInfRepository.ReleaseResource();
            tenant.SanteiInfDetails.Remove(santeiInfDetailUnitTest);
            tenant.SaveChanges();
        }
    }
    #endregion

    #region GetAutoSanteiMstByItemCd
    [Test]
    public void TC_014_GetAutoSanteiMstByItemCd_TestSuccess()
    {
        // Arrange
        var tenant = TenantProvider.GetNoTrackingDataContext();
        int hpId = 1;
        string itemCd = "TestItemCd";
        var autoSanteiMst = new AutoSanteiMst
        {
            HpId = hpId,
            ItemCd = itemCd,
            StartDate = 20220101,
            EndDate = 20221231
        };
        tenant.AutoSanteiMsts.Add(autoSanteiMst);
        tenant.SaveChanges();

        SanteiInfRepository santeiInfRepository = new SanteiInfRepository(TenantProvider);

        try
        {
            // Act
            var result = santeiInfRepository.GetAutoSanteiMstByItemCd(hpId, itemCd);

            // Assert
            Assert.IsNotNull(result);
            Assert.That(result.Count, Is.EqualTo(1));
            Assert.That(result[0].ItemCd, Is.EqualTo(autoSanteiMst.ItemCd));
            Assert.That(result[0].HpId, Is.EqualTo(autoSanteiMst.HpId));
            Assert.That(result[0].StartDate, Is.EqualTo(autoSanteiMst.StartDate));
            Assert.That(result[0].EndDate, Is.EqualTo(autoSanteiMst.EndDate));
        }
        finally
        {
            santeiInfRepository.ReleaseResource();
            tenant.AutoSanteiMsts.Remove(autoSanteiMst);
            tenant.SaveChanges();
        }
    }

    [Test]
    public void TC_015_GetAutoSanteiMstByItemCd_TestNoMatch()
    {
        // Arrange
        var tenant = TenantProvider.GetNoTrackingDataContext();
        int hpId = 2;
        string itemCd = "NonExistentItemCd";
        SanteiInfRepository santeiInfRepository = new SanteiInfRepository(TenantProvider);

        try
        {
            // Act
            var result = santeiInfRepository.GetAutoSanteiMstByItemCd(hpId, itemCd);

            // Assert
            Assert.IsNotNull(result);
            Assert.That(result.Count, Is.EqualTo(0));
        }
        finally
        {
            santeiInfRepository.ReleaseResource();
        }
    }
    #endregion

    #region private function
    private bool CompareOnlySanteiInf(long ptId, List<SanteiInfModel> santeiModels, List<SanteiInf> santeiInfs)
    {
        var santeiInf = santeiInfs.FirstOrDefault(item => item.PtId == ptId);
        if (santeiInf == null)
        {
            return false;
        }
        var santeiModel = santeiModels.FirstOrDefault(item => item.PtId == ptId && item.ItemCd == santeiInf.ItemCd);
        if (santeiModel == null)
        {
            return false;
        }
        if (santeiInf.Id != santeiModel.Id)
        {
            return false;
        }
        else if (santeiInf.AlertDays != santeiModel.AlertDays)
        {
            return false;
        }
        else if (santeiInf.AlertTerm != santeiModel.AlertTerm)
        {
            return false;
        }
        return true;
    }

    private bool CompareListSanteiInf(long ptId, List<SanteiInfModel> listSanteiModels, List<SanteiInf> santeiInfs, List<SanteiInfDetail> santeiInfDetails, List<OdrInf> odrInfs, List<OdrInfDetail> odrInfDetails, List<TenMst> tenMsts)
    {
        long id = 0;
        string itemCd = string.Empty;
        int seqNo = 0;
        int alertDays = 0;
        int alertTerm = 0;
        string itemName = string.Empty;
        int lastOdrDate = 0;
        int santeiItemCount = 0;
        double santeiItemSum = 0;
        int currentMonthSanteiItemCount = 0;
        double currentMonthSanteiItemSum = 0;
        bool isDeleted = false;

        var santeiInf = santeiInfs.FirstOrDefault();
        if (santeiInf != null)
        {
            id = santeiInf.Id;
            itemCd = santeiInf.ItemCd ?? string.Empty;
            seqNo = santeiInf.SeqNo;
            alertDays = santeiInf.AlertDays;
            alertTerm = santeiInf.AlertTerm;
        }

        var tenMst = tenMsts.FirstOrDefault();
        if (tenMst != null)
        {
            itemName = tenMst.Name ?? string.Empty;
        }

        var orderInf = odrInfs.FirstOrDefault();
        var orderInfDetail = odrInfDetails.FirstOrDefault();
        if (orderInf != null && orderInfDetail != null && orderInf.SinDate == orderInfDetail.SinDate)
        {
            lastOdrDate = orderInf.SinDate;
            santeiItemCount = 1;
            santeiItemSum = orderInfDetail.Suryo;
            currentMonthSanteiItemCount = 1;
            currentMonthSanteiItemSum = orderInfDetail.Suryo;
        }

        var santeiModel = listSanteiModels.FirstOrDefault(item => item.Id == id);
        if (santeiModel == null)
        {
            return false;
        }
        else if (santeiModel.Id != id)
        {
            return false;
        }
        else if (santeiModel.PtId != ptId)
        {
            return false;
        }
        else if (santeiModel.ItemCd != itemCd)
        {
            return false;
        }
        else if (santeiModel.SeqNo != seqNo)
        {
            return false;
        }
        else if (santeiModel.AlertDays != alertDays)
        {
            return false;
        }
        else if (santeiModel.AlertTerm != alertTerm)
        {
            return false;
        }
        else if (santeiModel.ItemName != itemName)
        {
            return false;
        }
        else if (santeiModel.LastOdrDate != lastOdrDate)
        {
            return false;
        }
        else if (santeiModel.SanteiItemCount != santeiItemCount)
        {
            return false;
        }
        else if (santeiModel.SanteiItemSum != santeiItemSum)
        {
            return false;
        }
        else if (santeiModel.CurrentMonthSanteiItemCount != currentMonthSanteiItemCount)
        {
            return false;
        }
        else if (santeiModel.CurrentMonthSanteiItemSum != currentMonthSanteiItemSum)
        {
            return false;
        }
        else if (santeiModel.IsDeleted != isDeleted)
        {
            return false;
        }
        var santeiInfDetail = santeiModel.SanteiInfDetailList.FirstOrDefault();
        if (santeiInfDetail == null)
        {
            return false;
        }
        return CompareListSanteiInfDetail(ptId, santeiInfDetail, santeiInfDetails);
    }

    private bool CompareListSanteiInfDetail(long ptId, SanteiInfDetailModel santeiDetailModel, List<SanteiInfDetail> santeiInfDetails)
    {
        var santeiInfDetail = santeiInfDetails.FirstOrDefault(item => item.Id == santeiDetailModel.Id);
        if (santeiInfDetail == null)
        {
            return false;
        }
        else if (santeiDetailModel.PtId != ptId)
        {
            return false;
        }
        else if (santeiDetailModel.ItemCd != santeiInfDetail.ItemCd)
        {
            return false;
        }
        else if (santeiDetailModel.EndDate != santeiInfDetail.EndDate)
        {
            return false;
        }
        else if (santeiDetailModel.KisanSbt != santeiInfDetail.KisanSbt)
        {
            return false;
        }
        else if (santeiDetailModel.KisanDate != santeiInfDetail.KisanDate)
        {
            return false;
        }
        else if (santeiDetailModel.Byomei != santeiInfDetail.Byomei)
        {
            return false;
        }
        else if (santeiDetailModel.HosokuComment != santeiInfDetail.HosokuComment)
        {
            return false;
        }
        else if (santeiDetailModel.Comment != santeiInfDetail.Comment)
        {
            return false;
        }
        else if (santeiDetailModel.IsDeleted)
        {
            return false;
        }
        return true;
    }
    #endregion
}
