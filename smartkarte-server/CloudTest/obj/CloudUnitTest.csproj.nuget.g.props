﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <RestoreSuccess Condition=" '$(RestoreSuccess)' == '' ">True</RestoreSuccess>
    <RestoreTool Condition=" '$(RestoreTool)' == '' ">NuGet</RestoreTool>
    <ProjectAssetsFile Condition=" '$(ProjectAssetsFile)' == '' ">$(MSBuildThisFileDirectory)project.assets.json</ProjectAssetsFile>
    <NuGetPackageRoot Condition=" '$(NuGetPackageRoot)' == '' ">/Users/<USER>/.nuget/packages/</NuGetPackageRoot>
    <NuGetPackageFolders Condition=" '$(NuGetPackageFolders)' == '' ">/Users/<USER>/.nuget/packages/</NuGetPackageFolders>
    <NuGetProjectStyle Condition=" '$(NuGetProjectStyle)' == '' ">PackageReference</NuGetProjectStyle>
    <NuGetToolVersion Condition=" '$(NuGetToolVersion)' == '' ">6.11.1</NuGetToolVersion>
  </PropertyGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <SourceRoot Include="/Users/<USER>/.nuget/packages/" />
  </ItemGroup>
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)microsoft.entityframeworkcore/7.0.1/buildTransitive/net6.0/Microsoft.EntityFrameworkCore.props" Condition="Exists('$(NuGetPackageRoot)microsoft.entityframeworkcore/7.0.1/buildTransitive/net6.0/Microsoft.EntityFrameworkCore.props')" />
    <Import Project="$(NuGetPackageRoot)nunit3testadapter/4.2.1/build/netcoreapp2.1/NUnit3TestAdapter.props" Condition="Exists('$(NuGetPackageRoot)nunit3testadapter/4.2.1/build/netcoreapp2.1/NUnit3TestAdapter.props')" />
    <Import Project="$(NuGetPackageRoot)nunit/3.13.3/build/NUnit.props" Condition="Exists('$(NuGetPackageRoot)nunit/3.13.3/build/NUnit.props')" />
    <Import Project="$(NuGetPackageRoot)microsoft.testplatform.testhost/17.1.0/build/netcoreapp2.1/Microsoft.TestPlatform.TestHost.props" Condition="Exists('$(NuGetPackageRoot)microsoft.testplatform.testhost/17.1.0/build/netcoreapp2.1/Microsoft.TestPlatform.TestHost.props')" />
    <Import Project="$(NuGetPackageRoot)microsoft.codecoverage/17.1.0/build/netstandard1.0/Microsoft.CodeCoverage.props" Condition="Exists('$(NuGetPackageRoot)microsoft.codecoverage/17.1.0/build/netstandard1.0/Microsoft.CodeCoverage.props')" />
    <Import Project="$(NuGetPackageRoot)microsoft.net.test.sdk/17.1.0/build/netcoreapp2.1/Microsoft.NET.Test.Sdk.props" Condition="Exists('$(NuGetPackageRoot)microsoft.net.test.sdk/17.1.0/build/netcoreapp2.1/Microsoft.NET.Test.Sdk.props')" />
  </ImportGroup>
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <PkgSentry Condition=" '$(PkgSentry)' == '' ">/Users/<USER>/.nuget/packages/sentry/4.13.0</PkgSentry>
    <PkgAWSSDK_Core Condition=" '$(PkgAWSSDK_Core)' == '' ">/Users/<USER>/.nuget/packages/awssdk.core/3.7.300.6</PkgAWSSDK_Core>
    <PkgAWSSDK_S3 Condition=" '$(PkgAWSSDK_S3)' == '' ">/Users/<USER>/.nuget/packages/awssdk.s3/3.7.301.1</PkgAWSSDK_S3>
    <PkgNUnit_Analyzers Condition=" '$(PkgNUnit_Analyzers)' == '' ">/Users/<USER>/.nuget/packages/nunit.analyzers/3.3.0</PkgNUnit_Analyzers>
    <PkgAWSSDK_Route53 Condition=" '$(PkgAWSSDK_Route53)' == '' ">/Users/<USER>/.nuget/packages/awssdk.route53/3.7.300</PkgAWSSDK_Route53>
    <PkgAWSSDK_RDS Condition=" '$(PkgAWSSDK_RDS)' == '' ">/Users/<USER>/.nuget/packages/awssdk.rds/3.7.300.1</PkgAWSSDK_RDS>
    <PkgAWSSDK_CloudWatch Condition=" '$(PkgAWSSDK_CloudWatch)' == '' ">/Users/<USER>/.nuget/packages/awssdk.cloudwatch/3.7.300</PkgAWSSDK_CloudWatch>
    <PkgAWSSDK_CloudFront Condition=" '$(PkgAWSSDK_CloudFront)' == '' ">/Users/<USER>/.nuget/packages/awssdk.cloudfront/3.7.300</PkgAWSSDK_CloudFront>
  </PropertyGroup>
</Project>