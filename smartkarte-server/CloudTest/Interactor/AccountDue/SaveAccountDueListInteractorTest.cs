using DocumentFormat.OpenXml.Spreadsheet;
using Domain.Models.AccountDue;
using Domain.Models.HpInf;
using Domain.Models.PatientInfor;
using Domain.Models.Reception;
using Domain.Models.User;
using EventProcessor.Interfaces;
using Infrastructure.Logger;
using Interactor.AccountDue;
using Moq;
using UseCase.AccountDue.SaveAccountDueList;
using SyunoSeikyuModel = Domain.Models.AccountDue.SyunoSeikyuModel;

namespace CloudUnitTest.Interactor.AccountDue
{
    public class SaveAccountDueListInteractorTest : BaseUT
    {
        #region ValidateInputDatas
        [Test]
        public void TC_001_SaveAccountDueListInteractor_Handle_ValidateInputDatas_Return_InvalidHpId()
        {
            //Arrange
            var mockIAccountDueRepository = new Mock<IAccountDueRepository>();
            var mockIUserRepository = new Mock<IUserRepository>();
            var mockIHpInfRepository = new Mock<IHpInfRepository>();
            var mockIPatientInforRepository = new Mock<IPatientInforRepository>();
            var mockIEventProcessorService = new Mock<IEventProcessorService>();
            var mockIReceptionRepository = new Mock<IReceptionRepository>();
            var mockILoggingHandler = new Mock<ILoggingHandler>();

            var saveAccountDueListInteractor = new SaveAccountDueListInteractor(mockIAccountDueRepository.Object, mockIUserRepository.Object, mockIHpInfRepository.Object,
                                                                                mockIPatientInforRepository.Object, mockIEventProcessorService.Object, mockIReceptionRepository.Object, TenantProvider);

            Random random = new Random();

            int hpId = random.Next(999, 99999); ;
            int userId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int sinDate = random.Next(999, 99999);
            string kaikeiTime = "";

            List<SyunoNyukinInputItem> syunoNyukinInputItems = new List<SyunoNyukinInputItem>();

            mockIHpInfRepository.Setup(finder => finder.CheckHpId(hpId))
            .Returns((int hpId) => false);
            var inputData = new SaveAccountDueListInputData(hpId, userId, ptId, sinDate, kaikeiTime, syunoNyukinInputItems, false);
            var result = saveAccountDueListInteractor.Handle(inputData);

            Assert.That(result.Status == SaveAccountDueListStatus.InvalidHpId);
        }

        [Test]
        public void TC_002_SaveAccountDueListInteractor_Handle_ValidateInputDatas_Return_InvalidUserId()
        {
            //Arrange
            var mockIAccountDueRepository = new Mock<IAccountDueRepository>();
            var mockIUserRepository = new Mock<IUserRepository>();
            var mockIHpInfRepository = new Mock<IHpInfRepository>();
            var mockIPatientInforRepository = new Mock<IPatientInforRepository>();
            var mockIEventProcessorService = new Mock<IEventProcessorService>();
            var mockIReceptionRepository = new Mock<IReceptionRepository>();
            var mockILoggingHandler = new Mock<ILoggingHandler>();

            var saveAccountDueListInteractor = new SaveAccountDueListInteractor(mockIAccountDueRepository.Object, mockIUserRepository.Object, mockIHpInfRepository.Object,
                                                                                mockIPatientInforRepository.Object, mockIEventProcessorService.Object, mockIReceptionRepository.Object, TenantProvider);

            Random random = new Random();

            int hpId = random.Next(999, 99999); ;
            int userId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int sinDate = random.Next(999, 99999);
            string kaikeiTime = "";

            List<SyunoNyukinInputItem> syunoNyukinInputItems = new List<SyunoNyukinInputItem>();

            mockIHpInfRepository.Setup(finder => finder.CheckHpId(hpId)).Returns((int hpId) => true);
            mockIUserRepository.Setup(finder => finder.CheckExistedUserId(hpId, userId)).Returns((int hpId, int userId) => false);
            var inputData = new SaveAccountDueListInputData(hpId, userId, ptId, sinDate, kaikeiTime, syunoNyukinInputItems, false);
            var result = saveAccountDueListInteractor.Handle(inputData);

            Assert.That(result.Status == SaveAccountDueListStatus.InvalidUserId);
        }

        [Test]
        public void TC_003_SaveAccountDueListInteractor_Handle_ValidateInputDatas_Return_InvalidPtId()
        {
            //Arrange
            var mockIAccountDueRepository = new Mock<IAccountDueRepository>();
            var mockIUserRepository = new Mock<IUserRepository>();
            var mockIHpInfRepository = new Mock<IHpInfRepository>();
            var mockIPatientInforRepository = new Mock<IPatientInforRepository>();
            var mockIEventProcessorService = new Mock<IEventProcessorService>();
            var mockIReceptionRepository = new Mock<IReceptionRepository>();
            var mockILoggingHandler = new Mock<ILoggingHandler>();

            var saveAccountDueListInteractor = new SaveAccountDueListInteractor(mockIAccountDueRepository.Object, mockIUserRepository.Object, mockIHpInfRepository.Object,
                                                                                mockIPatientInforRepository.Object, mockIEventProcessorService.Object, mockIReceptionRepository.Object, TenantProvider);

            Random random = new Random();

            int hpId = random.Next(999, 99999); ;
            int userId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int sinDate = random.Next(999, 99999);
            string kaikeiTime = "";

            List<SyunoNyukinInputItem> syunoNyukinInputItems = new List<SyunoNyukinInputItem>();

            mockIHpInfRepository.Setup(finder => finder.CheckHpId(hpId)).Returns((int hpId) => true);
            mockIUserRepository.Setup(finder => finder.CheckExistedUserId(hpId, userId)).Returns((int hpId, int userId) => true);
            mockIPatientInforRepository.Setup(finder => finder.CheckExistIdList(hpId, new List<long> { ptId })).Returns((int hpId, List<long> ptId) => false);
            var inputData = new SaveAccountDueListInputData(hpId, userId, ptId, sinDate, kaikeiTime, syunoNyukinInputItems, false);
            var result = saveAccountDueListInteractor.Handle(inputData);

            Assert.That(result.Status == SaveAccountDueListStatus.InvalidPtId);
        }

        [Test]
        public void TC_004_SaveAccountDueListInteractor_Handle_ValidateInputDatas_Return_InvalidSindate()
        {
            //Arrange
            var mockIAccountDueRepository = new Mock<IAccountDueRepository>();
            var mockIUserRepository = new Mock<IUserRepository>();
            var mockIHpInfRepository = new Mock<IHpInfRepository>();
            var mockIPatientInforRepository = new Mock<IPatientInforRepository>();
            var mockIEventProcessorService = new Mock<IEventProcessorService>();
            var mockIReceptionRepository = new Mock<IReceptionRepository>();
            var mockILoggingHandler = new Mock<ILoggingHandler>();

            var saveAccountDueListInteractor = new SaveAccountDueListInteractor(mockIAccountDueRepository.Object, mockIUserRepository.Object, mockIHpInfRepository.Object,
                                                                                mockIPatientInforRepository.Object, mockIEventProcessorService.Object, mockIReceptionRepository.Object, TenantProvider);

            Random random = new Random();

            int hpId = random.Next(999, 99999); ;
            int userId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int sinDate = random.Next(999, 99999);
            string kaikeiTime = "";
            int nyukinKbn = -1, sortNo = 0, adjustFutan = 0, nyukinGaku = 0, paymentMethodCd = 0, nyukinDate = 0, uketukeSbt = 0, seikyuGaku = 0, seikyuTensu = 0, raiinInfStatus = 0, seikyuAdjustFutan = 0, seikyuSinDate = 0;
            long raiinNo = 0, seqNo = 0;
            string nyukinCmt = "", seikyuDetail = "";
            bool isUpdated = false, isDelete = false;
            List<SyunoNyukinInputItem> syunoNyukinInputItems = new List<SyunoNyukinInputItem>()
            {
                new SyunoNyukinInputItem(nyukinKbn, raiinNo, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete)
            };

            mockIHpInfRepository.Setup(finder => finder.CheckHpId(hpId)).Returns((int hpId) => true);
            mockIUserRepository.Setup(finder => finder.CheckExistedUserId(hpId, userId)).Returns((int hpId, int userId) => true);
            mockIPatientInforRepository.Setup(finder => finder.CheckExistIdList(hpId, new List<long> { ptId })).Returns((int hpId, List<long> ptId) => true);
            var inputData = new SaveAccountDueListInputData(hpId, userId, ptId, sinDate, kaikeiTime, syunoNyukinInputItems, false);
            var result = saveAccountDueListInteractor.Handle(inputData);

            Assert.That(result.Status == SaveAccountDueListStatus.InvalidSindate);
        }

        [Test]
        public void TC_005_SaveAccountDueListInteractor_Handle_ValidateInputDatas_Return_InvalidNyukinKbn()
        {
            //Arrange
            var mockIAccountDueRepository = new Mock<IAccountDueRepository>();
            var mockIUserRepository = new Mock<IUserRepository>();
            var mockIHpInfRepository = new Mock<IHpInfRepository>();
            var mockIPatientInforRepository = new Mock<IPatientInforRepository>();
            var mockIEventProcessorService = new Mock<IEventProcessorService>();
            var mockIReceptionRepository = new Mock<IReceptionRepository>();
            var mockILoggingHandler = new Mock<ILoggingHandler>();

            var saveAccountDueListInteractor = new SaveAccountDueListInteractor(mockIAccountDueRepository.Object, mockIUserRepository.Object, mockIHpInfRepository.Object,
                                                                                mockIPatientInforRepository.Object, mockIEventProcessorService.Object, mockIReceptionRepository.Object, TenantProvider);

            Random random = new Random();

            int hpId = random.Next(999, 99999); ;
            int userId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int sinDate = random.Next(********, ********);
            string kaikeiTime = "";
            int nyukinKbn = -1, sortNo = 0, adjustFutan = 0, nyukinGaku = 0, paymentMethodCd = 0, nyukinDate = 0, uketukeSbt = 0, seikyuGaku = 0, seikyuTensu = 0, raiinInfStatus = 0, seikyuAdjustFutan = 0, seikyuSinDate = 0;
            long raiinNo = 0, seqNo = 0;
            string nyukinCmt = "", seikyuDetail = "";
            bool isUpdated = false, isDelete = false;
            List<SyunoNyukinInputItem> syunoNyukinInputItems = new List<SyunoNyukinInputItem>()
            {
                new SyunoNyukinInputItem(nyukinKbn, raiinNo, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete)
            };

            mockIHpInfRepository.Setup(finder => finder.CheckHpId(hpId)).Returns((int hpId) => true);
            mockIUserRepository.Setup(finder => finder.CheckExistedUserId(hpId, userId)).Returns((int hpId, int userId) => true);
            mockIPatientInforRepository.Setup(finder => finder.CheckExistIdList(hpId, new List<long> { ptId })).Returns((int hpId, List<long> ptId) => true);
            var inputData = new SaveAccountDueListInputData(hpId, userId, ptId, sinDate, kaikeiTime, syunoNyukinInputItems, false);
            var result = saveAccountDueListInteractor.Handle(inputData);

            Assert.That(result.Status == SaveAccountDueListStatus.InvalidNyukinKbn);
        }

        [Test]
        public void TC_006_SaveAccountDueListInteractor_Handle_ValidateInputDatas_Return_InvalidRaiinNo()
        {
            //Arrange
            var mockIAccountDueRepository = new Mock<IAccountDueRepository>();
            var mockIUserRepository = new Mock<IUserRepository>();
            var mockIHpInfRepository = new Mock<IHpInfRepository>();
            var mockIPatientInforRepository = new Mock<IPatientInforRepository>();
            var mockIEventProcessorService = new Mock<IEventProcessorService>();
            var mockIReceptionRepository = new Mock<IReceptionRepository>();
            var mockILoggingHandler = new Mock<ILoggingHandler>();

            var saveAccountDueListInteractor = new SaveAccountDueListInteractor(mockIAccountDueRepository.Object, mockIUserRepository.Object, mockIHpInfRepository.Object,
                                                                                mockIPatientInforRepository.Object, mockIEventProcessorService.Object, mockIReceptionRepository.Object, TenantProvider);

            Random random = new Random();

            int hpId = random.Next(999, 99999); ;
            int userId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int sinDate = random.Next(********, ********);
            string kaikeiTime = "";
            int nyukinKbn = 0, sortNo = 0, adjustFutan = 0, nyukinGaku = 0, paymentMethodCd = 0, nyukinDate = 0, uketukeSbt = 0, seikyuGaku = 0, seikyuTensu = 0, raiinInfStatus = 0, seikyuAdjustFutan = 0, seikyuSinDate = 0;
            long raiinNo = 0, seqNo = 0;
            string nyukinCmt = "", seikyuDetail = "";
            bool isUpdated = false, isDelete = false;
            List<SyunoNyukinInputItem> syunoNyukinInputItems = new List<SyunoNyukinInputItem>()
            {
                new SyunoNyukinInputItem(nyukinKbn, raiinNo, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete)
            };

            mockIHpInfRepository.Setup(finder => finder.CheckHpId(hpId)).Returns((int hpId) => true);
            mockIUserRepository.Setup(finder => finder.CheckExistedUserId(hpId, userId)).Returns((int hpId, int userId) => true);
            mockIPatientInforRepository.Setup(finder => finder.CheckExistIdList(hpId, new List<long> { ptId })).Returns((int hpId, List<long> ptId) => true);
            var inputData = new SaveAccountDueListInputData(hpId, userId, ptId, sinDate, kaikeiTime, syunoNyukinInputItems, false);
            var result = saveAccountDueListInteractor.Handle(inputData);

            Assert.That(result.Status == SaveAccountDueListStatus.InvalidRaiinNo);
        }

        [Test]
        public void TC_007_SaveAccountDueListInteractor_Handle_ValidateInputDatas_Return_InvalidSortNo()
        {
            //Arrange
            var mockIAccountDueRepository = new Mock<IAccountDueRepository>();
            var mockIUserRepository = new Mock<IUserRepository>();
            var mockIHpInfRepository = new Mock<IHpInfRepository>();
            var mockIPatientInforRepository = new Mock<IPatientInforRepository>();
            var mockIEventProcessorService = new Mock<IEventProcessorService>();
            var mockIReceptionRepository = new Mock<IReceptionRepository>();
            var mockILoggingHandler = new Mock<ILoggingHandler>();

            var saveAccountDueListInteractor = new SaveAccountDueListInteractor(mockIAccountDueRepository.Object, mockIUserRepository.Object, mockIHpInfRepository.Object,
                                                                                mockIPatientInforRepository.Object, mockIEventProcessorService.Object, mockIReceptionRepository.Object, TenantProvider);

            Random random = new Random();

            int hpId = random.Next(999, 99999); ;
            int userId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int sinDate = random.Next(********, ********);
            string kaikeiTime = "";
            int nyukinKbn = 0, sortNo = -1, adjustFutan = 0, nyukinGaku = 0, paymentMethodCd = 0, nyukinDate = 0, uketukeSbt = 0, seikyuGaku = 0, seikyuTensu = 0, raiinInfStatus = 0, seikyuAdjustFutan = 0, seikyuSinDate = 0;
            long raiinNo = 1, seqNo = 0;
            string nyukinCmt = "", seikyuDetail = "";
            bool isUpdated = false, isDelete = false;
            List<SyunoNyukinInputItem> syunoNyukinInputItems = new List<SyunoNyukinInputItem>()
            {
                new SyunoNyukinInputItem(nyukinKbn, raiinNo, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete)
            };

            mockIHpInfRepository.Setup(finder => finder.CheckHpId(hpId)).Returns((int hpId) => true);
            mockIUserRepository.Setup(finder => finder.CheckExistedUserId(hpId, userId)).Returns((int hpId, int userId) => true);
            mockIPatientInforRepository.Setup(finder => finder.CheckExistIdList(hpId, new List<long> { ptId })).Returns((int hpId, List<long> ptId) => true);
            var inputData = new SaveAccountDueListInputData(hpId, userId, ptId, sinDate, kaikeiTime, syunoNyukinInputItems, false);
            var result = saveAccountDueListInteractor.Handle(inputData);

            Assert.That(result.Status == SaveAccountDueListStatus.InvalidSortNo);
        }

        [Test]
        public void TC_008_SaveAccountDueListInteractor_Handle_ValidateInputDatas_Return_InvalidPaymentMethodCd()
        {
            //Arrange
            var mockIAccountDueRepository = new Mock<IAccountDueRepository>();
            var mockIUserRepository = new Mock<IUserRepository>();
            var mockIHpInfRepository = new Mock<IHpInfRepository>();
            var mockIPatientInforRepository = new Mock<IPatientInforRepository>();
            var mockIEventProcessorService = new Mock<IEventProcessorService>();
            var mockIReceptionRepository = new Mock<IReceptionRepository>();
            var mockILoggingHandler = new Mock<ILoggingHandler>();

            var saveAccountDueListInteractor = new SaveAccountDueListInteractor(mockIAccountDueRepository.Object, mockIUserRepository.Object, mockIHpInfRepository.Object,
                                                                                mockIPatientInforRepository.Object, mockIEventProcessorService.Object, mockIReceptionRepository.Object, TenantProvider);

            Random random = new Random();

            int hpId = random.Next(999, 99999); ;
            int userId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int sinDate = random.Next(********, ********);
            string kaikeiTime = "";
            int nyukinKbn = 0, sortNo = 0, adjustFutan = 0, nyukinGaku = 0, paymentMethodCd = -1, nyukinDate = 0, uketukeSbt = 0, seikyuGaku = 0, seikyuTensu = 0, raiinInfStatus = 0, seikyuAdjustFutan = 0, seikyuSinDate = 0;
            long raiinNo = 1, seqNo = 0;
            string nyukinCmt = "", seikyuDetail = "";
            bool isUpdated = false, isDelete = false;
            List<SyunoNyukinInputItem> syunoNyukinInputItems = new List<SyunoNyukinInputItem>()
            {
                new SyunoNyukinInputItem(nyukinKbn, raiinNo, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete)
            };

            mockIHpInfRepository.Setup(finder => finder.CheckHpId(hpId)).Returns((int hpId) => true);
            mockIUserRepository.Setup(finder => finder.CheckExistedUserId(hpId, userId)).Returns((int hpId, int userId) => true);
            mockIPatientInforRepository.Setup(finder => finder.CheckExistIdList(hpId, new List<long> { ptId })).Returns((int hpId, List<long> ptId) => true);
            var inputData = new SaveAccountDueListInputData(hpId, userId, ptId, sinDate, kaikeiTime, syunoNyukinInputItems, false);
            var result = saveAccountDueListInteractor.Handle(inputData);

            Assert.That(result.Status == SaveAccountDueListStatus.InvalidPaymentMethodCd);
        }

        [Test]
        public void TC_009_SaveAccountDueListInteractor_Handle_ValidateInputDatas_Return_InvalidNyukinDate()
        {
            //Arrange
            var mockIAccountDueRepository = new Mock<IAccountDueRepository>();
            var mockIUserRepository = new Mock<IUserRepository>();
            var mockIHpInfRepository = new Mock<IHpInfRepository>();
            var mockIPatientInforRepository = new Mock<IPatientInforRepository>();
            var mockIEventProcessorService = new Mock<IEventProcessorService>();
            var mockIReceptionRepository = new Mock<IReceptionRepository>();
            var mockILoggingHandler = new Mock<ILoggingHandler>();

            var saveAccountDueListInteractor = new SaveAccountDueListInteractor(mockIAccountDueRepository.Object, mockIUserRepository.Object, mockIHpInfRepository.Object,
                                                                                mockIPatientInforRepository.Object, mockIEventProcessorService.Object, mockIReceptionRepository.Object, TenantProvider);

            Random random = new Random();

            int hpId = random.Next(999, 99999); ;
            int userId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int sinDate = random.Next(********, ********);
            string kaikeiTime = "";
            int nyukinKbn = 0, sortNo = 0, adjustFutan = 0, nyukinGaku = 0, paymentMethodCd = 1, nyukinDate = -1, uketukeSbt = 0, seikyuGaku = 0, seikyuTensu = 0, raiinInfStatus = 0, seikyuAdjustFutan = 0, seikyuSinDate = 0;
            long raiinNo = 1, seqNo = 0;
            string nyukinCmt = "", seikyuDetail = "";
            bool isUpdated = false, isDelete = false;
            List<SyunoNyukinInputItem> syunoNyukinInputItems = new List<SyunoNyukinInputItem>()
            {
                new SyunoNyukinInputItem(nyukinKbn, raiinNo, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete)
            };

            mockIHpInfRepository.Setup(finder => finder.CheckHpId(hpId)).Returns((int hpId) => true);
            mockIUserRepository.Setup(finder => finder.CheckExistedUserId(hpId, userId)).Returns((int hpId, int userId) => true);
            mockIPatientInforRepository.Setup(finder => finder.CheckExistIdList(hpId, new List<long> { ptId })).Returns((int hpId, List<long> ptId) => true);
            var inputData = new SaveAccountDueListInputData(hpId, userId, ptId, sinDate, kaikeiTime, syunoNyukinInputItems, false);
            var result = saveAccountDueListInteractor.Handle(inputData);

            Assert.That(result.Status == SaveAccountDueListStatus.InvalidNyukinDate);
        }

        [Test]
        public void TC_010_SaveAccountDueListInteractor_Handle_ValidateInputDatas_Return_NyukinCmtMaxLength100()
        {
            //Arrange
            var mockIAccountDueRepository = new Mock<IAccountDueRepository>();
            var mockIUserRepository = new Mock<IUserRepository>();
            var mockIHpInfRepository = new Mock<IHpInfRepository>();
            var mockIPatientInforRepository = new Mock<IPatientInforRepository>();
            var mockIEventProcessorService = new Mock<IEventProcessorService>();
            var mockIReceptionRepository = new Mock<IReceptionRepository>();
            var mockILoggingHandler = new Mock<ILoggingHandler>();

            var saveAccountDueListInteractor = new SaveAccountDueListInteractor(mockIAccountDueRepository.Object, mockIUserRepository.Object, mockIHpInfRepository.Object,
                                                                                mockIPatientInforRepository.Object, mockIEventProcessorService.Object, mockIReceptionRepository.Object, TenantProvider);

            Random random = new Random();

            int hpId = random.Next(999, 99999); ;
            int userId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int sinDate = random.Next(********, ********);
            string kaikeiTime = "";
            int nyukinKbn = 0, sortNo = 0, adjustFutan = 0, nyukinGaku = 0, paymentMethodCd = 1, nyukinDate = 1, uketukeSbt = 0, seikyuGaku = 0, seikyuTensu = 0, raiinInfStatus = 0, seikyuAdjustFutan = 0, seikyuSinDate = 0;
            long raiinNo = 1, seqNo = 0;
            string longString = "OpenAI is amazing! ", seikyuDetail = "";
            string nyukinCmt = string.Concat(Enumerable.Repeat(longString, 10));
            bool isUpdated = false, isDelete = false;
            List<SyunoNyukinInputItem> syunoNyukinInputItems = new List<SyunoNyukinInputItem>()
            {
                new SyunoNyukinInputItem(nyukinKbn, raiinNo, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete)
            };

            mockIHpInfRepository.Setup(finder => finder.CheckHpId(hpId)).Returns((int hpId) => true);
            mockIUserRepository.Setup(finder => finder.CheckExistedUserId(hpId, userId)).Returns((int hpId, int userId) => true);
            mockIPatientInforRepository.Setup(finder => finder.CheckExistIdList(hpId, new List<long> { ptId })).Returns((int hpId, List<long> ptId) => true);
            var inputData = new SaveAccountDueListInputData(hpId, userId, ptId, sinDate, kaikeiTime, syunoNyukinInputItems, false);
            var result = saveAccountDueListInteractor.Handle(inputData);

            Assert.That(result.Status == SaveAccountDueListStatus.NyukinCmtMaxLength100);
        }

        [Test]
        public void TC_011_SaveAccountDueListInteractor_Handle_ValidateInputDatas_Return_InvalidSeikyuTensu()
        {
            //Arrange
            var mockIAccountDueRepository = new Mock<IAccountDueRepository>();
            var mockIUserRepository = new Mock<IUserRepository>();
            var mockIHpInfRepository = new Mock<IHpInfRepository>();
            var mockIPatientInforRepository = new Mock<IPatientInforRepository>();
            var mockIEventProcessorService = new Mock<IEventProcessorService>();
            var mockIReceptionRepository = new Mock<IReceptionRepository>();
            var mockILoggingHandler = new Mock<ILoggingHandler>();

            var saveAccountDueListInteractor = new SaveAccountDueListInteractor(mockIAccountDueRepository.Object, mockIUserRepository.Object, mockIHpInfRepository.Object,
                                                                                mockIPatientInforRepository.Object, mockIEventProcessorService.Object, mockIReceptionRepository.Object, TenantProvider);

            Random random = new Random();

            int hpId = random.Next(999, 99999); ;
            int userId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int sinDate = random.Next(********, ********);
            string kaikeiTime = "";
            int nyukinKbn = 0, sortNo = 0, adjustFutan = 0, nyukinGaku = 0, paymentMethodCd = 1, nyukinDate = 1, uketukeSbt = 0, seikyuGaku = 0, seikyuTensu = -1, raiinInfStatus = 0, seikyuAdjustFutan = 0, seikyuSinDate = 0;
            long raiinNo = 1, seqNo = 0;
            string nyukinCmt = "", seikyuDetail = "";
            bool isUpdated = false, isDelete = false;
            List<SyunoNyukinInputItem> syunoNyukinInputItems = new List<SyunoNyukinInputItem>()
            {
                new SyunoNyukinInputItem(nyukinKbn, raiinNo, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete)
            };

            mockIHpInfRepository.Setup(finder => finder.CheckHpId(hpId)).Returns((int hpId) => true);
            mockIUserRepository.Setup(finder => finder.CheckExistedUserId(hpId, userId)).Returns((int hpId, int userId) => true);
            mockIPatientInforRepository.Setup(finder => finder.CheckExistIdList(hpId, new List<long> { ptId })).Returns((int hpId, List<long> ptId) => true);
            var inputData = new SaveAccountDueListInputData(hpId, userId, ptId, sinDate, kaikeiTime, syunoNyukinInputItems, false);
            var result = saveAccountDueListInteractor.Handle(inputData);

            Assert.That(result.Status == SaveAccountDueListStatus.InvalidSeikyuTensu);
        }

        [Test]
        public void TC_012_SaveAccountDueListInteractor_Handle_ValidateInputDatas_Return_InvalidSeqNo()
        {
            //Arrange
            var mockIAccountDueRepository = new Mock<IAccountDueRepository>();
            var mockIUserRepository = new Mock<IUserRepository>();
            var mockIHpInfRepository = new Mock<IHpInfRepository>();
            var mockIPatientInforRepository = new Mock<IPatientInforRepository>();
            var mockIEventProcessorService = new Mock<IEventProcessorService>();
            var mockIReceptionRepository = new Mock<IReceptionRepository>();
            var mockILoggingHandler = new Mock<ILoggingHandler>();

            var saveAccountDueListInteractor = new SaveAccountDueListInteractor(mockIAccountDueRepository.Object, mockIUserRepository.Object, mockIHpInfRepository.Object,
                                                                                mockIPatientInforRepository.Object, mockIEventProcessorService.Object, mockIReceptionRepository.Object, TenantProvider);

            Random random = new Random();

            int hpId = random.Next(999, 99999); ;
            int userId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int sinDate = random.Next(********, ********);
            string kaikeiTime = "";
            int nyukinKbn = 0, sortNo = 0, adjustFutan = 0, nyukinGaku = 0, paymentMethodCd = 1, nyukinDate = 1, uketukeSbt = 0, seikyuGaku = 0, seikyuTensu = 0, raiinInfStatus = 0, seikyuAdjustFutan = 0, seikyuSinDate = 0;
            long raiinNo = 1, seqNo = -1;
            string nyukinCmt = "", seikyuDetail = "";
            bool isUpdated = false, isDelete = false;
            List<SyunoNyukinInputItem> syunoNyukinInputItems = new List<SyunoNyukinInputItem>()
            {
                new SyunoNyukinInputItem(nyukinKbn, raiinNo, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete)
            };

            mockIHpInfRepository.Setup(finder => finder.CheckHpId(hpId)).Returns((int hpId) => true);
            mockIUserRepository.Setup(finder => finder.CheckExistedUserId(hpId, userId)).Returns((int hpId, int userId) => true);
            mockIPatientInforRepository.Setup(finder => finder.CheckExistIdList(hpId, new List<long> { ptId })).Returns((int hpId, List<long> ptId) => true);
            var inputData = new SaveAccountDueListInputData(hpId, userId, ptId, sinDate, kaikeiTime, syunoNyukinInputItems, false);
            var result = saveAccountDueListInteractor.Handle(inputData);

            Assert.That(result.Status == SaveAccountDueListStatus.InvalidSeqNo);
        }

        [Test]
        public void TC_013_SaveAccountDueListInteractor_Handle_ValidateInputDatas_Return_InvalidNyukinKbn()
        {
            //Arrange
            var mockIAccountDueRepository = new Mock<IAccountDueRepository>();
            var mockIUserRepository = new Mock<IUserRepository>();
            var mockIHpInfRepository = new Mock<IHpInfRepository>();
            var mockIPatientInforRepository = new Mock<IPatientInforRepository>();
            var mockIEventProcessorService = new Mock<IEventProcessorService>();
            var mockIReceptionRepository = new Mock<IReceptionRepository>();
            var mockILoggingHandler = new Mock<ILoggingHandler>();

            var saveAccountDueListInteractor = new SaveAccountDueListInteractor(mockIAccountDueRepository.Object, mockIUserRepository.Object, mockIHpInfRepository.Object,
                                                                                mockIPatientInforRepository.Object, mockIEventProcessorService.Object, mockIReceptionRepository.Object, TenantProvider);

            Random random = new Random();

            int hpId = random.Next(999, 99999); ;
            int userId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int sinDate = random.Next(********, ********);
            string kaikeiTime = "";
            int nyukinKbn = 0, sortNo = 0, adjustFutan = 0, nyukinGaku = 0, paymentMethodCd = 1, nyukinDate = 1, uketukeSbt = 0, seikyuGaku = 0, seikyuTensu = 0, raiinInfStatus = 0, seikyuAdjustFutan = 0, seikyuSinDate = 0;
            long raiinNo = 1, seqNo = 0;
            string nyukinCmt = "", seikyuDetail = "";
            bool isUpdated = false, isDelete = false;
            List<SyunoNyukinInputItem> syunoNyukinInputItems = new List<SyunoNyukinInputItem>()
            {
                new SyunoNyukinInputItem(nyukinKbn, raiinNo, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete),
                new SyunoNyukinInputItem(nyukinKbn + 1, raiinNo, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete)
            };

            mockIHpInfRepository.Setup(finder => finder.CheckHpId(hpId)).Returns((int hpId) => true);
            mockIUserRepository.Setup(finder => finder.CheckExistedUserId(hpId, userId)).Returns((int hpId, int userId) => true);
            mockIPatientInforRepository.Setup(finder => finder.CheckExistIdList(hpId, new List<long> { ptId })).Returns((int hpId, List<long> ptId) => true);
            var inputData = new SaveAccountDueListInputData(hpId, userId, ptId, sinDate, kaikeiTime, syunoNyukinInputItems, false);
            var result = saveAccountDueListInteractor.Handle(inputData);

            Assert.That(result.Status == SaveAccountDueListStatus.InvalidNyukinKbn);
        }

        [Test]
        public void TC_014_SaveAccountDueListInteractor_Handle_ValidateInputDatas_Return_InvalidSeikyuGaku()
        {
            //Arrange
            var mockIAccountDueRepository = new Mock<IAccountDueRepository>();
            var mockIUserRepository = new Mock<IUserRepository>();
            var mockIHpInfRepository = new Mock<IHpInfRepository>();
            var mockIPatientInforRepository = new Mock<IPatientInforRepository>();
            var mockIEventProcessorService = new Mock<IEventProcessorService>();
            var mockIReceptionRepository = new Mock<IReceptionRepository>();
            var mockILoggingHandler = new Mock<ILoggingHandler>();

            var saveAccountDueListInteractor = new SaveAccountDueListInteractor(mockIAccountDueRepository.Object, mockIUserRepository.Object, mockIHpInfRepository.Object,
                                                                                mockIPatientInforRepository.Object, mockIEventProcessorService.Object, mockIReceptionRepository.Object, TenantProvider);

            Random random = new Random();

            int hpId = random.Next(999, 99999); ;
            int userId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int sinDate = random.Next(********, ********);
            string kaikeiTime = "";
            int nyukinKbn = 0, sortNo = 0, adjustFutan = 0, nyukinGaku = 0, paymentMethodCd = 1, nyukinDate = 1, uketukeSbt = 0, seikyuGaku = 0, seikyuTensu = 0, raiinInfStatus = 0, seikyuAdjustFutan = 0, seikyuSinDate = 0;
            long raiinNo = 1, seqNo = 0;
            string nyukinCmt = "", seikyuDetail = "";
            bool isUpdated = false, isDelete = false;
            List<SyunoNyukinInputItem> syunoNyukinInputItems = new List<SyunoNyukinInputItem>()
            {
                new SyunoNyukinInputItem(nyukinKbn, raiinNo, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete),
                new SyunoNyukinInputItem(nyukinKbn, raiinNo, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku + 1, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete)
            };

            mockIHpInfRepository.Setup(finder => finder.CheckHpId(hpId)).Returns((int hpId) => true);
            mockIUserRepository.Setup(finder => finder.CheckExistedUserId(hpId, userId)).Returns((int hpId, int userId) => true);
            mockIPatientInforRepository.Setup(finder => finder.CheckExistIdList(hpId, new List<long> { ptId })).Returns((int hpId, List<long> ptId) => true);
            var inputData = new SaveAccountDueListInputData(hpId, userId, ptId, sinDate, kaikeiTime, syunoNyukinInputItems, false);
            var result = saveAccountDueListInteractor.Handle(inputData);

            Assert.That(result.Status == SaveAccountDueListStatus.InvalidSeikyuGaku);
        }

        [Test]
        public void TC_015_SaveAccountDueListInteractor_Handle_ValidateInputDatas_Return_InvalidSeikyuAdjustFutan()
        {
            //Arrange
            var mockIAccountDueRepository = new Mock<IAccountDueRepository>();
            var mockIUserRepository = new Mock<IUserRepository>();
            var mockIHpInfRepository = new Mock<IHpInfRepository>();
            var mockIPatientInforRepository = new Mock<IPatientInforRepository>();
            var mockIEventProcessorService = new Mock<IEventProcessorService>();
            var mockIReceptionRepository = new Mock<IReceptionRepository>();
            var mockILoggingHandler = new Mock<ILoggingHandler>();

            var saveAccountDueListInteractor = new SaveAccountDueListInteractor(mockIAccountDueRepository.Object, mockIUserRepository.Object, mockIHpInfRepository.Object,
                                                                                mockIPatientInforRepository.Object, mockIEventProcessorService.Object, mockIReceptionRepository.Object, TenantProvider);

            Random random = new Random();

            int hpId = random.Next(999, 99999); ;
            int userId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int sinDate = random.Next(********, ********);
            string kaikeiTime = "";
            int nyukinKbn = 0, sortNo = 0, adjustFutan = 0, nyukinGaku = 0, paymentMethodCd = 1, nyukinDate = 1, uketukeSbt = 0, seikyuGaku = 0, seikyuTensu = 0, raiinInfStatus = 0, seikyuAdjustFutan = 0, seikyuSinDate = 0;
            long raiinNo = 1, seqNo = 0;
            string nyukinCmt = "", seikyuDetail = "";
            bool isUpdated = false, isDelete = false;
            List<SyunoNyukinInputItem> syunoNyukinInputItems = new List<SyunoNyukinInputItem>()
            {
                new SyunoNyukinInputItem(nyukinKbn, raiinNo, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete),
                new SyunoNyukinInputItem(nyukinKbn, raiinNo, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan + 1, seikyuSinDate, isDelete)
            };

            mockIHpInfRepository.Setup(finder => finder.CheckHpId(hpId)).Returns((int hpId) => true);
            mockIUserRepository.Setup(finder => finder.CheckExistedUserId(hpId, userId)).Returns((int hpId, int userId) => true);
            mockIPatientInforRepository.Setup(finder => finder.CheckExistIdList(hpId, new List<long> { ptId })).Returns((int hpId, List<long> ptId) => true);
            var inputData = new SaveAccountDueListInputData(hpId, userId, ptId, sinDate, kaikeiTime, syunoNyukinInputItems, false);
            var result = saveAccountDueListInteractor.Handle(inputData);

            Assert.That(result.Status == SaveAccountDueListStatus.InvalidSeikyuAdjustFutan);
        }

        [Test]
        public void TC_016_SaveAccountDueListInteractor_Handle_ConvertToListAccountDueModel_Return_NoItemChange()
        {
            //Arrange
            var mockIAccountDueRepository = new Mock<IAccountDueRepository>();
            var mockIUserRepository = new Mock<IUserRepository>();
            var mockIHpInfRepository = new Mock<IHpInfRepository>();
            var mockIPatientInforRepository = new Mock<IPatientInforRepository>();
            var mockIEventProcessorService = new Mock<IEventProcessorService>();
            var mockIReceptionRepository = new Mock<IReceptionRepository>();
            var mockILoggingHandler = new Mock<ILoggingHandler>();

            var saveAccountDueListInteractor = new SaveAccountDueListInteractor(mockIAccountDueRepository.Object, mockIUserRepository.Object, mockIHpInfRepository.Object,
                                                                                mockIPatientInforRepository.Object, mockIEventProcessorService.Object, mockIReceptionRepository.Object, TenantProvider);

            Random random = new Random();

            int hpId = random.Next(999, 99999); ;
            int userId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int sinDate = random.Next(********, ********);
            string kaikeiTime = "";
            int nyukinKbn = 0, nyukinjiTensu = 0, nyukinjiSeikyu = 0, newSeikyuTensu = 0, newAdjustFutan = 0, newSeikyuGaku = 0, sortNo = 0, adjustFutan = 0, nyukinGaku = 0, paymentMethodCd = 1, nyukinDate = 1, uketukeSbt = 0, seikyuGaku = 0, seikyuTensu = 0, raiinInfStatus = 0, seikyuAdjustFutan = 0, seikyuSinDate = 0;
            long raiinNo = 1, seqNo = 0;
            string nyukinCmt = "", seikyuDetail = "", newSeikyuDetail = "", nyukinjiDetail = "";
            bool isDelete = false;

            List<SyunoNyukinInputItem> syunoNyukinInputItems = new List<SyunoNyukinInputItem>();

            List<SyunoSeikyuModel> syunoSeikyuModels = new List<SyunoSeikyuModel>()
            {
                new SyunoSeikyuModel(hpId, ptId, sinDate, raiinNo, nyukinKbn, seikyuTensu, adjustFutan, seikyuGaku, seikyuDetail, newSeikyuTensu, newAdjustFutan, newSeikyuGaku, newSeikyuDetail)
            };

            List<SyunoNyukinModel> syunoNyukinModels = new List<SyunoNyukinModel>()
            {
                new SyunoNyukinModel(hpId, ptId, sinDate, raiinNo, seqNo, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, nyukinjiTensu, nyukinjiSeikyu, nyukinjiDetail)
            };

            List<AccountDueModel> listAccountDueModel = new List<AccountDueModel>()
            {
                new AccountDueModel(nyukinKbn, sortNo, raiinNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete)
            };

            List<AccountDueModel> listAccountDue = new List<AccountDueModel>()
            {
                new AccountDueModel(nyukinKbn, sortNo, raiinNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete)
            };

            mockIHpInfRepository.Setup(finder => finder.CheckHpId(hpId)).Returns((int hpId) => true);
            mockIUserRepository.Setup(finder => finder.CheckExistedUserId(hpId, userId)).Returns((int hpId, int userId) => true);
            mockIPatientInforRepository.Setup(finder => finder.CheckExistIdList(hpId, new List<long> { ptId })).Returns((int hpId, List<long> ptId) => true);
            mockIAccountDueRepository.Setup(finder => finder.GetListSyunoSeikyuModel(hpId, new List<long> { raiinNo })).Returns((int hpId, List<long> listRaiinNo) => syunoSeikyuModels);
            mockIAccountDueRepository.Setup(finder => finder.GetListSyunoNyukinModel(hpId, new List<long> { raiinNo })).Returns((int hpId, List<long> listRaiinNo) => syunoNyukinModels);
            var inputData = new SaveAccountDueListInputData(hpId, userId, ptId, sinDate, kaikeiTime, syunoNyukinInputItems, false);

            mockIAccountDueRepository.Setup(x => x.SaveAccountDueList(It.IsAny<int>(), It.IsAny<long>(), It.IsAny<int>(), It.IsAny<int>(), It.IsAny<List<AccountDueModel>>(), It.IsAny<Dictionary<long, RaiinInfModel>>(), It.IsAny<string>()))
            .Returns((int input1, long input2, int input3, int input4, List<AccountDueModel> input5, Dictionary<long, RaiinInfModel> input6, string input7) => listAccountDue);

            var result = saveAccountDueListInteractor.Handle(inputData);

            Assert.That(result.Status == SaveAccountDueListStatus.NoItemChange);
        }
        #endregion

        #region ValidateInvalidNyukinKbn
        /// <summary>
        ///  NyukinKbn = 1
        /// </summary>
        [Test]
        public void TC_017_SaveAccountDueListInteractor_Handle_ValidateInvalidNyukinKbn_NyukinKbn_1_Return_InvalidNyukinKbn()
        {
            //Arrange
            var mockIAccountDueRepository = new Mock<IAccountDueRepository>();
            var mockIUserRepository = new Mock<IUserRepository>();
            var mockIHpInfRepository = new Mock<IHpInfRepository>();
            var mockIPatientInforRepository = new Mock<IPatientInforRepository>();
            var mockIEventProcessorService = new Mock<IEventProcessorService>();
            var mockIReceptionRepository = new Mock<IReceptionRepository>();
            var mockILoggingHandler = new Mock<ILoggingHandler>();

            var saveAccountDueListInteractor = new SaveAccountDueListInteractor(mockIAccountDueRepository.Object, mockIUserRepository.Object, mockIHpInfRepository.Object,
                                                                                mockIPatientInforRepository.Object, mockIEventProcessorService.Object, mockIReceptionRepository.Object, TenantProvider);

            Random random = new Random();

            int hpId = random.Next(999, 99999); ;
            int userId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int sinDate = random.Next(********, ********);
            string kaikeiTime = "";
            int nyukinKbn = 1, nyukinjiTensu = 0, nyukinjiSeikyu = 0, newSeikyuTensu = 0, newAdjustFutan = 0, newSeikyuGaku = 0, sortNo = 0, adjustFutan = 0, nyukinGaku = 0, paymentMethodCd = 1, nyukinDate = 1, uketukeSbt = 0, seikyuGaku = 0, seikyuTensu = 0, raiinInfStatus = 0, seikyuAdjustFutan = 0, seikyuSinDate = 0;
            long raiinNo = 1, seqNo = 0;
            string nyukinCmt = "", seikyuDetail = "", newSeikyuDetail = "", nyukinjiDetail = "";
            bool isDelete = false, isUpdated = true;

            List<SyunoNyukinInputItem> syunoNyukinInputItems = new List<SyunoNyukinInputItem>()
            {
                new SyunoNyukinInputItem(nyukinKbn, raiinNo, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete),
                new SyunoNyukinInputItem(nyukinKbn, raiinNo+1, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan + 1, seikyuSinDate, isDelete)
            };

            List<SyunoSeikyuModel> syunoSeikyuModels = new List<SyunoSeikyuModel>()
            {
                new SyunoSeikyuModel(hpId, ptId, sinDate, raiinNo, nyukinKbn, seikyuTensu, adjustFutan, seikyuGaku + 1, seikyuDetail, newSeikyuTensu, newAdjustFutan, newSeikyuGaku, newSeikyuDetail)
            };

            List<SyunoNyukinModel> syunoNyukinModels = new List<SyunoNyukinModel>()
            {
                new SyunoNyukinModel(hpId, ptId, sinDate, raiinNo, seqNo, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, nyukinjiTensu, nyukinjiSeikyu, nyukinjiDetail)
            };

            Dictionary<long, RaiinInfModel> dictRaiinInfModel = new Dictionary<long, RaiinInfModel>(){
                [1] = new RaiinInfModel(hpId, ptId,sinDate,raiinNo,new ReserveDetailModel(0,0)),
                [2] = new RaiinInfModel(hpId, ptId,sinDate,raiinNo,new ReserveDetailModel(0,0))
            };

            List<AccountDueModel> listAccountDue = new List<AccountDueModel>()
            {
                new AccountDueModel(nyukinKbn, sortNo, raiinNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete)
            };

            Dictionary<int, string> paymentMethodModel = new Dictionary<int, string>(){
                [1] = "現金"
            };

            mockIHpInfRepository.Setup(finder => finder.CheckHpId(hpId)).Returns((int hpId) => true);
            mockIUserRepository.Setup(finder => finder.CheckExistedUserId(hpId, userId)).Returns((int hpId, int userId) => true);
            mockIPatientInforRepository.Setup(finder => finder.CheckExistIdList(hpId, new List<long> { ptId })).Returns((int hpId, List<long> ptId) => true);
            mockIAccountDueRepository.Setup(finder => finder.GetListSyunoSeikyuModel(It.IsAny<int>(), It.IsAny<List<long>>())).Returns((int hpId, List<long> listRaiinNo) => syunoSeikyuModels);
            mockIAccountDueRepository.Setup(finder => finder.GetListSyunoNyukinModel(It.IsAny<int>(), It.IsAny<List<long>>())).Returns((int hpId, List<long> listRaiinNo) => syunoNyukinModels);
            mockIAccountDueRepository.Setup(finder => finder.GetDictRaiinInfModel(It.IsAny<int>(), It.IsAny<List<long>>())).Returns((int hpId, List<long> listRaiinNo) => dictRaiinInfModel);
            mockIAccountDueRepository.Setup(finder => finder.GetPaymentMethod(It.IsAny<int>())).Returns((int paymentMethodCd) => paymentMethodModel);
            var inputData = new SaveAccountDueListInputData(hpId, userId, ptId, sinDate, kaikeiTime, syunoNyukinInputItems,false);

            mockIAccountDueRepository.Setup(x => x.SaveAccountDueList(It.IsAny<int>(), It.IsAny<long>(), It.IsAny<int>(), It.IsAny<int>(), It.IsAny<List<AccountDueModel>>(), It.IsAny<Dictionary<long, RaiinInfModel>>(), It.IsAny<string>()))
            .Returns((int input1, long input2, int input3, int input4, List<AccountDueModel> input5, Dictionary<long, RaiinInfModel> input6, string input7) => listAccountDue);

            var result = saveAccountDueListInteractor.Handle(inputData);

            Assert.That(result.Status == SaveAccountDueListStatus.InvalidNyukinKbn);
        }

        /// <summary>
        ///  NyukinKbn = 2
        /// </summary>
        [Test]
        public void TC_018_SaveAccountDueListInteractor_Handle_ValidateInvalidNyukinKbn_NyukinKbn_2_Return_InvalidNyukinKbn()
        {
            //Arrange
            var mockIAccountDueRepository = new Mock<IAccountDueRepository>();
            var mockIUserRepository = new Mock<IUserRepository>();
            var mockIHpInfRepository = new Mock<IHpInfRepository>();
            var mockIPatientInforRepository = new Mock<IPatientInforRepository>();
            var mockIEventProcessorService = new Mock<IEventProcessorService>();
            var mockIReceptionRepository = new Mock<IReceptionRepository>();
            var mockILoggingHandler = new Mock<ILoggingHandler>();

            var saveAccountDueListInteractor = new SaveAccountDueListInteractor(mockIAccountDueRepository.Object, mockIUserRepository.Object, mockIHpInfRepository.Object,
                                                                                mockIPatientInforRepository.Object, mockIEventProcessorService.Object, mockIReceptionRepository.Object, TenantProvider);

            Random random = new Random();

            int hpId = random.Next(999, 99999); ;
            int userId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int sinDate = random.Next(********, ********);
            string kaikeiTime = "";
            int nyukinKbn = 2, nyukinjiTensu = 0, nyukinjiSeikyu = 0, newSeikyuTensu = 0, newAdjustFutan = 0, newSeikyuGaku = 0, sortNo = 0, adjustFutan = 1, nyukinGaku = 0, paymentMethodCd = 1, nyukinDate = 1, uketukeSbt = 0, seikyuGaku = 1, seikyuTensu = 0, raiinInfStatus = 0, seikyuAdjustFutan = 0, seikyuSinDate = 0;
            long raiinNo = 1, seqNo = 0;
            string nyukinCmt = "", seikyuDetail = "", newSeikyuDetail = "", nyukinjiDetail = "";
            bool isDelete = false, isUpdated = true;

            List<SyunoNyukinInputItem> syunoNyukinInputItems = new List<SyunoNyukinInputItem>()
            {
                new SyunoNyukinInputItem(nyukinKbn, raiinNo, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete),
                new SyunoNyukinInputItem(nyukinKbn, raiinNo+1, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan + 1, seikyuSinDate, isDelete)
            };

            List<SyunoSeikyuModel> syunoSeikyuModels = new List<SyunoSeikyuModel>()
            {
                new SyunoSeikyuModel(hpId, ptId, sinDate, raiinNo, nyukinKbn, seikyuTensu, adjustFutan, seikyuGaku, seikyuDetail, newSeikyuTensu, newAdjustFutan, newSeikyuGaku, newSeikyuDetail)
            };

            List<SyunoNyukinModel> syunoNyukinModels = new List<SyunoNyukinModel>()
            {
                new SyunoNyukinModel(hpId, ptId, sinDate, raiinNo, seqNo, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, nyukinjiTensu, nyukinjiSeikyu, nyukinjiDetail)
            };

            Dictionary<long, RaiinInfModel> dictRaiinInfModel = new Dictionary<long, RaiinInfModel>(){
                [1] = new RaiinInfModel(hpId, ptId,sinDate,raiinNo,new ReserveDetailModel(0,0)),
                [2] = new RaiinInfModel(hpId, ptId,sinDate,raiinNo,new ReserveDetailModel(0,0))
            };

            List<AccountDueModel> listAccountDue = new List<AccountDueModel>()
            {
                new AccountDueModel(nyukinKbn, sortNo, raiinNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete)
            };

            Dictionary<int, string> paymentMethodModel = new Dictionary<int, string>(){
                [1] = "現金",
                [2] = "クレジット"
            };

            mockIHpInfRepository.Setup(finder => finder.CheckHpId(hpId)).Returns((int hpId) => true);
            mockIUserRepository.Setup(finder => finder.CheckExistedUserId(hpId, userId)).Returns((int hpId, int userId) => true);
            mockIPatientInforRepository.Setup(finder => finder.CheckExistIdList(hpId, new List<long> { ptId })).Returns((int hpId, List<long> ptId) => true);
            mockIAccountDueRepository.Setup(finder => finder.GetListSyunoSeikyuModel(It.IsAny<int>(), It.IsAny<List<long>>())).Returns((int hpId, List<long> listRaiinNo) => syunoSeikyuModels);
            mockIAccountDueRepository.Setup(finder => finder.GetListSyunoNyukinModel(It.IsAny<int>(), It.IsAny<List<long>>())).Returns((int hpId, List<long> listRaiinNo) => syunoNyukinModels);
            mockIAccountDueRepository.Setup(finder => finder.GetDictRaiinInfModel(It.IsAny<int>(), It.IsAny<List<long>>())).Returns((int hpId, List<long> listRaiinNo) => dictRaiinInfModel);
            mockIAccountDueRepository.Setup(finder => finder.GetPaymentMethod(It.IsAny<int>())).Returns((int paymentMethodCd) => paymentMethodModel);
            var inputData = new SaveAccountDueListInputData(hpId, userId, ptId, sinDate, kaikeiTime, syunoNyukinInputItems, false);

            mockIAccountDueRepository.Setup(x => x.SaveAccountDueList(It.IsAny<int>(), It.IsAny<long>(), It.IsAny<int>(), It.IsAny<int>(), It.IsAny<List<AccountDueModel>>(), It.IsAny<Dictionary<long, RaiinInfModel>>(), It.IsAny<string>()))
            .Returns((int input1, long input2, int input3, int input4, List<AccountDueModel> input5, Dictionary<long, RaiinInfModel> input6, string input7) => listAccountDue);

            var result = saveAccountDueListInteractor.Handle(inputData);

            Assert.That(result.Status == SaveAccountDueListStatus.InvalidNyukinKbn);
        }

        /// <summary>
        /// NyukinKbn = 3
        /// </summary>
        [Test]
        public void TC_019_SaveAccountDueListInteractor_Handle_ValidateInvalidNyukinKbn_NyukinKbn_3_Return_InvalidNyukinKbn()
        {
            //Arrange
            var mockIAccountDueRepository = new Mock<IAccountDueRepository>();
            var mockIUserRepository = new Mock<IUserRepository>();
            var mockIHpInfRepository = new Mock<IHpInfRepository>();
            var mockIPatientInforRepository = new Mock<IPatientInforRepository>();
            var mockIEventProcessorService = new Mock<IEventProcessorService>();
            var mockIReceptionRepository = new Mock<IReceptionRepository>();
            var mockILoggingHandler = new Mock<ILoggingHandler>();

            var saveAccountDueListInteractor = new SaveAccountDueListInteractor(mockIAccountDueRepository.Object, mockIUserRepository.Object, mockIHpInfRepository.Object,
                                                                                mockIPatientInforRepository.Object, mockIEventProcessorService.Object, mockIReceptionRepository.Object, TenantProvider);

            Random random = new Random();

            int hpId = random.Next(999, 99999); ;
            int userId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int sinDate = random.Next(********, ********);
            string kaikeiTime = "";
            int nyukinKbn = 3, nyukinjiTensu = 0, nyukinjiSeikyu = 0, newSeikyuTensu = 0, newAdjustFutan = 0, newSeikyuGaku = 0, sortNo = 0, adjustFutan = 0, nyukinGaku = 0, paymentMethodCd = 1, nyukinDate = 1, uketukeSbt = 0, seikyuGaku = 1, seikyuTensu = 0, raiinInfStatus = 0, seikyuAdjustFutan = 0, seikyuSinDate = 0;
            long raiinNo = 1, seqNo = 0;
            string nyukinCmt = "", seikyuDetail = "", newSeikyuDetail = "", nyukinjiDetail = "";
            bool isDelete = false, isUpdated = true;

            List<SyunoNyukinInputItem> syunoNyukinInputItems = new List<SyunoNyukinInputItem>()
            {
                new SyunoNyukinInputItem(nyukinKbn, raiinNo, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete),
                new SyunoNyukinInputItem(nyukinKbn, raiinNo + 1, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan + 1, seikyuSinDate, isDelete)
            };

            List<SyunoSeikyuModel> syunoSeikyuModels = new List<SyunoSeikyuModel>()
            {
                new SyunoSeikyuModel(hpId, ptId, sinDate, raiinNo, nyukinKbn, seikyuTensu, adjustFutan, seikyuGaku, seikyuDetail, newSeikyuTensu, newAdjustFutan, newSeikyuGaku, newSeikyuDetail)
            };

            List<SyunoNyukinModel> syunoNyukinModels = new List<SyunoNyukinModel>()
            {
                new SyunoNyukinModel(hpId, ptId, sinDate, raiinNo, seqNo, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, nyukinjiTensu, nyukinjiSeikyu, nyukinjiDetail)
            };

            Dictionary<long, RaiinInfModel> dictRaiinInfModel = new Dictionary<long, RaiinInfModel>(){
                [1] = new RaiinInfModel(hpId, ptId,sinDate,raiinNo,new ReserveDetailModel(0,0)),
                [2] = new RaiinInfModel(hpId, ptId,sinDate,raiinNo,new ReserveDetailModel(0,0))
            };

            List<AccountDueModel> listAccountDue = new List<AccountDueModel>()
            {
                new AccountDueModel(nyukinKbn, sortNo, raiinNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete)
            };

            Dictionary<int, string> paymentMethodModel = new Dictionary<int, string>(){
                [1] = "現金",
                [2] = "クレジット"
            };

            mockIHpInfRepository.Setup(finder => finder.CheckHpId(hpId)).Returns((int hpId) => true);
            mockIUserRepository.Setup(finder => finder.CheckExistedUserId(hpId, userId)).Returns((int hpId, int userId) => true);
            mockIPatientInforRepository.Setup(finder => finder.CheckExistIdList(hpId, new List<long> { ptId })).Returns((int hpId, List<long> ptId) => true);
            mockIAccountDueRepository.Setup(finder => finder.GetListSyunoSeikyuModel(It.IsAny<int>(), It.IsAny<List<long>>())).Returns((int hpId, List<long> listRaiinNo) => syunoSeikyuModels);
            mockIAccountDueRepository.Setup(finder => finder.GetListSyunoNyukinModel(It.IsAny<int>(), It.IsAny<List<long>>())).Returns((int hpId, List<long> listRaiinNo) => syunoNyukinModels);
            mockIAccountDueRepository.Setup(finder => finder.GetDictRaiinInfModel(It.IsAny<int>(), It.IsAny<List<long>>())).Returns((int hpId, List<long> listRaiinNo) => dictRaiinInfModel);
            mockIAccountDueRepository.Setup(finder => finder.GetPaymentMethod(It.IsAny<int>())).Returns((int paymentMethodCd) => paymentMethodModel);
            var inputData = new SaveAccountDueListInputData(hpId, userId, ptId, sinDate, kaikeiTime, syunoNyukinInputItems, false);

            mockIAccountDueRepository.Setup(x => x.SaveAccountDueList(It.IsAny<int>(), It.IsAny<long>(), It.IsAny<int>(), It.IsAny<int>(), It.IsAny<List<AccountDueModel>>(), It.IsAny<Dictionary<long, RaiinInfModel>>(), It.IsAny<string>()))
            .Returns((int input1, long input2, int input3, int input4, List<AccountDueModel> input5, Dictionary<long, RaiinInfModel> input6, string input7) => listAccountDue);

            var result = saveAccountDueListInteractor.Handle(inputData);

            Assert.That(result.Status == SaveAccountDueListStatus.InvalidNyukinKbn);
        }
        #endregion

        #region Covert Function Handle
        [Test]
        public void TC_020_SaveAccountDueListInteractor_Handle_ValidateInvalidNyukinKbn_Return_Any_AccountDueList()
        {
            //Arrange
            var mockIAccountDueRepository = new Mock<IAccountDueRepository>();
            var mockIUserRepository = new Mock<IUserRepository>();
            var mockIHpInfRepository = new Mock<IHpInfRepository>();
            var mockIPatientInforRepository = new Mock<IPatientInforRepository>();
            var mockIEventProcessorService = new Mock<IEventProcessorService>();
            var mockIReceptionRepository = new Mock<IReceptionRepository>();
            var mockILoggingHandler = new Mock<ILoggingHandler>();

            var saveAccountDueListInteractor = new SaveAccountDueListInteractor(mockIAccountDueRepository.Object, mockIUserRepository.Object, mockIHpInfRepository.Object,
                                                                                mockIPatientInforRepository.Object, mockIEventProcessorService.Object, mockIReceptionRepository.Object, TenantProvider);

            Random random = new Random();

            int hpId = random.Next(999, 99999); ;
            int userId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int sinDate = random.Next(********, ********);
            string kaikeiTime = "";
            int nyukinKbn = 4, nyukinjiTensu = 0, nyukinjiSeikyu = 0, newSeikyuTensu = 0, newAdjustFutan = 0, newSeikyuGaku = 0, sortNo = 0, adjustFutan = 0, nyukinGaku = 0, paymentMethodCd = 1, nyukinDate = 1, uketukeSbt = 0, seikyuGaku = 1, seikyuTensu = 0, raiinInfStatus = 0, seikyuAdjustFutan = 0, seikyuSinDate = 0;
            long raiinNo = 1, seqNo = 0;
            string nyukinCmt = "", seikyuDetail = "", newSeikyuDetail = "", nyukinjiDetail = "";
            bool isDelete = false, isUpdated = true;

            List<SyunoNyukinInputItem> syunoNyukinInputItems = new List<SyunoNyukinInputItem>()
            {
                new SyunoNyukinInputItem(nyukinKbn, raiinNo, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete),
                new SyunoNyukinInputItem(nyukinKbn, raiinNo + 1, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan + 1, seikyuSinDate, isDelete),
                new SyunoNyukinInputItem(nyukinKbn, raiinNo, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete),
                new SyunoNyukinInputItem(nyukinKbn, raiinNo + 1, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan + 1, seikyuSinDate, isDelete)
            };

            List<SyunoSeikyuModel> syunoSeikyuModels = new List<SyunoSeikyuModel>()
            {
                new SyunoSeikyuModel(hpId, ptId, sinDate, raiinNo, nyukinKbn, seikyuTensu, adjustFutan, seikyuGaku, seikyuDetail, newSeikyuTensu, newAdjustFutan, newSeikyuGaku, newSeikyuDetail)
            };

            List<SyunoNyukinModel> syunoNyukinModels = new List<SyunoNyukinModel>()
            {
                new SyunoNyukinModel(hpId, ptId, sinDate, raiinNo, seqNo, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, nyukinjiTensu, nyukinjiSeikyu, nyukinjiDetail)
            };

            Dictionary<long, RaiinInfModel> dictRaiinInfModel = new Dictionary<long, RaiinInfModel>(){
                [1] = new RaiinInfModel(hpId, ptId,sinDate,raiinNo,new ReserveDetailModel(0,0)),
                [2] = new RaiinInfModel(hpId, ptId,sinDate,raiinNo,new ReserveDetailModel(0,0))
            };

            List<AccountDueModel> listAccountDue = new List<AccountDueModel>()
            {
                new AccountDueModel(nyukinKbn, sortNo, raiinNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete)
            };

            Dictionary<int, string> paymentMethodModel = new Dictionary<int, string>(){
                [1] = "現金",
                [2] = "クレジット"
            };

            mockIHpInfRepository.Setup(finder => finder.CheckHpId(hpId)).Returns((int hpId) => true);
            mockIUserRepository.Setup(finder => finder.CheckExistedUserId(hpId, userId)).Returns((int hpId, int userId) => true);
            mockIPatientInforRepository.Setup(finder => finder.CheckExistIdList(hpId, new List<long> { ptId })).Returns((int hpId, List<long> ptId) => true);
            mockIAccountDueRepository.Setup(finder => finder.GetListSyunoSeikyuModel(It.IsAny<int>(), It.IsAny<List<long>>())).Returns((int hpId, List<long> listRaiinNo) => syunoSeikyuModels);
            mockIAccountDueRepository.Setup(finder => finder.GetListSyunoNyukinModel(It.IsAny<int>(), It.IsAny<List<long>>())).Returns((int hpId, List<long> listRaiinNo) => syunoNyukinModels);
            mockIAccountDueRepository.Setup(finder => finder.GetDictRaiinInfModel(It.IsAny<int>(), It.IsAny<List<long>>())).Returns((int hpId, List<long> listRaiinNo) => dictRaiinInfModel);
            mockIAccountDueRepository.Setup(finder => finder.GetPaymentMethod(It.IsAny<int>())).Returns((int paymentMethodCd) => paymentMethodModel);
            var inputData = new SaveAccountDueListInputData(hpId, userId, ptId, sinDate, kaikeiTime, syunoNyukinInputItems, false);

            mockIAccountDueRepository.Setup(x => x.SaveAccountDueList(It.IsAny<int>(), It.IsAny<long>(), It.IsAny<int>(), It.IsAny<int>(), It.IsAny<List<AccountDueModel>>(), It.IsAny<Dictionary<long, RaiinInfModel>>(), It.IsAny<string>()))
            .Returns((int input1, long input2, int input3, int input4, List<AccountDueModel> input5, Dictionary<long, RaiinInfModel> input6, string input7) => listAccountDue);

            var result = saveAccountDueListInteractor.Handle(inputData);

            Assert.That(result.AccountDueModel.AccountDueList.Any(x => x.RaiinNo == raiinNo));
        }

        [Test]
        public void TC_021_SaveAccountDueListInteractor_Handle_ValidateInvalidNyukinKbn_Return_False()
        {
            //Arrange
            var mockIAccountDueRepository = new Mock<IAccountDueRepository>();
            var mockIUserRepository = new Mock<IUserRepository>();
            var mockIHpInfRepository = new Mock<IHpInfRepository>();
            var mockIPatientInforRepository = new Mock<IPatientInforRepository>();
            var mockIEventProcessorService = new Mock<IEventProcessorService>();
            var mockIReceptionRepository = new Mock<IReceptionRepository>();
            var mockILoggingHandler = new Mock<ILoggingHandler>();

            var saveAccountDueListInteractor = new SaveAccountDueListInteractor(mockIAccountDueRepository.Object, mockIUserRepository.Object, mockIHpInfRepository.Object,
                                                                                mockIPatientInforRepository.Object, mockIEventProcessorService.Object, mockIReceptionRepository.Object, TenantProvider);

            Random random = new Random();

            int hpId = random.Next(999, 99999); ;
            int userId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int sinDate = random.Next(********, ********);
            string kaikeiTime = "";
            int nyukinKbn = 4, nyukinjiTensu = 0, nyukinjiSeikyu = 0, newSeikyuTensu = 0, newAdjustFutan = 0, newSeikyuGaku = 0, sortNo = 0, adjustFutan = 0, nyukinGaku = 0, paymentMethodCd = 1, nyukinDate = 1, uketukeSbt = 0, seikyuGaku = 1, seikyuTensu = 0, raiinInfStatus = 0, seikyuAdjustFutan = 0, seikyuSinDate = 0;
            long raiinNo = 1, seqNo = 0;
            string nyukinCmt = "", seikyuDetail = "", newSeikyuDetail = "", nyukinjiDetail = "";
            bool isDelete = false, isUpdated = true;

            List<SyunoNyukinInputItem> syunoNyukinInputItems = new List<SyunoNyukinInputItem>()
            {
                new SyunoNyukinInputItem(nyukinKbn, raiinNo, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete),
                new SyunoNyukinInputItem(nyukinKbn, raiinNo + 1, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan + 1, seikyuSinDate, isDelete),
                new SyunoNyukinInputItem(nyukinKbn, raiinNo, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete),
                new SyunoNyukinInputItem(nyukinKbn, raiinNo + 1, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan + 1, seikyuSinDate, isDelete)
            };

            List<SyunoSeikyuModel> syunoSeikyuModels = new List<SyunoSeikyuModel>()
            {
                new SyunoSeikyuModel(hpId, ptId, sinDate, raiinNo, nyukinKbn, seikyuTensu, adjustFutan, seikyuGaku, seikyuDetail, newSeikyuTensu, newAdjustFutan, newSeikyuGaku, newSeikyuDetail)
            };

            List<SyunoNyukinModel> syunoNyukinModels = new List<SyunoNyukinModel>()
            {
                new SyunoNyukinModel(hpId, ptId, sinDate, raiinNo, seqNo, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, nyukinjiTensu, nyukinjiSeikyu, nyukinjiDetail)
            };

            Dictionary<long, RaiinInfModel> dictRaiinInfModel = new Dictionary<long, RaiinInfModel>(){
                [1] = new RaiinInfModel(hpId, ptId,sinDate,raiinNo,new ReserveDetailModel(0,0)),
                [2] = new RaiinInfModel(hpId, ptId,sinDate,raiinNo,new ReserveDetailModel(0,0))
            };

            List<AccountDueModel> listAccountDue = new List<AccountDueModel>();

            Dictionary<int, string> paymentMethodModel = new Dictionary<int, string>(){
                [1] = "現金",
                [2] = "クレジット"
            };

            mockIHpInfRepository.Setup(finder => finder.CheckHpId(hpId)).Returns((int hpId) => true);
            mockIUserRepository.Setup(finder => finder.CheckExistedUserId(hpId, userId)).Returns((int hpId, int userId) => true);
            mockIPatientInforRepository.Setup(finder => finder.CheckExistIdList(hpId, new List<long> { ptId })).Returns((int hpId, List<long> ptId) => true);
            mockIAccountDueRepository.Setup(finder => finder.GetListSyunoSeikyuModel(It.IsAny<int>(), It.IsAny<List<long>>())).Returns((int hpId, List<long> listRaiinNo) => syunoSeikyuModels);
            mockIAccountDueRepository.Setup(finder => finder.GetListSyunoNyukinModel(It.IsAny<int>(), It.IsAny<List<long>>())).Returns((int hpId, List<long> listRaiinNo) => syunoNyukinModels);
            mockIAccountDueRepository.Setup(finder => finder.GetDictRaiinInfModel(It.IsAny<int>(), It.IsAny<List<long>>())).Returns((int hpId, List<long> listRaiinNo) => dictRaiinInfModel);
            mockIAccountDueRepository.Setup(finder => finder.GetPaymentMethod(It.IsAny<int>())).Returns((int paymentMethodCd) => paymentMethodModel);
            var inputData = new SaveAccountDueListInputData(hpId, userId, ptId, sinDate, kaikeiTime, syunoNyukinInputItems, false);

            mockIAccountDueRepository.Setup(x => x.SaveAccountDueList(It.IsAny<int>(), It.IsAny<long>(), It.IsAny<int>(), It.IsAny<int>(), It.IsAny<List<AccountDueModel>>(), It.IsAny<Dictionary<long, RaiinInfModel>>(), It.IsAny<string>()))
            .Returns((int input1, long input2, int input3, int input4, List<AccountDueModel> input5, Dictionary<long, RaiinInfModel> input6, string input7) => listAccountDue);

            // Act
            var result = saveAccountDueListInteractor.Handle(inputData);

            //Assert
            Assert.That(result.Status == SaveAccountDueListStatus.Failed);
        }

        [Test]
        public void TC_022_SaveAccountDueListInteractor_Handle_ValidateInputParam_Bulk_NotSelectFCO()
        {
            //Arrange
            var mockIAccountDueRepository = new Mock<IAccountDueRepository>();
            var mockIUserRepository = new Mock<IUserRepository>();
            var mockIHpInfRepository = new Mock<IHpInfRepository>();
            var mockIPatientInforRepository = new Mock<IPatientInforRepository>();
            var mockIEventProcessorService = new Mock<IEventProcessorService>();
            var mockIReceptionRepository = new Mock<IReceptionRepository>();
            var mockILoggingHandler = new Mock<ILoggingHandler>();

            var saveAccountDueListInteractor = new SaveAccountDueListInteractor(mockIAccountDueRepository.Object, mockIUserRepository.Object, mockIHpInfRepository.Object,
                                                                                mockIPatientInforRepository.Object, mockIEventProcessorService.Object, mockIReceptionRepository.Object, TenantProvider);

            Random random = new Random();

            int hpId = random.Next(999, 99999); ;
            int userId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int sinDate = random.Next(********, ********);
            string kaikeiTime = "";
            int nyukinKbn = 1, nyukinjiTensu = 0, nyukinjiSeikyu = 0, newSeikyuTensu = 0, newAdjustFutan = 0, newSeikyuGaku = 0, sortNo = 0, adjustFutan = 0, nyukinGaku = 0, paymentMethodCd = 1, nyukinDate = 1, uketukeSbt = 0, seikyuGaku = 1, seikyuTensu = 0, raiinInfStatus = 0, seikyuAdjustFutan = 0, seikyuSinDate = 0;
            int paymentMethodFCO = 6;
            long raiinNo = 1, seqNo = 0;
            bool isBulk = true;
            string nyukinCmt = "", seikyuDetail = "", newSeikyuDetail = "", nyukinjiDetail = "";
            bool isDelete = false, isUpdated = true;

            List<SyunoNyukinInputItem> syunoNyukinInputItems = new List<SyunoNyukinInputItem>()
            {
                new SyunoNyukinInputItem(nyukinKbn, raiinNo, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete),
                new SyunoNyukinInputItem(nyukinKbn, raiinNo + 1, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan + 1, seikyuSinDate, isDelete),
                new SyunoNyukinInputItem(nyukinKbn, raiinNo, sortNo, adjustFutan, nyukinGaku, paymentMethodFCO, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete),
                new SyunoNyukinInputItem(nyukinKbn, raiinNo + 1, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan + 1, seikyuSinDate, isDelete)
            };

            List<SyunoSeikyuModel> syunoSeikyuModels = new List<SyunoSeikyuModel>()
            {
                new SyunoSeikyuModel(hpId, ptId, sinDate, raiinNo, nyukinKbn, seikyuTensu, adjustFutan, seikyuGaku, seikyuDetail, newSeikyuTensu, newAdjustFutan, newSeikyuGaku, newSeikyuDetail)
            };

            List<SyunoNyukinModel> syunoNyukinModels = new List<SyunoNyukinModel>()
            {
                new SyunoNyukinModel(hpId, ptId, sinDate, raiinNo, seqNo, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, nyukinjiTensu, nyukinjiSeikyu, nyukinjiDetail)
            };

            Dictionary<long, RaiinInfModel> dictRaiinInfModel = new Dictionary<long, RaiinInfModel>(){
                [1] = new RaiinInfModel(hpId, ptId,sinDate,raiinNo,new ReserveDetailModel(0,0)),
                [2] = new RaiinInfModel(hpId, ptId,sinDate,raiinNo,new ReserveDetailModel(0,0))
            };

            List<AccountDueModel> listAccountDue = new List<AccountDueModel>()
            {
                new AccountDueModel(nyukinKbn, sortNo, raiinNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete)
            };

            Dictionary<int, string> paymentMethodModel = new Dictionary<int, string>(){
                [1] = "現金",
                [2] = "クレジット",
                [6] = "FCO連携"
            };

            mockIHpInfRepository.Setup(finder => finder.CheckHpId(hpId)).Returns((int hpId) => true);
            mockIUserRepository.Setup(finder => finder.CheckExistedUserId(hpId, userId)).Returns((int hpId, int userId) => true);
            mockIPatientInforRepository.Setup(finder => finder.CheckExistIdList(hpId, new List<long> { ptId })).Returns((int hpId, List<long> ptId) => true);
            mockIAccountDueRepository.Setup(finder => finder.GetListSyunoSeikyuModel(It.IsAny<int>(), It.IsAny<List<long>>())).Returns((int hpId, List<long> listRaiinNo) => syunoSeikyuModels);
            mockIAccountDueRepository.Setup(finder => finder.GetListSyunoNyukinModel(It.IsAny<int>(), It.IsAny<List<long>>())).Returns((int hpId, List<long> listRaiinNo) => syunoNyukinModels);
            mockIAccountDueRepository.Setup(finder => finder.GetDictRaiinInfModel(It.IsAny<int>(), It.IsAny<List<long>>())).Returns((int hpId, List<long> listRaiinNo) => dictRaiinInfModel);
            mockIAccountDueRepository.Setup(finder => finder.GetPaymentMethod(It.IsAny<int>())).Returns((int paymentMethodCd) => paymentMethodModel);
            var inputData = new SaveAccountDueListInputData(hpId, userId, ptId, sinDate, kaikeiTime, syunoNyukinInputItems, isBulk);

            mockIAccountDueRepository.Setup(x => x.SaveAccountDueList(It.IsAny<int>(), It.IsAny<long>(), It.IsAny<int>(), It.IsAny<int>(), It.IsAny<List<AccountDueModel>>(), It.IsAny<Dictionary<long, RaiinInfModel>>(), It.IsAny<string>()))
            .Returns((int input1, long input2, int input3, int input4, List<AccountDueModel> input5, Dictionary<long, RaiinInfModel> input6, string input7) => listAccountDue);

            // Act
            var result = saveAccountDueListInteractor.Handle(inputData);

            //Assert
            Assert.That(result.Status, Is.EqualTo(SaveAccountDueListStatus.NotAllowedBulkPaymentMethodSelectFCO));
        }

        [Test]
        public void TC_023_SaveAccountDueListInteractor_Handle_ValidateInvalidNyukinKbn_PaymentMethod_Change_NotOtherToFCO()
        {
            //Arrange
            var mockIAccountDueRepository = new Mock<IAccountDueRepository>();
            var mockIUserRepository = new Mock<IUserRepository>();
            var mockIHpInfRepository = new Mock<IHpInfRepository>();
            var mockIPatientInforRepository = new Mock<IPatientInforRepository>();
            var mockIEventProcessorService = new Mock<IEventProcessorService>();
            var mockIReceptionRepository = new Mock<IReceptionRepository>();
            var mockILoggingHandler = new Mock<ILoggingHandler>();

            var saveAccountDueListInteractor = new SaveAccountDueListInteractor(mockIAccountDueRepository.Object, mockIUserRepository.Object, mockIHpInfRepository.Object,
                                                                                mockIPatientInforRepository.Object, mockIEventProcessorService.Object, mockIReceptionRepository.Object, TenantProvider);

            Random random = new Random();

            int hpId = random.Next(999, 99999); ;
            int userId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int sinDate = random.Next(********, ********);
            string kaikeiTime = "";
            int nyukinKbn = 1, nyukinjiTensu = 0, nyukinjiSeikyu = 0, newSeikyuTensu = 0, newAdjustFutan = 0, newSeikyuGaku = 0, sortNo = 0, adjustFutan = 0, nyukinGaku = 0, paymentMethodCd = 1, nyukinDate = 1, uketukeSbt = 0, seikyuGaku = 1, seikyuTensu = 0, raiinInfStatus = 0, seikyuAdjustFutan = 0, seikyuSinDate = 0;
            int paymentMethodFCO = 6;
            long raiinNo = 1, seqNo = 0;
            bool isBulk = false;
            string nyukinCmt = "", seikyuDetail = "", newSeikyuDetail = "", nyukinjiDetail = "";
            bool isDelete = false, isUpdated = true;

            List<SyunoNyukinInputItem> syunoNyukinInputItems = new List<SyunoNyukinInputItem>()
            {
                new SyunoNyukinInputItem(nyukinKbn, raiinNo, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo + 1, raiinInfStatus, seikyuAdjustFutan, sinDate, isDelete),
                new SyunoNyukinInputItem(nyukinKbn, raiinNo + 1, sortNo, adjustFutan, nyukinGaku, paymentMethodFCO, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo + 1, raiinInfStatus, seikyuAdjustFutan + 1, sinDate, isDelete)
            };

            List<SyunoSeikyuModel> syunoSeikyuModels = new List<SyunoSeikyuModel>()
            {
                new SyunoSeikyuModel(hpId, ptId, sinDate, raiinNo, nyukinKbn, seikyuTensu, adjustFutan, seikyuGaku, seikyuDetail, newSeikyuTensu, newAdjustFutan, newSeikyuGaku, newSeikyuDetail)
            };

            List<SyunoNyukinModel> syunoNyukinModels = new List<SyunoNyukinModel>()
            {
                new SyunoNyukinModel(hpId, ptId, sinDate, raiinNo, seqNo + 1, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, nyukinjiTensu, nyukinjiSeikyu, nyukinjiDetail),
                new SyunoNyukinModel(hpId, ptId, sinDate, raiinNo + 1, seqNo + 1, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, nyukinjiTensu, nyukinjiSeikyu, nyukinjiDetail)
            };

            Dictionary<long, RaiinInfModel> dictRaiinInfModel = new Dictionary<long, RaiinInfModel>(){
                [1] = new RaiinInfModel(hpId, ptId, sinDate, raiinNo, new ReserveDetailModel(0,0)),
                [2] = new RaiinInfModel(hpId, ptId, sinDate, raiinNo, new ReserveDetailModel(0,0))
            };

            List<AccountDueModel> listAccountDue = new List<AccountDueModel>()
            {
                new AccountDueModel(nyukinKbn, sortNo, raiinNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete)
            };

            Dictionary<int, string> paymentMethodModel = new Dictionary<int, string>(){
                [1] = "現金",
                [2] = "クレジット",
                [6] = "FCO連携"
            };

            mockIHpInfRepository.Setup(finder => finder.CheckHpId(hpId)).Returns((int hpId) => true);
            mockIUserRepository.Setup(finder => finder.CheckExistedUserId(hpId, userId)).Returns((int hpId, int userId) => true);
            mockIPatientInforRepository.Setup(finder => finder.CheckExistIdList(hpId, new List<long> { ptId })).Returns((int hpId, List<long> ptId) => true);
            mockIAccountDueRepository.Setup(finder => finder.GetListSyunoSeikyuModel(It.IsAny<int>(), It.IsAny<List<long>>())).Returns((int hpId, List<long> listRaiinNo) => syunoSeikyuModels);
            mockIAccountDueRepository.Setup(finder => finder.GetListSyunoNyukinModel(It.IsAny<int>(), It.IsAny<List<long>>())).Returns((int hpId, List<long> listRaiinNo) => syunoNyukinModels);
            mockIAccountDueRepository.Setup(finder => finder.GetDictRaiinInfModel(It.IsAny<int>(), It.IsAny<List<long>>())).Returns((int hpId, List<long> listRaiinNo) => dictRaiinInfModel);
            mockIAccountDueRepository.Setup(finder => finder.GetPaymentMethod(It.IsAny<int>())).Returns((int paymentMethodCd) => paymentMethodModel);
            var inputData = new SaveAccountDueListInputData(hpId, userId, ptId, sinDate, kaikeiTime, syunoNyukinInputItems, isBulk);

            mockIAccountDueRepository.Setup(x => x.SaveAccountDueList(It.IsAny<int>(), It.IsAny<long>(), It.IsAny<int>(), It.IsAny<int>(), It.IsAny<List<AccountDueModel>>(), It.IsAny<Dictionary<long, RaiinInfModel>>(), It.IsAny<string>()))
            .Returns((int input1, long input2, int input3, int input4, List<AccountDueModel> input5, Dictionary<long, RaiinInfModel> input6, string input7) => listAccountDue);

            // Act
            var result = saveAccountDueListInteractor.Handle(inputData);

            //Assert
            Assert.That(result.Status, Is.EqualTo(SaveAccountDueListStatus.NotAllowedPaymentMethodChangeToFCO));
        }

        [Test]
        public void TC_024_SaveAccountDueListInteractor_Handle_ValidateInvalidNyukinKbn_PaymentMethod_Change_FCOToOther_Success()
        {
            //Arrange
            var mockIAccountDueRepository = new Mock<IAccountDueRepository>();
            var mockIUserRepository = new Mock<IUserRepository>();
            var mockIHpInfRepository = new Mock<IHpInfRepository>();
            var mockIPatientInforRepository = new Mock<IPatientInforRepository>();
            var mockIEventProcessorService = new Mock<IEventProcessorService>();
            var mockIReceptionRepository = new Mock<IReceptionRepository>();
            var mockILoggingHandler = new Mock<ILoggingHandler>();

            var saveAccountDueListInteractor = new SaveAccountDueListInteractor(mockIAccountDueRepository.Object, mockIUserRepository.Object, mockIHpInfRepository.Object,
                                                                                mockIPatientInforRepository.Object, mockIEventProcessorService.Object, mockIReceptionRepository.Object, TenantProvider);

            Random random = new Random();

            int hpId = random.Next(999, 99999); ;
            int userId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int sinDate = random.Next(********, ********);
            string kaikeiTime = "";
            int nyukinKbn = 1, nyukinjiTensu = 0, nyukinjiSeikyu = 0, newSeikyuTensu = 0, newAdjustFutan = 0, newSeikyuGaku = 1000, sortNo = 0, adjustFutan = 0, nyukinGaku = 500, paymentMethodCd = 1, nyukinDate = 1, uketukeSbt = 0, seikyuGaku = 1, seikyuTensu = 0, raiinInfStatus = 0, seikyuAdjustFutan = 0, seikyuSinDate = 0;
            int paymentMethodCash = 0, paymentMethodCredit = 1, paymentMethodElectronicMoney = 4, paymentMethodFCO = 6;
            long raiinNo = 1, seqNo = 0;
            bool isBulk = false;
            string nyukinCmt = "", seikyuDetail = "", newSeikyuDetail = "", nyukinjiDetail = "";
            bool isDelete = false, isUpdated = true;

            List<SyunoNyukinInputItem> syunoNyukinInputItems = new List<SyunoNyukinInputItem>()
            {
                new SyunoNyukinInputItem(nyukinKbn, raiinNo, sortNo, adjustFutan, nyukinGaku, paymentMethodCash, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan, sinDate, isDelete),
                new SyunoNyukinInputItem(nyukinKbn, raiinNo + 1, sortNo, adjustFutan, nyukinGaku, paymentMethodCredit, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan, sinDate, isDelete),
                new SyunoNyukinInputItem(nyukinKbn, raiinNo + 1, sortNo, adjustFutan, nyukinGaku, paymentMethodElectronicMoney, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo + 1, raiinInfStatus, seikyuAdjustFutan, sinDate, isDelete),
                new SyunoNyukinInputItem(nyukinKbn, raiinNo + 2, sortNo, adjustFutan, nyukinGaku, paymentMethodFCO, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan + 1, sinDate, isDelete),
                new SyunoNyukinInputItem(nyukinKbn, raiinNo + 3, sortNo, adjustFutan, nyukinGaku, paymentMethodFCO, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan + 1, sinDate, isDelete)
            };

            List<SyunoSeikyuModel> syunoSeikyuModels = new List<SyunoSeikyuModel>()
            {
                new SyunoSeikyuModel(hpId, ptId, sinDate, raiinNo, nyukinKbn, seikyuTensu, adjustFutan, seikyuGaku, seikyuDetail, newSeikyuTensu, newAdjustFutan, newSeikyuGaku, newSeikyuDetail),
                new SyunoSeikyuModel(hpId, ptId, sinDate, raiinNo + 1, nyukinKbn, seikyuTensu, adjustFutan, seikyuGaku, seikyuDetail, newSeikyuTensu, newAdjustFutan, newSeikyuGaku, newSeikyuDetail),
                new SyunoSeikyuModel(hpId, ptId, sinDate, raiinNo + 2, nyukinKbn, seikyuTensu, adjustFutan, seikyuGaku, seikyuDetail, newSeikyuTensu, newAdjustFutan, newSeikyuGaku, newSeikyuDetail)
            };

            List<SyunoNyukinModel> syunoNyukinModels = new List<SyunoNyukinModel>()
            {
                new SyunoNyukinModel(hpId, ptId, sinDate, raiinNo, seqNo, sortNo, adjustFutan, nyukinGaku, paymentMethodFCO, nyukinDate, uketukeSbt, nyukinCmt, nyukinjiTensu, nyukinjiSeikyu, nyukinjiDetail),
                new SyunoNyukinModel(hpId, ptId, sinDate, raiinNo + 1, seqNo, sortNo, adjustFutan, nyukinGaku, paymentMethodFCO, nyukinDate, uketukeSbt, nyukinCmt, nyukinjiTensu, nyukinjiSeikyu, nyukinjiDetail),
                new SyunoNyukinModel(hpId, ptId, sinDate, raiinNo + 1, seqNo + 1, sortNo, adjustFutan, nyukinGaku, paymentMethodFCO, nyukinDate, uketukeSbt, nyukinCmt, nyukinjiTensu, nyukinjiSeikyu, nyukinjiDetail),
                new SyunoNyukinModel(hpId, ptId, sinDate, raiinNo + 2, seqNo, sortNo, adjustFutan, nyukinGaku, paymentMethodFCO, nyukinDate, uketukeSbt, nyukinCmt, nyukinjiTensu, nyukinjiSeikyu, nyukinjiDetail)
            };

            Dictionary<long, RaiinInfModel> dictRaiinInfModel = new Dictionary<long, RaiinInfModel>(){
                [1] = new RaiinInfModel(hpId, ptId, sinDate, raiinNo, new ReserveDetailModel(0,0)),
                [2] = new RaiinInfModel(hpId, ptId, sinDate, raiinNo + 1, new ReserveDetailModel(0,0)),
                [3] = new RaiinInfModel(hpId, ptId, sinDate, raiinNo + 2, new ReserveDetailModel(0,0)),
                [4] = new RaiinInfModel(hpId, ptId, sinDate, raiinNo + 3, new ReserveDetailModel(0,0))
            };

            List<AccountDueModel> listAccountDue = new List<AccountDueModel>()
            {
                new AccountDueModel(nyukinKbn, sortNo, raiinNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete),
                new AccountDueModel(nyukinKbn, sortNo, raiinNo + 1, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete),
                new AccountDueModel(nyukinKbn, sortNo, raiinNo + 1, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, seqNo + 1, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete),
                new AccountDueModel(nyukinKbn, sortNo, raiinNo + 2, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete),
                new AccountDueModel(nyukinKbn, sortNo, raiinNo + 3, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete)
            };

            Dictionary<int, string> paymentMethodModel = new Dictionary<int, string>(){
                [0] = "現金",
                [1] = "クレジット",
                [4] = "電子マネー",
                [5] = "オンライン決済",
                [6] = "FCO連携"
            };

            mockIHpInfRepository.Setup(finder => finder.CheckHpId(hpId)).Returns((int hpId) => true);
            mockIUserRepository.Setup(finder => finder.CheckExistedUserId(hpId, userId)).Returns((int hpId, int userId) => true);
            mockIPatientInforRepository.Setup(finder => finder.CheckExistIdList(hpId, new List<long> { ptId })).Returns((int hpId, List<long> ptId) => true);
            mockIAccountDueRepository.Setup(finder => finder.GetListSyunoSeikyuModel(It.IsAny<int>(), It.IsAny<List<long>>())).Returns((int hpId, List<long> listRaiinNo) => syunoSeikyuModels);
            mockIAccountDueRepository.Setup(finder => finder.GetListSyunoNyukinModel(It.IsAny<int>(), It.IsAny<List<long>>())).Returns((int hpId, List<long> listRaiinNo) => syunoNyukinModels);
            mockIAccountDueRepository.Setup(finder => finder.GetDictRaiinInfModel(It.IsAny<int>(), It.IsAny<List<long>>())).Returns((int hpId, List<long> listRaiinNo) => dictRaiinInfModel);
            mockIAccountDueRepository.Setup(finder => finder.GetPaymentMethod(It.IsAny<int>())).Returns((int paymentMethodCd) => paymentMethodModel);
            var inputData = new SaveAccountDueListInputData(hpId, userId, ptId, sinDate, kaikeiTime, syunoNyukinInputItems, isBulk);

            mockIAccountDueRepository.Setup(x => x.SaveAccountDueList(It.IsAny<int>(), It.IsAny<long>(), It.IsAny<int>(), It.IsAny<int>(), It.IsAny<List<AccountDueModel>>(), It.IsAny<Dictionary<long, RaiinInfModel>>(), It.IsAny<string>()))
            .Returns((int input1, long input2, int input3, int input4, List<AccountDueModel> input5, Dictionary<long, RaiinInfModel> input6, string input7) => listAccountDue);

            // Act
            var result = saveAccountDueListInteractor.Handle(inputData);

            //Assert
            Assert.That(result.Status, Is.EqualTo(SaveAccountDueListStatus.Successed));
            for (int i = 0; i < result.AccountDueModel.AccountDueList.Count; i++) {
                Assert.That(result.AccountDueModel.AccountDueList[i].PaymentMethodCd, Is.EqualTo(listAccountDue[i].PaymentMethodCd), "PaymentMethodCd:Count:[%d]", i);
            }
        }

        [Test]
        public void SaveAccountDueListInteractor_Handle_ValidateDelete_Return()
        {
            //Arrange
            var mockIAccountDueRepository = new Mock<IAccountDueRepository>();
            var mockIUserRepository = new Mock<IUserRepository>();
            var mockIHpInfRepository = new Mock<IHpInfRepository>();
            var mockIPatientInforRepository = new Mock<IPatientInforRepository>();
            var mockIEventProcessorService = new Mock<IEventProcessorService>();
            var mockIReceptionRepository = new Mock<IReceptionRepository>();
            var mockILoggingHandler = new Mock<ILoggingHandler>();

            var saveAccountDueListInteractor = new SaveAccountDueListInteractor(mockIAccountDueRepository.Object, mockIUserRepository.Object, mockIHpInfRepository.Object,
                                                                                mockIPatientInforRepository.Object, mockIEventProcessorService.Object, mockIReceptionRepository.Object, TenantProvider);

            Random random = new Random();

            int hpId = random.Next(999, 99999);
            int userId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int sinDate = random.Next(********, ********);
            string kaikeiTime = "";
            int nyukinKbn = 3, nyukinjiTensu = 0, nyukinjiSeikyu = 0, newSeikyuTensu = 0, newAdjustFutan = 0, newSeikyuGaku = 0, sortNo = 0, adjustFutan = 0, nyukinGaku = 0, paymentMethodCd = 1, nyukinDate = 1, uketukeSbt = 0, seikyuGaku = 1, seikyuTensu = 0, raiinInfStatus = 0, seikyuAdjustFutan = 0, seikyuSinDate = 0;
            long raiinNo = 1, seqNo = 0;
            string nyukinCmt = "", seikyuDetail = "", newSeikyuDetail = "", nyukinjiDetail = "";
            bool isDelete = true, isUpdated = true;

            List<SyunoNyukinInputItem> syunoNyukinInputItems = new List<SyunoNyukinInputItem>()
            {
                new SyunoNyukinInputItem(nyukinKbn, raiinNo, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete),
            };

            List<SyunoSeikyuModel> syunoSeikyuModels = new List<SyunoSeikyuModel>()
            {
                new SyunoSeikyuModel(hpId, ptId, sinDate, raiinNo, nyukinKbn, seikyuTensu, adjustFutan, seikyuGaku, seikyuDetail, newSeikyuTensu, newAdjustFutan, newSeikyuGaku, newSeikyuDetail)
            };

            List<SyunoNyukinModel> syunoNyukinModels = new List<SyunoNyukinModel>()
            {
                new SyunoNyukinModel(hpId, ptId, sinDate, raiinNo, seqNo, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, nyukinjiTensu, nyukinjiSeikyu, nyukinjiDetail)
            };

            List<AccountDueModel> listAccountDue = new List<AccountDueModel>()
            {
                new AccountDueModel(nyukinKbn, sortNo, raiinNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete)
            };
            var raiinInfDictionary = new Dictionary<long, RaiinInfModel>
            {
                {raiinNo,  new RaiinInfModel(hpId, ptId, sinDate, raiinNo, null) }
            };
            var paymentMethodDic = new Dictionary<int, string>{{1,"Pay"}} ;

            mockIHpInfRepository.Setup(finder => finder.CheckHpId(hpId)).Returns((int hpId) => true);
            mockIUserRepository.Setup(finder => finder.CheckExistedUserId(hpId, userId)).Returns((int hpId, int userId) => true);
            mockIPatientInforRepository.Setup(finder => finder.CheckExistIdList(hpId, new List<long> { ptId })).Returns((int hpId, List<long> ptId) => true);
            mockIAccountDueRepository.Setup(finder => finder.GetListSyunoSeikyuModel(It.IsAny<int>(), It.IsAny<List<long>>())).Returns((int hpId, List<long> listRaiinNo) => syunoSeikyuModels);
            mockIAccountDueRepository.Setup(finder => finder.GetListSyunoNyukinModel(It.IsAny<int>(), It.IsAny<List<long>>())).Returns((int hpId, List<long> listRaiinNo) => syunoNyukinModels);
            mockIAccountDueRepository.Setup(finder => finder.GetDictRaiinInfModel(It.IsAny<int>(), It.IsAny<List<long>>())).Returns((int hpId, List<long> listRaiinNo) => raiinInfDictionary);
            mockIAccountDueRepository.Setup(finder => finder.GetPaymentMethod(It.IsAny<int>())).Returns((int hpId) => paymentMethodDic);;
            var inputData = new SaveAccountDueListInputData(hpId, userId, ptId, sinDate, kaikeiTime, syunoNyukinInputItems, false);

            mockIAccountDueRepository.Setup(x => x.SaveAccountDueList(It.IsAny<int>(), It.IsAny<long>(), It.IsAny<int>(), It.IsAny<int>(), It.IsAny<List<AccountDueModel>>(), It.IsAny<Dictionary<long, RaiinInfModel>>(), It.IsAny<string>()))
            .Returns((int input1, long input2, int input3, int input4, List<AccountDueModel> input5, Dictionary<long, RaiinInfModel> input6,string input7) => listAccountDue);

            var result = saveAccountDueListInteractor.Handle(inputData);

            Assert.That(result.Status == SaveAccountDueListStatus.Successed);
        }


        [Test]
        public void SaveAccountDueListInteractor_Handle_ValidateInvalidPaymentMethod_Change_NoBillToFCO()
        {
            //Arrange
            var mockIAccountDueRepository = new Mock<IAccountDueRepository>();
            var mockIUserRepository = new Mock<IUserRepository>();
            var mockIHpInfRepository = new Mock<IHpInfRepository>();
            var mockIPatientInforRepository = new Mock<IPatientInforRepository>();
            var mockIEventProcessorService = new Mock<IEventProcessorService>();
            var mockIReceptionRepository = new Mock<IReceptionRepository>();
            var mockILoggingHandler = new Mock<ILoggingHandler>();

            var saveAccountDueListInteractor = new SaveAccountDueListInteractor(mockIAccountDueRepository.Object, mockIUserRepository.Object, mockIHpInfRepository.Object,
                                                                                mockIPatientInforRepository.Object, mockIEventProcessorService.Object, mockIReceptionRepository.Object, TenantProvider);

            Random random = new Random();

            int hpId = random.Next(999, 99999); ;
            int userId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int sinDate = random.Next(********, ********);
            string kaikeiTime = "";
            int nyukinKbn = 1, nyukinjiTensu = 0, nyukinjiSeikyu = 0, newSeikyuTensu = 0, newAdjustFutan = 0, newSeikyuGaku = 0, sortNo = 0, adjustFutan = 0, nyukinGaku = 0, paymentMethodCd = 1, nyukinDate = 1, uketukeSbt = 0, seikyuGaku = 1, seikyuTensu = 0, raiinInfStatus = 0, seikyuAdjustFutan = 0, seikyuSinDate = 0;
            int paymentMethodFCO = 6;
            int nyukinKbnMenjo = 2;
            long raiinNo = 1, seqNo = 0;
            bool isBulk = true;
            string nyukinCmt = "", seikyuDetail = "", newSeikyuDetail = "", nyukinjiDetail = "";
            bool isDelete = false, isUpdated = true;

            List<SyunoNyukinInputItem> syunoNyukinInputItems = new List<SyunoNyukinInputItem>()
            {
                new SyunoNyukinInputItem(nyukinKbn, raiinNo, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo + 1, raiinInfStatus, seikyuAdjustFutan, sinDate, isDelete),
                new SyunoNyukinInputItem(nyukinKbnMenjo, raiinNo + 1, sortNo, adjustFutan, nyukinGaku, paymentMethodFCO, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, isUpdated, seqNo + 1, raiinInfStatus, seikyuAdjustFutan + 1, sinDate, isDelete)
            };

            List<SyunoSeikyuModel> syunoSeikyuModels = new List<SyunoSeikyuModel>()
            {
                new SyunoSeikyuModel(hpId, ptId, sinDate, raiinNo, nyukinKbn, seikyuTensu, adjustFutan, seikyuGaku, seikyuDetail, newSeikyuTensu, newAdjustFutan, newSeikyuGaku, newSeikyuDetail)
            };

            List<SyunoNyukinModel> syunoNyukinModels = new List<SyunoNyukinModel>()
            {
                new SyunoNyukinModel(hpId, ptId, sinDate, raiinNo, seqNo + 1, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, nyukinjiTensu, nyukinjiSeikyu, nyukinjiDetail),
                new SyunoNyukinModel(hpId, ptId, sinDate, raiinNo + 1, seqNo + 1, sortNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, nyukinjiTensu, nyukinjiSeikyu, nyukinjiDetail)
            };

            Dictionary<long, RaiinInfModel> dictRaiinInfModel = new Dictionary<long, RaiinInfModel>(){
                [1] = new RaiinInfModel(hpId, ptId, sinDate, raiinNo, new ReserveDetailModel(0,0)),
                [2] = new RaiinInfModel(hpId, ptId, sinDate, raiinNo, new ReserveDetailModel(0,0))
            };

            List<AccountDueModel> listAccountDue = new List<AccountDueModel>()
            {
                new AccountDueModel(nyukinKbn, sortNo, raiinNo, adjustFutan, nyukinGaku, paymentMethodCd, nyukinDate, uketukeSbt, nyukinCmt, seikyuGaku, seikyuTensu, seikyuDetail, seqNo, raiinInfStatus, seikyuAdjustFutan, seikyuSinDate, isDelete)
            };

            Dictionary<int, string> paymentMethodModel = new Dictionary<int, string>(){
                [1] = "現金",
                [2] = "クレジット",
                [6] = "FCO連携"
            };

            mockIHpInfRepository.Setup(finder => finder.CheckHpId(hpId)).Returns((int hpId) => true);
            mockIUserRepository.Setup(finder => finder.CheckExistedUserId(hpId, userId)).Returns((int hpId, int userId) => true);
            mockIPatientInforRepository.Setup(finder => finder.CheckExistIdList(hpId, new List<long> { ptId })).Returns((int hpId, List<long> ptId) => true);
            mockIAccountDueRepository.Setup(finder => finder.GetListSyunoSeikyuModel(It.IsAny<int>(), It.IsAny<List<long>>())).Returns((int hpId, List<long> listRaiinNo) => syunoSeikyuModels);
            mockIAccountDueRepository.Setup(finder => finder.GetListSyunoNyukinModel(It.IsAny<int>(), It.IsAny<List<long>>())).Returns((int hpId, List<long> listRaiinNo) => syunoNyukinModels);
            mockIAccountDueRepository.Setup(finder => finder.GetDictRaiinInfModel(It.IsAny<int>(), It.IsAny<List<long>>())).Returns((int hpId, List<long> listRaiinNo) => dictRaiinInfModel);
            mockIAccountDueRepository.Setup(finder => finder.GetPaymentMethod(It.IsAny<int>())).Returns((int paymentMethodCd) => paymentMethodModel);
            var inputData = new SaveAccountDueListInputData(hpId, userId, ptId, sinDate, kaikeiTime, syunoNyukinInputItems, isBulk);

            mockIAccountDueRepository.Setup(x => x.SaveAccountDueList(It.IsAny<int>(), It.IsAny<long>(), It.IsAny<int>(), It.IsAny<int>(), It.IsAny<List<AccountDueModel>>(), It.IsAny<Dictionary<long, RaiinInfModel>>(), It.IsAny<string>()))
            .Returns((int input1, long input2, int input3, int input4, List<AccountDueModel> input5, Dictionary<long, RaiinInfModel> input6, string input7) => listAccountDue);

            // Act
            var result = saveAccountDueListInteractor.Handle(inputData);

            //Assert
            Assert.That(result.Status, Is.EqualTo(SaveAccountDueListStatus.NotAllowedBulkPaymentMethodSelectFCO));
        }
        #endregion
    }
}
