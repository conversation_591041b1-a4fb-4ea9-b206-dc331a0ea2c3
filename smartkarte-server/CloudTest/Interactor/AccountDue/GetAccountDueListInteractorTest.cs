﻿using DocumentFormat.OpenXml.Office2010.Excel;
using Domain.Models.AccountDue;
using Domain.Models.PatientInfor;
using Domain.Models.Reception;
using Domain.Models.Fincode;
using Entity.Tenant;
using Helper.Enum;
using Helper.Constants;
using Interactor.AccountDue;
using Infrastructure.Repositories;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Moq;
using PostgreDataContext;
using System;
using System.Runtime.InteropServices;
using UseCase.AccountDue.GetAccountDueList;
using Domain.Models.NextOrder;
using Domain.Models.CommonModel;

namespace CloudUnitTest.Interactor.AccountDue
{
    public class GetAccountDueListInteractorTest : BaseUT
    {
        [Test]
        public void TC_001_GetAccountDueListInteractor_Handle_InvalidHpId()
        {
            //Arrange
            var mockIAccountDueRepository = new Mock<IAccountDueRepository>();
            var mockIReceptionRepository = new Mock<IReceptionRepository>();
            var mockIPatientInforRepository = new Mock<IPatientInforRepository>();

            var getAccountDueListInteractor = new GetAccountDueListInteractor(mockIAccountDueRepository.Object, mockIReceptionRepository.Object, mockIPatientInforRepository.Object);
            
            int hpId = -1;
            long ptId = 280301;
            int sinDate = ********;
            bool isUnpaidChecked = false;
            var inputData = new GetAccountDueListInputData(hpId, ptId, sinDate, isUnpaidChecked);

            // Act
            var result = getAccountDueListInteractor.Handle(inputData);

            // Assert
            Assert.That(result.Status == GetAccountDueListStatus.InvalidHpId);
        }

        [Test]
        public void TC_002_GetAccountDueListInteractor_Handle_InvalidPtId()
        {
            //Arrange
            var mockIAccountDueRepository = new Mock<IAccountDueRepository>();
            var mockIReceptionRepository = new Mock<IReceptionRepository>();
            var mockIPatientInforRepository = new Mock<IPatientInforRepository>();

            var getAccountDueListInteractor = new GetAccountDueListInteractor(mockIAccountDueRepository.Object, mockIReceptionRepository.Object, mockIPatientInforRepository.Object);

            int hpId = 1;
            long ptId = 280301;
            int sinDate = ********;
            bool isUnpaidChecked = false;
            var inputData = new GetAccountDueListInputData(hpId, ptId, sinDate, isUnpaidChecked);

            // Act
            var result = getAccountDueListInteractor.Handle(inputData);

            // Assert
            Assert.That(result.Status == GetAccountDueListStatus.InvalidPtId);
        }


        [Test]
        public void TC_003_GetAccountDueListInteractor_Handle_InvalidSindate()
        {
            //Arrange
            var mockIAccountDueRepository = new Mock<IAccountDueRepository>();
            var mockIReceptionRepository = new Mock<IReceptionRepository>();
            var mockIPatientInforRepository = new Mock<IPatientInforRepository>();

            var getAccountDueListInteractor = new GetAccountDueListInteractor(mockIAccountDueRepository.Object, mockIReceptionRepository.Object, mockIPatientInforRepository.Object);

            var tennal = TenantProvider.GetTrackingTenantDataContext();
            int hpId = 1;
            long ptId = ********;
            long seqNo = ********;
            int sinDate = 2001032;
            bool isUnpaidChecked = false;
            var inputData = new GetAccountDueListInputData(hpId, ptId, sinDate, isUnpaidChecked);
            var ptIds = new List<long>();
            ptIds.Add(ptId);
            mockIPatientInforRepository.Setup(finder => finder.CheckExistIdList(hpId, ptIds))
            .Returns((int hpId, List<long> ptIds) => true);

            PtInf ptInf = new PtInf()
            {
                HpId = hpId,
                PtId = ptId,
                SeqNo = seqNo,
            };

            tennal.Add(ptInf);

            try
            {
                // Act
                tennal.SaveChanges();
                var result = getAccountDueListInteractor.Handle(inputData);

                // Assert
                Assert.That(result.Status == GetAccountDueListStatus.InvalidSindate);
            }
            finally
            {
                tennal.PtInfs.Remove(ptInf);
                tennal.SaveChanges();
            }
        }

        // [Test]
        // public void TC_004_GetAccountDueListInteractor_Handle_NyukinKbn()
        // {
        //     //Arrange
        //     var mockIAccountDueRepository = new Mock<IAccountDueRepository>();
        //     var mockIReceptionRepository = new Mock<IReceptionRepository>();
        //     var mockIPatientInforRepository = new Mock<IPatientInforRepository>();

        //     var getAccountDueListInteractor = new GetAccountDueListInteractor(mockIAccountDueRepository.Object, mockIReceptionRepository.Object, mockIPatientInforRepository.Object);

        //     Random random = new Random();
        //     var tennal = TenantProvider.GetTrackingTenantDataContext();
        //     int hpId = ********;
        //     long ptId = random.Next(999, 99999);
        //     int sinDate = ********;
        //     long raiinNo = random.Next(999, 99999);
        //     long seqNo = random.Next(999, 99999);
        //     int status = random.Next(999, 99999);
        //     long id = random.Next(999, 99999);
        //     bool isUnpaidChecked = true;
        //     var inputData = new GetAccountDueListInputData(hpId, ptId, sinDate, isUnpaidChecked);
        //     var ptIds = new List<long>();
        //     Dictionary<int, string> getUketsukeSbt = new();
        //     Dictionary<int, string> getPaymentMethod = new();
        //     List<AccountDueModel> listAccountDues = new List<AccountDueModel>()
        //     {
        //         new AccountDueModel(hpId, ptId, sinDate, 200103, raiinNo, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, "", 0, 0, "", 0, 0, "", 0, 0, "", 0, 0)
        //     };

        //     List<ReceptionRowModel> hokenPatternList = new List<ReceptionRowModel>()
        //     {
        //         new ReceptionRowModel(raiinNo, ptId, sinDate, 0)
        //     };

        //     var uketukeList = tennal.UketukeSbtMsts.Where(item => item.HpId == hpId && item.IsDeleted == 0).OrderBy(p => p.SortNo).ToList();
        //     var paymentMethodList = tennal.PaymentMethodMsts.Where(item => item.HpId == hpId).OrderBy(item => item.SortNo).ToList();

        //     foreach (var uketuke in uketukeList)
        //     {
        //         getUketsukeSbt.Add(uketuke.KbnId, uketuke.KbnName ?? string.Empty);
        //     }

        //     foreach (var paymentMethod in paymentMethodList)
        //     {
        //         getPaymentMethod.Add(paymentMethod.PaymentMethodCd, paymentMethod.PayName ?? string.Empty);
        //     }

        //     ptIds.Add(ptId);

        //     mockIPatientInforRepository.Setup(finder => finder.CheckExistIdList(hpId, ptIds))
        //     .Returns((int hpId, List<long> ptIds) => true);
        //     mockIAccountDueRepository.Setup(finder => finder.GetUketsukeSbt(hpId))
        //     .Returns((int hpId) => getUketsukeSbt);
        //     mockIAccountDueRepository.Setup(finder => finder.GetPaymentMethod(hpId))
        //     .Returns((int hpId) => getPaymentMethod);
        //     mockIAccountDueRepository.Setup(finder => finder.GetAccountDueList(hpId, ptId, sinDate, isUnpaidChecked))
        //     .Returns((int hpId, long ptId, int sinDate, bool isUnpaidChecked) => listAccountDues);
        //     mockIReceptionRepository.Setup(finder => finder.GetList(hpId, sinDate, -1, ptId, true, false, 2, false))
        //     .Returns((int hpId, int sinDate, long raiinNo, long ptId, [Optional] bool isGetAccountDue, [Optional] bool isGetFamily, int isDeleted, bool searchSameVisit) => hokenPatternList);

        //     PtInf ptInf = new PtInf()
        //     {
        //         HpId = hpId,
        //         PtId = ptId,
        //         SeqNo = seqNo,
        //     };
        //     SyunoSeikyu syunoSeikyu = new SyunoSeikyu()
        //     {
        //         HpId = hpId,
        //         PtId = ptId,
        //         SinDate = sinDate,
        //         RaiinNo = raiinNo
        //     };

        //     SyunoNyukin syunoNyukin = new SyunoNyukin()
        //     {
        //         HpId = hpId,
        //         PtId = ptId,
        //         RaiinNo = raiinNo,
        //         SeqNo = seqNo
        //     };

        //     RaiinInf raiinInf = new RaiinInf()
        //     {
        //         HpId = hpId,
        //         RaiinNo = raiinNo,
        //         PtId = ptId,
        //         Status = status
        //     };

        //     KaMst kaMst = new KaMst()
        //     {
        //         HpId = hpId,
        //         Id = id
        //     };

        //     tennal.Add(syunoSeikyu);
        //     tennal.Add(syunoNyukin);
        //     tennal.Add(raiinInf);
        //     tennal.Add(kaMst);
        //     tennal.Add(ptInf);

        //     try
        //     {
        //         // Act
        //         tennal.SaveChanges();
        //         var result = getAccountDueListInteractor.Handle(inputData);

        //         // Assert
        //         Assert.That(result.AccountDueModel.AccountDueList.Any());
        //     }
        //     finally
        //     {
        //         tennal.SyunoSeikyus.Remove(syunoSeikyu);
        //         tennal.SyunoNyukin.Remove(syunoNyukin);
        //         tennal.RaiinInfs.Remove(raiinInf);
        //         tennal.KaMsts.Remove(kaMst);
        //         tennal.PtInfs.Remove(ptInf);
        //         tennal.SaveChanges();
        //     }
        // }

        // [Test]
        // public void TC_005_GetAccountDueListInteractor_Handle_Equals_RaiinNo()
        // {
        //     //Arrange
        //     var mockIAccountDueRepository = new Mock<IAccountDueRepository>();
        //     var mockIReceptionRepository = new Mock<IReceptionRepository>();
        //     var mockIPatientInforRepository = new Mock<IPatientInforRepository>();

        //     var getAccountDueListInteractor = new GetAccountDueListInteractor(mockIAccountDueRepository.Object, mockIReceptionRepository.Object, mockIPatientInforRepository.Object);

        //     Random random = new Random();
        //     var tennal = TenantProvider.GetTrackingTenantDataContext();

        //     int hpId = ********;
        //     long ptId = random.Next(999, 99999);
        //     int sinDate = ********;
        //     long raiinNo = 0;
        //     long seqNo = random.Next(999, 99999);
        //     int status = random.Next(999, 99999);
        //     long id = random.Next(999, 99999);
        //     bool isUnpaidChecked = true;
        //     var inputData = new GetAccountDueListInputData(hpId, ptId, sinDate, isUnpaidChecked);
        //     var ptIds = new List<long>();
        //     Dictionary<int, string> getUketsukeSbt = new();
        //     Dictionary<int, string> getPaymentMethod = new();
        //     List<AccountDueModel> listAccountDues = new List<AccountDueModel>()
        //     {
        //         new AccountDueModel(hpId, ptId, sinDate, 200103, raiinNo, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, "", 0, 0, "", 0, 0, "", 0, 0, "", 0, 0)
        //     };

        //     List<ReceptionRowModel> hokenPatternList = new List<ReceptionRowModel>()
        //     {
        //         new ReceptionRowModel(raiinNo, ptId, sinDate, 0)
        //     };

        //     var uketukeList = tennal.UketukeSbtMsts.Where(item => item.HpId == hpId && item.IsDeleted == 0).OrderBy(p => p.SortNo).ToList();
        //     var paymentMethodList = tennal.PaymentMethodMsts.Where(item => item.HpId == hpId).OrderBy(item => item.SortNo).ToList();

        //     foreach (var uketuke in uketukeList)
        //     {
        //         getUketsukeSbt.Add(uketuke.KbnId, uketuke.KbnName ?? string.Empty);
        //     }

        //     foreach (var paymentMethod in paymentMethodList)
        //     {
        //         getPaymentMethod.Add(paymentMethod.PaymentMethodCd, paymentMethod.PayName ?? string.Empty);
        //     }

        //     ptIds.Add(ptId);

        //     mockIPatientInforRepository.Setup(finder => finder.CheckExistIdList(hpId, ptIds))
        //     .Returns((int hpId, List<long> ptIds) => true);
        //     mockIAccountDueRepository.Setup(finder => finder.GetUketsukeSbt(hpId))
        //     .Returns((int hpId) => getUketsukeSbt);
        //     mockIAccountDueRepository.Setup(finder => finder.GetPaymentMethod(hpId))
        //     .Returns((int hpId) => getPaymentMethod);
        //     mockIAccountDueRepository.Setup(finder => finder.GetAccountDueList(hpId, ptId, sinDate, isUnpaidChecked))
        //     .Returns((int hpId, long ptId, int sinDate, bool isUnpaidChecked) => listAccountDues);
        //     mockIReceptionRepository.Setup(finder => finder.GetList(hpId, sinDate, -1, ptId, true, false, 2, false))
        //     .Returns((int hpId, int sinDate, long raiinNo, long ptId, [Optional] bool isGetAccountDue, [Optional] bool isGetFamily, int isDeleted, bool searchSameVisit) => hokenPatternList);

        //     PtInf ptInf = new PtInf()
        //     {
        //         HpId = hpId,
        //         PtId = ptId,
        //         SeqNo = seqNo,
        //     };
        //     SyunoSeikyu syunoSeikyu = new SyunoSeikyu()
        //     {
        //         HpId = hpId,
        //         PtId = ptId,
        //         SinDate = sinDate,
        //         RaiinNo = raiinNo
        //     };

        //     SyunoNyukin syunoNyukin = new SyunoNyukin()
        //     {
        //         HpId = hpId,
        //         PtId = ptId,
        //         RaiinNo = raiinNo,
        //         SeqNo = seqNo
        //     };

        //     RaiinInf raiinInf = new RaiinInf()
        //     {
        //         HpId = hpId,
        //         RaiinNo = raiinNo,
        //         PtId = ptId,
        //         Status = status
        //     };

        //     KaMst kaMst = new KaMst()
        //     {
        //         HpId = hpId,
        //         Id = id
        //     };

        //     tennal.Add(syunoSeikyu);
        //     tennal.Add(syunoNyukin);
        //     tennal.Add(raiinInf);
        //     tennal.Add(kaMst);
        //     tennal.Add(ptInf);

        //     try
        //     {
        //         // Act
        //         tennal.SaveChanges();
        //         var result = getAccountDueListInteractor.Handle(inputData);

        //         // Assert
        //         Assert.That(result.AccountDueModel.AccountDueList.Any());
        //     }
        //     finally
        //     {
        //         tennal.SyunoSeikyus.Remove(syunoSeikyu);
        //         tennal.SyunoNyukin.Remove(syunoNyukin);
        //         tennal.RaiinInfs.Remove(raiinInf);
        //         tennal.KaMsts.Remove(kaMst);
        //         tennal.PtInfs.Remove(ptInf);
        //         tennal.SaveChanges();
        //     }
        // }

        // [Test]
        // public void TC_006_GetAccountDueListInteractor_Handle_NotEquals_RaiinNo()
        // {
        //     //Arrange
        //     var mockIAccountDueRepository = new Mock<IAccountDueRepository>();
        //     var mockIReceptionRepository = new Mock<IReceptionRepository>();
        //     var mockIPatientInforRepository = new Mock<IPatientInforRepository>();

        //     var getAccountDueListInteractor = new GetAccountDueListInteractor(mockIAccountDueRepository.Object, mockIReceptionRepository.Object, mockIPatientInforRepository.Object);

        //     Random random = new Random();
        //     var tennal = TenantProvider.GetTrackingTenantDataContext();
        //     int hpId = ********;
        //     long ptId = random.Next(999, 99999);
        //     int sinDate = ********;
        //     long raiinNo = random.Next(999, 99999);
        //     long seqNo = random.Next(999, 99999);
        //     int status = random.Next(999, 99999);
        //     long id = random.Next(999, 99999);
        //     bool isUnpaidChecked = true;
        //     var inputData = new GetAccountDueListInputData(hpId, ptId, sinDate, isUnpaidChecked);
        //     var ptIds = new List<long>();
        //     Dictionary<int, string> getUketsukeSbt = new();
        //     Dictionary<int, string> getPaymentMethod = new();

        //     List<AccountDueModel> listAccountDues = new List<AccountDueModel>()
        //     {
        //         new AccountDueModel(hpId, ptId, sinDate, 200103, raiinNo, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, "", 0, 0, "", 0, 0, "", 0, 0, "", 0, 0)
        //     };

        //     List<ReceptionRowModel> hokenPatternList = new List<ReceptionRowModel>()
        //     {
        //         new ReceptionRowModel(raiinNo, ptId, sinDate, 0)
        //     };

        //     var uketukeList = tennal.UketukeSbtMsts.Where(item => item.HpId == hpId && item.IsDeleted == 0).OrderBy(p => p.SortNo).ToList();
        //     var paymentMethodList = tennal.PaymentMethodMsts.Where(item => item.HpId == hpId).OrderBy(item => item.SortNo).ToList();

        //     foreach (var uketuke in uketukeList)
        //     {
        //         getUketsukeSbt.Add(uketuke.KbnId, uketuke.KbnName ?? string.Empty);
        //     }

        //     foreach (var paymentMethod in paymentMethodList)
        //     {
        //         getPaymentMethod.Add(paymentMethod.PaymentMethodCd, paymentMethod.PayName ?? string.Empty);
        //     }

        //     ptIds.Add(ptId);

        //     mockIPatientInforRepository.Setup(finder => finder.CheckExistIdList(hpId, ptIds))
        //     .Returns((int hpId, List<long> ptIds) => true);
        //     mockIAccountDueRepository.Setup(finder => finder.GetUketsukeSbt(hpId))
        //     .Returns((int hpId) => getUketsukeSbt);
        //     mockIAccountDueRepository.Setup(finder => finder.GetPaymentMethod(hpId))
        //     .Returns((int hpId) => getPaymentMethod);
        //     mockIAccountDueRepository.Setup(finder => finder.GetAccountDueList(hpId, ptId, sinDate, isUnpaidChecked))
        //     .Returns((int hpId, long ptId, int sinDate, bool isUnpaidChecked) => listAccountDues);
        //     mockIReceptionRepository.Setup(finder => finder.GetList(hpId, sinDate, -1, ptId, true, false, 2, false))
        //     .Returns((int hpId, int sinDate, long raiinNo, long ptId, [Optional] bool isGetAccountDue, [Optional] bool isGetFamily, int isDeleted, bool searchSameVisit) => hokenPatternList);

        //     PtInf ptInf = new PtInf()
        //     {
        //         HpId = hpId,
        //         PtId = ptId,
        //         SeqNo = seqNo,
        //     };
        //     SyunoSeikyu syunoSeikyu = new SyunoSeikyu()
        //     {
        //         HpId = hpId,
        //         PtId = ptId,
        //         SinDate = sinDate,
        //         RaiinNo = raiinNo
        //     };

        //     SyunoNyukin syunoNyukin = new SyunoNyukin()
        //     {
        //         HpId = hpId,
        //         PtId = ptId,
        //         RaiinNo = raiinNo,
        //         SeqNo = seqNo
        //     };

        //     RaiinInf raiinInf = new RaiinInf()
        //     {
        //         HpId = hpId,
        //         RaiinNo = raiinNo,
        //         PtId = ptId,
        //         Status = status
        //     };

        //     KaMst kaMst = new KaMst()
        //     {
        //         HpId = hpId,
        //         Id = id
        //     };

        //     tennal.Add(syunoSeikyu);
        //     tennal.Add(syunoNyukin);
        //     tennal.Add(raiinInf);
        //     tennal.Add(kaMst);
        //     tennal.Add(ptInf);

        //     try
        //     {
        //         // Act
        //         tennal.SaveChanges();
        //         var result = getAccountDueListInteractor.Handle(inputData);

        //         // Assert
        //         Assert.That(result.AccountDueModel.AccountDueList.Any());
        //     }
        //     finally
        //     {
        //         tennal.SyunoSeikyus.Remove(syunoSeikyu);
        //         tennal.SyunoNyukin.Remove(syunoNyukin);
        //         tennal.RaiinInfs.Remove(raiinInf);
        //         tennal.KaMsts.Remove(kaMst);
        //         tennal.PtInfs.Remove(ptInf);
        //         tennal.SaveChanges();
        //     }
        // }

        [Test]
        public void Handle_NyukinErr_GetCheck()
        {
            //Arrange
            var _mockFincodeRepository = new Mock<IFincodeRepository>();
            var _mockNextOrderRepository = new Mock<INextOrderRepository>();
            var _mockCommonRepository = new Mock<ICommonRepository>();
            var _receptionRepository = new ReceptionRepository(TenantProvider, _mockNextOrderRepository.Object, _mockCommonRepository.Object);
            var _patientInforRepository = new PatientInforRepository(TenantProvider, _receptionRepository);
            var _accountDueRepository = new AccountDueRepository(TenantProvider, _mockFincodeRepository.Object);

            // 正の請求額と正の未収金を持つケースを検証する。
            // 確認内容: 入金、請求,来院データが取得できること。
            var getAccountDueListInteractor = new GetAccountDueListInteractor(_accountDueRepository,  _receptionRepository, _patientInforRepository);
            var tenant = TenantProvider.GetTrackingTenantDataContext();

            Random random = new Random();
            int hpId = 10;
            long ptId = random.Next(999, 99999);
            long raiinNo1 = random.Next(999, 99999);
            long raiinNo2 = raiinNo1 + 1;
            long raiinNo3 = raiinNo1 + 2;
            long raiinNo4 = raiinNo1 + 3;
            long raiinNo5 = raiinNo1 + 4;
            long raiinNo6 = raiinNo1 + 5;
            long oyaRaiinNo = raiinNo1;
            bool isUnpaidChecked = false;
            int sinDate = ********;

            //患者情報のテストデータ
            tenant.PtInfs.AddRange(
                CreatePtInf(hpId, ptId)
            );

            //同一来院のテストデータ
            tenant.RaiinInfs.AddRange(
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo1, oyaRaiinNo, RaiinState.ConsultationCompleted),
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo2, oyaRaiinNo, RaiinState.AmountConfirmed),
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo3, oyaRaiinNo, RaiinState.AmountConfirmed),
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo4, oyaRaiinNo, RaiinState.Paid),
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo5, oyaRaiinNo, RaiinState.Paid),
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo6, oyaRaiinNo, RaiinState.Paid)
            );

            tenant.SyunoSeikyus.AddRange(
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo1, NyukinKbnEnums.Unsettled, 100, 1100, 0, 0),
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo2, NyukinKbnEnums.Unsettled, 100, 2100, 0, 0),
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo3, NyukinKbnEnums.Unsettled, 100, 3100, 0, 0),
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo4, NyukinKbnEnums.PartiallySettled, 100, 4100, 0, 0),
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo5, NyukinKbnEnums.PartiallySettled, 100, 5100, 0, 0),
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo6, NyukinKbnEnums.FullySettled, 100, 6100, 0, 0)
            );
            tenant.SyunoNyukin.AddRange(
                CreateSyunoNyukin(hpId, ptId, sinDate, raiinNo4, 500, 1500, PaymentMethodCdEnum.Cash, NyukinStatusEnums.Success),
                CreateSyunoNyukin(hpId, ptId, sinDate, raiinNo5, 100, 500, PaymentMethodCdEnum.Cash, NyukinStatusEnums.Failed),
                CreateSyunoNyukin(hpId, ptId, sinDate, raiinNo6, 200, 5500, PaymentMethodCdEnum.Cash, NyukinStatusEnums.Failed)
            );
            tenant.SaveChanges();

            //リクエスト
            var inputData = new GetAccountDueListInputData(
                hpId: hpId,
                ptId: ptId,
                sinDate: sinDate,
                isUnpaidChecked: isUnpaidChecked
            );

            var result = getAccountDueListInteractor.Handle(inputData);
            Assert.That(result.Status, Is.EqualTo(GetAccountDueListStatus.Successed));
            Assert.That(result.AccountDueModel.AccountDueList.Any());

            //raiinNo4
            AssertSyunoNyukin(result.AccountDueModel.AccountDueList[3], 1500 , 500);
            //raiinNo5
            AssertSyunoNyukin(result.AccountDueModel.AccountDueList[4], 500 , 100);
            //raiinNo6
            AssertSyunoNyukin(result.AccountDueModel.AccountDueList[5], 5500 , 200);  

            // データを削除
            CleanupTestData(tenant, hpId);
        }

        private PtInf CreatePtInf(int hpId, long ptId)
        {
            return new PtInf
            {
                HpId = hpId,
                PtId = ptId,
                SeqNo = 1,
                PtNum = 999,
                KanaName = "テストデータ",
                Name = "試験データ",
                Sex = 1,
                Birthday = ********,
                Tel1 = "**********"
            };
        }

        private RaiinInf CreateRaiinInf(int hpId, long ptId, int sinDate, long raiinNo, long oyaRaiinNo, int status)
        {
            return new RaiinInf
            {
                HpId = hpId,
                PtId = ptId,
                SinDate = sinDate,
                RaiinNo = raiinNo,
                OyaRaiinNo = oyaRaiinNo,
                Status = status,
                KaikeiTime = "104500",
                KaikeiId = 0,
                HokenPid = 0,
                MonshinStatus = 0
            };
        }

        private SyunoSeikyu CreateSyunoSeikyu(int hpId, long ptId, int sinDate, long raiinNo, NyukinKbnEnums nyukinKbn, int adjustFutan, int seikyuGaku, int newAdjustFutan, int newSeikyuGaku)
        {
            return new SyunoSeikyu
            {
                HpId = hpId,
                PtId = ptId,
                SinDate = sinDate,
                RaiinNo = raiinNo,
                NyukinKbn = (int)nyukinKbn,
                AdjustFutan = adjustFutan,
                SeikyuGaku = seikyuGaku,
                NewAdjustFutan = newAdjustFutan,
                NewSeikyuGaku = newSeikyuGaku,
            };
        }

        private SyunoNyukin CreateSyunoNyukin(int hpId, long ptId, int sinDate, long raiinNo, int adjustFutan, int nyukinGaku, PaymentMethodCdEnum paymentMethodCd, NyukinStatusEnums nyukinStatus)
        {
            return new SyunoNyukin
            {
                HpId = hpId,
                PtId = ptId,
                SinDate = sinDate,
                RaiinNo = raiinNo,
                AdjustFutan = adjustFutan,
                NyukinGaku = nyukinStatus == NyukinStatusEnums.Success ? nyukinGaku : 0,
                PaymentMethodCd = (int)paymentMethodCd,
                NyukinStatus = (int)nyukinStatus,
                ExpectedNyukinGaku = nyukinStatus == NyukinStatusEnums.Success ? 0 : nyukinGaku
            };
        }

        private void AssertSyunoNyukin(AccountDueModel accountDueModel, int expectedNyukinGaku, int expectedAdjustFutan)
        {
            Assert.That(accountDueModel.NyukinGaku, Is.EqualTo(expectedNyukinGaku));
            Assert.That(accountDueModel.AdjustFutan, Is.EqualTo(expectedAdjustFutan));
        }

        private void CleanupTestData(TenantDataContext tenant, int hpId)
        {
            tenant.PtInfs.RemoveRange(tenant.PtInfs.Where(x => x.HpId == hpId).ToList());
            tenant.RaiinInfs.RemoveRange(tenant.RaiinInfs.Where(x => x.HpId == hpId).ToList());
            tenant.SyunoSeikyus.RemoveRange(tenant.SyunoSeikyus.Where(x => x.HpId == hpId).ToList());
            tenant.SyunoNyukin.RemoveRange(tenant.SyunoNyukin.Where(x => x.HpId == hpId).ToList());
            tenant.SaveChanges();
        }
    }
}
