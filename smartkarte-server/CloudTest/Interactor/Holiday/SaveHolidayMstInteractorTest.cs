using Domain.Models.FlowSheet;
using Domain.Models.User;
using Interactor.Holiday;
using UseCase.Holiday.SaveHoliday;
using Moq;
using static Helper.Constants.UserConst;

namespace CloudUnitTest.Interactor.Holiday
{
    public class SaveHolidayMstInteractorTest : BaseUT
    {
        [Test]
        // nullの入力データの場合、Failed を返すことを確認するテスト
        public void Test_SaveHolidayMstInteractor_Handle_ValidateInputData_NG_Failed()
        {
            // Arrange
            var mockIFlowSheetRepository = new Mock<IFlowSheetRepository>();
            var mockIUserRepository = new Mock<IUserRepository>();
            var saveHolidayMstInteractor = new SaveHolidayMstInteractor(mockIFlowSheetRepository.Object, mockIUserRepository.Object);

            // Act
            var result = saveHolidayMstInteractor.Handle(null);

            // Assert
            Assert.That(result.Status, Is.EqualTo(SaveHolidayMstStatus.Failed));

            // Verify
            mockIFlowSheetRepository.Verify(repo => repo.SaveHolidayMst(It.IsAny<List<HolidayModel>>(), It.IsAny<int>(), It.IsAny<bool>()), Times.Never);
            mockIUserRepository.Verify(repo => repo.GetPermissionByScreenCode(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<string>()), Times.Never);
        }

        [Test]
        // ユーザーIDが0以下の場合、InvalidUserId を返すことを確認するテスト
        public void Test_SaveKaMstInteractor_Handle_ValidateInputData_NG_InvalidHpId()
        {
            // Arrange
            var mockIFlowSheetRepository = new Mock<IFlowSheetRepository>();
            var mockIUserRepository = new Mock<IUserRepository>();
            var saveHolidayMstInteractor = new SaveHolidayMstInteractor(mockIFlowSheetRepository.Object, mockIUserRepository.Object);

            // Setup
            mockIFlowSheetRepository.Verify(repo => repo.SaveHolidayMst(It.IsAny<List<HolidayModel>>(), It.IsAny<int>(), It.IsAny<bool>()), Times.Never);
            mockIUserRepository.Verify(repo => repo.GetPermissionByScreenCode(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<string>()), Times.Never);

            // Act
            var inputData = new SaveHolidayMstInputData(new HolidayModel(0, 0, ""), "", 0);
            var result = saveHolidayMstInteractor.Handle(inputData);

            // Assert
            Assert.That(result.Status, Is.EqualTo(SaveHolidayMstStatus.InvalidUserId));

            // Verify
            mockIFlowSheetRepository.Verify(repo => repo.SaveHolidayMst(It.IsAny<List<HolidayModel>>(), It.IsAny<int>(), It.IsAny<bool>()), Times.Never);
            mockIUserRepository.Verify(repo => repo.GetPermissionByScreenCode(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<string>()), Times.Never);
        }

        [TestCase(20251225, "", new[] { 20251225 })]
        [TestCase(20251225, "RRULE:FREQ=WEEKLY;BYDAY=TU", new[] { 20251230 })]
        [TestCase(20251103, "RRULE:FREQ=WEEKLY;BYDAY=TU", new[] { 20251104, 20251111, 20251118, 20251125, 20251202, 20251209, 20251216, 20251223, 20251230 })]
        [TestCase(20251103, "RRULE:FREQ=WEEKLY;BYDAY=SA", new[] { 20251108, 20251115, 20251122, 20251129, 20251206, 20251213, 20251220, 20251227 })]
        [TestCase(20251103, "RRULE:FREQ=MONTHLY;BYDAY=1WE", new[] { 20251105, 20251203 })]
        [TestCase(20251103, "RRULE:FREQ=MONTHLY;BYDAY=5TU", new[] { 20251230 })]
        [TestCase(20251103, "RRULE:FREQ=MONTHLY;BYDAY=6TU", new int[] { })]
        [TestCase(20251103, "Strange parameters", new int[] { })]
        // 正常系のテスト
        public void Test_SaveKaMstInteractor_Handle_ValidateInputData_OK(int startDate, string recurrenceRule, int[] expectedDates)
        {
            // Arrange
            var mockIFlowSheetRepository = new Mock<IFlowSheetRepository>();
            var mockIUserRepository = new Mock<IUserRepository>();
            var saveHolidayMstInteractor = new SaveHolidayMstInteractor(mockIFlowSheetRepository.Object, mockIUserRepository.Object);

            // Setup
            mockIFlowSheetRepository.Setup(repo => repo.SaveHolidayMst(It.IsAny<List<HolidayModel>>(), It.IsAny<int>(), It.IsAny<bool>()))
                               .Returns(true);
            mockIUserRepository.Setup(repo => repo.GetPermissionByScreenCode(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<string>()))
                               .Returns(PermissionType.Unlimited);

            // Act
            var inputData = new SaveHolidayMstInputData(
                new HolidayModel(0, 0, startDate, 0, 0, ""),
                recurrenceRule,
                35
            );
            var result = saveHolidayMstInteractor.Handle(inputData);

            // Assert
            Assert.That(result.Status, Is.EqualTo(SaveHolidayMstStatus.Successful));

            // Verify
            mockIFlowSheetRepository.Verify(
                repo => repo.SaveHolidayMst(
                    It.Is<List<HolidayModel>>(list =>
                        list.Select(h => h.SinDate)
                            .SequenceEqual(expectedDates) // 期待する配列と一致しているか確認
                    ),
                    It.Is<int>(userId => userId == 35),
                    It.IsAny<bool>()
                ),
                Times.Once
            );
            mockIUserRepository.Verify(repo => repo.GetPermissionByScreenCode(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<string>()), Times.Once);
        }
    }
}
