using Domain.Models.Ka;
using Interactor.Ka;
using Moq;
using UseCase.Ka.SaveList;
using Domain.Constant;
using Helper.Exceptions;

namespace CloudUnitTest.Interactor.Ka
{
    public class SaveKaMstInteractorTest : BaseUT
    {
        #region ValidateInputData
        [Test]
        // nullの入力データの場合、Failed を返すことを確認するテスト
        public void TC_001_SaveKaMstInteractor_Handle_ValidateInputData_Return_Failed()
        {
            // Arrange
            var mockIKaRepository = new Mock<IKaRepository>();
            var saveKaMstInteractor = new SaveKaMstInteractor(mockIKaRepository.Object);

            // Act
            var result = saveKaMstInteractor.Handle(null);

            // Assert
            Assert.That(result.Status == SaveKaMstStatus.Failed);
        }

        [Test]
        // 病院IDが0以下の場合、InvalidHpId を返すことを確認するテスト
        public void TC_002_SaveKaMstInteractor_Handle_ValidateInputData_Return_InvalidHpId()
        {
            // Arrange
            var mockIKaRepository = new Mock<IKaRepository>();
            var saveKaMstInteractor = new SaveKaMstInteractor(mockIKaRepository.Object);

            var inputData = new SaveKaMstInputData(0, 1, new List<SaveKaMstInputItem>());

            // Act
            var result = saveKaMstInteractor.Handle(inputData);

            // Assert
            Assert.That(result.Status == SaveKaMstStatus.InvalidHpId);
        }

        [Test]
        // ユーザーIDが0以下の場合、InvalidUserId を返すことを確認するテスト
        public void TC_003_SaveKaMstInteractor_Handle_ValidateInputData_Return_InvalidUserId()
        {
            // Arrange
            var mockIKaRepository = new Mock<IKaRepository>();
            var saveKaMstInteractor = new SaveKaMstInteractor(mockIKaRepository.Object);

            var inputData = new SaveKaMstInputData(1, 0, new List<SaveKaMstInputItem>());

            // Act
            var result = saveKaMstInteractor.Handle(inputData);

            // Assert
            Assert.That(result.Status == SaveKaMstStatus.InvalidUserId);
        }

        [Test]
        // 科IDが0以下の場合、InvalidKaId を返すことを確認するテスト
        public void TC_004_SaveKaMstInteractor_Handle_ValidateInputData_Return_InvalidKaId()
        {
            // Arrange
            var mockIKaRepository = new Mock<IKaRepository>();
            var saveKaMstInteractor = new SaveKaMstInteractor(mockIKaRepository.Object);

            var kaMstInputItems = new List<SaveKaMstInputItem>
            {
                new SaveKaMstInputItem(1, 0, "1", "001", "科略名", "科名", 1)
            };

            var inputData = new SaveKaMstInputData(1, 1, kaMstInputItems);

            // Act
            var result = saveKaMstInteractor.Handle(inputData);

            // Assert
            Assert.That(result.Status == SaveKaMstStatus.InvalidKaId);
        }

        [Test]
        // 科略名が20文字を超える場合、KaSnameMaxLength20 を返すことを確認するテスト
        public void TC_005_SaveKaMstInteractor_Handle_ValidateInputData_Return_KaSnameMaxLength20()
        {
            // Arrange
            var mockIKaRepository = new Mock<IKaRepository>();
            var saveKaMstInteractor = new SaveKaMstInteractor(mockIKaRepository.Object);

            mockIKaRepository.Setup(repo => repo.GetListKacode(1))
                            .Returns(new List<KaCodeMstModel> { new KaCodeMstModel("001", 1, "kaSname", "receYousikiKaCd") });


            var kaMstInputItems = new List<SaveKaMstInputItem>
            {
                new SaveKaMstInputItem(1, 1, "001", new string('あ', 21), "科名", "",1)
            };

            var inputData = new SaveKaMstInputData(1, 1, kaMstInputItems);

            // Act
            var result = saveKaMstInteractor.Handle(inputData);

            // Assert
            Assert.That(result.Status, Is.EqualTo(SaveKaMstStatus.KaSnameMaxLength20));
        }

        [Test]
        // 科名が40文字を超える場合、KaNameMaxLength40 を返すことを確認するテスト
        public void TC_006_SaveKaMstInteractor_Handle_ValidateInputData_Return_KaNameMaxLength40()
        {
            // Arrange
            var mockIKaRepository = new Mock<IKaRepository>();
            var saveKaMstInteractor = new SaveKaMstInteractor(mockIKaRepository.Object);

            mockIKaRepository.Setup(repo => repo.GetListKacode(1))
                            .Returns(new List<KaCodeMstModel> { new KaCodeMstModel("001", 1, "kaSname", "receYousikiKaCd") });

            var kaMstInputItems = new List<SaveKaMstInputItem>
            {
                new SaveKaMstInputItem(1, 1, "001", new string('あ', 20), new string('あ', 41), "",1)
            };

            var inputData = new SaveKaMstInputData(1, 1, kaMstInputItems);

            // Act
            var result = saveKaMstInteractor.Handle(inputData);

            // Assert
            Assert.That(result.Status, Is.EqualTo(SaveKaMstStatus.KaNameMaxLength40));
        }

        [Test]
        // 受付科コードが存在しない場合、ReceKaCdNotFound を返すことを確認するテスト
        // また、科名が受付科コードチェックスキップリストに含まれない場合のチェックも含む
        public void TC_007_SaveKaMstInteractor_Handle_ValidateInputData_Return_ReceKaCdNotFound()
        {
            var mockIKaRepository = new Mock<IKaRepository>();
            var saveKaMstInteractor = new SaveKaMstInteractor(mockIKaRepository.Object);

            var kaMstInputItems = new List<SaveKaMstInputItem>
            {
                new SaveKaMstInputItem(1, 1, "1", "999", "通常科", "通常科名", 1)
            };

            mockIKaRepository.Setup(repo => repo.GetListKacode(It.IsAny<int>()))
                           .Returns(new List<KaCodeMstModel>());

            var inputData = new SaveKaMstInputData(1, 1, kaMstInputItems);

            var result = saveKaMstInteractor.Handle(inputData);

            Assert.That(result.Status, Is.EqualTo(SaveKaMstStatus.ReceKaCdNotFound));
        }

        [Test]
        // 同じ科IDが複数存在する場合、CanNotDuplicateKaId を返すことを確認するテスト
        public void TC_008_SaveKaMstInteractor_Handle_ValidateInputData_Return_CanNotDuplicateKaId()
        {
            // Arrange
            var mockIKaRepository = new Mock<IKaRepository>();
            var saveKaMstInteractor = new SaveKaMstInteractor(mockIKaRepository.Object);

            var kaMstInputItems = new List<SaveKaMstInputItem>
            {
                new SaveKaMstInputItem(1, 1, "001", "科1", "科名1", "1", 1),
                new SaveKaMstInputItem(1, 1,  "001", "科1", "科名2","1", 1) // Same KaId
            };

            // 特定の入力に対する戻り値を設定
            mockIKaRepository.Setup(repo => repo.GetListKacode(1))
                            .Returns(new List<KaCodeMstModel> { new KaCodeMstModel("001", 1, "kaSname", "receYousikiKaCd") });


            var inputData = new SaveKaMstInputData(1, 1, kaMstInputItems);

            // Act
            var result = saveKaMstInteractor.Handle(inputData);

            // Assert
            Assert.That(result.Status, Is.EqualTo(SaveKaMstStatus.CanNotDuplicateKaId));
        }

        [Test]
        // 科マスタの保存が成功した場合、Successed を返すことを確認するテスト
        public void TC_009_SaveKaMstInteractor_Handle_SaveKaMst_Return_Success()
        {
            // Arrange
            var mockIKaRepository = new Mock<IKaRepository>();
            var saveKaMstInteractor = new SaveKaMstInteractor(mockIKaRepository.Object);

            var kaMstInputItems = new List<SaveKaMstInputItem>
            {
                new SaveKaMstInputItem(1, 1, "001", "科1", "科名1", "1", 1),
            };

            mockIKaRepository.Setup(repo => repo.GetListKacode(1))
                            .Returns(new List<KaCodeMstModel> { new KaCodeMstModel("001", 1, "kaSname", "receYousikiKaCd") });

            mockIKaRepository.Setup(repo => repo.SaveKaMst(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<List<KaMstModel>>()))
                           .Returns(true);

            var inputData = new SaveKaMstInputData(1, 1, kaMstInputItems);

            // Act
            var result = saveKaMstInteractor.Handle(inputData);

            // Assert
            Assert.That(result.Status, Is.EqualTo(SaveKaMstStatus.Successed));
        }

        [Test]
        // 科マスタの保存が失敗した場合、Failed を返すことを確認するテスト
        public void TC_010_SaveKaMstInteractor_Handle_SaveKaMst_Return_Failed()
        {
            // Arrange
            var mockIKaRepository = new Mock<IKaRepository>();
            var saveKaMstInteractor = new SaveKaMstInteractor(mockIKaRepository.Object);

            var kaMstInputItems = new List<SaveKaMstInputItem>
            {
                new SaveKaMstInputItem(1, 1, "001", "科1", "科名1", "1", 1),
            };

            mockIKaRepository.Setup(repo => repo.GetListKacode(1))
                            .Returns(new List<KaCodeMstModel> { new KaCodeMstModel("001", 1, "kaSname", "receYousikiKaCd") });

            mockIKaRepository.Setup(repo => repo.SaveKaMst(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<List<KaMstModel>>()))
                           .Returns(false);

            var inputData = new SaveKaMstInputData(1, 1, kaMstInputItems);

            // Act
            var result = saveKaMstInteractor.Handle(inputData);

            // Assert
            Assert.That(result.Status, Is.EqualTo(SaveKaMstStatus.Failed));
        }

        [Test]
        // 科マスタの保存で例外が発生した場合、InteractorCustomException をスローすることを確認するテスト
        public void TC_011_SaveKaMstInteractor_Handle_SaveKaMst_Throws_Exception()
        {
            // Arrange
            var mockIKaRepository = new Mock<IKaRepository>();
            var saveKaMstInteractor = new SaveKaMstInteractor(mockIKaRepository.Object);

            var kaMstInputItems = new List<SaveKaMstInputItem>
            {
                new SaveKaMstInputItem(1, 1, "001", "科1", "科名1", "1", 1),
            };

            mockIKaRepository.Setup(repo => repo.GetListKacode(1))
                            .Returns(new List<KaCodeMstModel> { new KaCodeMstModel("001", 1, "kaSname", "receYousikiKaCd") });

            mockIKaRepository.Setup(repo => repo.SaveKaMst(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<List<KaMstModel>>()))
                           .Throws(new Exception("Test exception"));

            var inputData = new SaveKaMstInputData(1, 1, kaMstInputItems);

            // Act & Assert
            var ex = Assert.Throws<InteractorCustomException>(() => saveKaMstInteractor.Handle(inputData));
            Assert.That(ex.InnerException?.Message, Is.EqualTo("Test exception"));
        }

        [Test]
        // 処理完了時にリソースが解放されることを確認するテスト
        public void TC_012_SaveKaMstInteractor_Handle_SaveKaMst_Verify_ReleaseResource()
        {
            // Arrange
            var mockIKaRepository = new Mock<IKaRepository>();
            var saveKaMstInteractor = new SaveKaMstInteractor(mockIKaRepository.Object);

            var kaMstInputItems = new List<SaveKaMstInputItem>
            {
                new SaveKaMstInputItem(1, 1, "001", "科1", "科名1", "1", 1),
            };

            // 特定の入力に対する戻り値を設定
            mockIKaRepository.Setup(repo => repo.GetListKacode(1))
                            .Returns(new List<KaCodeMstModel> { new KaCodeMstModel("001", 1, "kaSname", "receYousikiKaCd") });



            mockIKaRepository.Setup(repo => repo.SaveKaMst(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<List<KaMstModel>>()))
                           .Returns(true);

            mockIKaRepository.Setup(repo => repo.ReleaseResource()).Verifiable();

            var inputData = new SaveKaMstInputData(1, 1, kaMstInputItems);

            // Act
            saveKaMstInteractor.Handle(inputData);

            // Assert
            mockIKaRepository.Verify(repo => repo.ReleaseResource(), Times.Once);
        }

        [Test]
        public void TC_0013_SaveKaMstInteractor_Handle_ValidateInputData_Skip_ReceKaCdCheck()
        {
            var mockIKaRepository = new Mock<IKaRepository>();
            var saveKaMstInteractor = new SaveKaMstInteractor(mockIKaRepository.Object);

            var kaMstInputItems = new List<SaveKaMstInputItem>
            {
                new SaveKaMstInputItem(1, 1, "", "矯正歯科", "矯正歯科", "", 1)
            };

            mockIKaRepository.Setup(repo => repo.GetListKacode(It.IsAny<int>()))
                           .Returns(new List<KaCodeMstModel>());

            mockIKaRepository.Setup(repo => repo.SaveKaMst(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<List<KaMstModel>>()))
                           .Returns(true);

            var inputData = new SaveKaMstInputData(1, 1, kaMstInputItems);

            var result = saveKaMstInteractor.Handle(inputData);

            Assert.That(result.Status, Is.EqualTo(SaveKaMstStatus.Successed));
        }

        #endregion
    }
}
