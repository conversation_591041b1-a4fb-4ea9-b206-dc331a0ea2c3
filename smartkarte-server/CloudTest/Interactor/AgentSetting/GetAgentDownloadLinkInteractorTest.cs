using Infrastructure.Interfaces;
using Infrastructure.Options;
using Interactor.AgentSetting;
using Microsoft.Extensions.Options;
using Moq;
using UseCase.AgentSetting.GetAgentDownloadLink;
using Helper.Exceptions;

namespace CloudUnitTest.Interactor.AgentSetting;

[TestFixture]
public class GetAgentDownloadLinkInteractorTest
{
    private Mock<IAmazonS3Service> _amazonS3Service;
    private Mock<IOptions<AmazonS3Options>> _optionsAccessor;
    private Mock<AmazonS3Options> _amazonS3Options;
    private GetAgentDownloadLinkInteractor _interactor;

    [SetUp]
    public void SetUp()
    {
        _amazonS3Service = new Mock<IAmazonS3Service>();
        _amazonS3Options = new Mock<AmazonS3Options>();
        _optionsAccessor = new Mock<IOptions<AmazonS3Options>>();
        _optionsAccessor.Setup(x => x.Value).Returns(_amazonS3Options.Object);

        _interactor = new GetAgentDownloadLinkInteractor(_amazonS3Service.Object, _optionsAccessor.Object);
    }

    [Test]
    public void Handle_ReturnsSuccess_WhenValidWindowsDeviceType()
    {
        // Arrange
        var inputData = new GetAgentDownloadLinkInputData(1, "win");
        _amazonS3Options.Object.BucketName = "tf-smacli-dev2-image-bucket";
        _amazonS3Service.Setup(x => x.GetPreSignedUrlCommons(It.IsAny<string>()))
            .Returns("https://example.com/presigned-url");

        // Act
        var result = _interactor.Handle(inputData);

        // Assert
        Assert.That(result.Status, Is.EqualTo(GetAgentDownloadLinkStatus.Success));
        Assert.That(result.DownloadUrl, Is.EqualTo("https://example.com/presigned-url"));
        Assert.That(result.FileName, Is.EqualTo("SmartKarteAgent_Dev2.exe"));
    }

    [Test]
    public void Handle_ReturnsSuccess_WhenValidMacDeviceType()
    {
        // Arrange
        var inputData = new GetAgentDownloadLinkInputData(1, "mac");
        _amazonS3Options.Object.BucketName = "tf-smacli-stg2-image-bucket";
        _amazonS3Service.Setup(x => x.GetPreSignedUrlCommons(It.IsAny<string>()))
            .Returns("https://example.com/presigned-url");

        // Act
        var result = _interactor.Handle(inputData);

        // Assert
        Assert.That(result.Status, Is.EqualTo(GetAgentDownloadLinkStatus.Success));
        Assert.That(result.DownloadUrl, Is.EqualTo("https://example.com/presigned-url"));
        Assert.That(result.FileName, Is.EqualTo("SmartKarteAgent-Stg2.pkg"));
    }

    [Test]
    public void Handle_ReturnsNotImplemented_WhenNonSupportedEnvironment()
    {
        // Arrange
        var inputData = new GetAgentDownloadLinkInputData(1, "win");
        _amazonS3Options.Object.BucketName = "tf-smacli-prd-image-bucket";

        // Act
        var result = _interactor.Handle(inputData);

        // Assert
        Assert.That(result.Status, Is.EqualTo(GetAgentDownloadLinkStatus.NotImplemented));
        Assert.That(result.DownloadUrl, Is.Empty);
        Assert.That(result.FileName, Is.Empty);
    }

    [Test]
    public void Handle_ReturnsInvalidDeviceType_WhenDeviceTypeIsNull()
    {
        // Arrange
        var inputData = new GetAgentDownloadLinkInputData(1, null);

        // Act
        var result = _interactor.Handle(inputData);

        // Assert
        Assert.That(result.Status, Is.EqualTo(GetAgentDownloadLinkStatus.InvalidDeviceType));
        Assert.That(result.DownloadUrl, Is.Empty);
        Assert.That(result.FileName, Is.Empty);
    }

    [Test]
    public void Handle_ReturnsInvalidDeviceType_WhenDeviceTypeIsEmpty()
    {
        // Arrange
        var inputData = new GetAgentDownloadLinkInputData(1, "");

        // Act
        var result = _interactor.Handle(inputData);

        // Assert
        Assert.That(result.Status, Is.EqualTo(GetAgentDownloadLinkStatus.InvalidDeviceType));
        Assert.That(result.DownloadUrl, Is.Empty);
        Assert.That(result.FileName, Is.Empty);
    }

    [Test]
    public void Handle_ReturnsInvalidDeviceType_WhenDeviceTypeIsInvalid()
    {
        // Arrange
        var inputData = new GetAgentDownloadLinkInputData(1, "linux");

        // Act
        var result = _interactor.Handle(inputData);

        // Assert
        Assert.That(result.Status, Is.EqualTo(GetAgentDownloadLinkStatus.InvalidDeviceType));
        Assert.That(result.DownloadUrl, Is.Empty);
        Assert.That(result.FileName, Is.Empty);
    }

    [Test]
    public void Handle_ReturnsNotImplemented_WhenEnvironmentNotSupported()
    {
        // Arrange
        var inputData = new GetAgentDownloadLinkInputData(1, "win");
        _amazonS3Options.Object.BucketName = "tf-smacli-unknown-image-bucket";

        // Act
        var result = _interactor.Handle(inputData);

        // Assert
        Assert.That(result.Status, Is.EqualTo(GetAgentDownloadLinkStatus.NotImplemented));
        Assert.That(result.DownloadUrl, Is.Empty);
        Assert.That(result.FileName, Is.Empty);
    }

    [Test]
    public void Handle_ThrowsInteractorCustomException_WhenPresignedUrlIsNull()
    {
        // Arrange
        var inputData = new GetAgentDownloadLinkInputData(1, "win");
        _amazonS3Options.Object.BucketName = "tf-smacli-dev2-image-bucket";
        _amazonS3Service.Setup(x => x.GetPreSignedUrlCommons(It.IsAny<string>()))
            .Returns((string)null);

        // Act & Assert
        var exception = Assert.Throws<InteractorCustomException>(() => _interactor.Handle(inputData));
        Assert.That(exception.Message, Is.EqualTo("Interactor.AgentSetting"));
    }

    [Test]
    public void Handle_ThrowsInteractorCustomException_WhenPresignedUrlIsEmpty()
    {
        // Arrange
        var inputData = new GetAgentDownloadLinkInputData(1, "win");
        _amazonS3Options.Object.BucketName = "tf-smacli-dev2-image-bucket";
        _amazonS3Service.Setup(x => x.GetPreSignedUrlCommons(It.IsAny<string>()))
            .Returns("");

        // Act & Assert
        var exception = Assert.Throws<InteractorCustomException>(() => _interactor.Handle(inputData));
        Assert.That(exception.Message, Is.EqualTo("Interactor.AgentSetting"));
    }

    [Test]
    public void Handle_ThrowsInteractorCustomException_WhenExceptionOccurs()
    {
        // Arrange
        var inputData = new GetAgentDownloadLinkInputData(1, "win");
        _amazonS3Options.Object.BucketName = "tf-smacli-dev2-image-bucket";
        var originalException = new Exception("S3 service error");
        _amazonS3Service.Setup(x => x.GetPreSignedUrlCommons(It.IsAny<string>()))
            .Throws(originalException);

        // Act & Assert
        var exception = Assert.Throws<InteractorCustomException>(() => _interactor.Handle(inputData));
        Assert.That(exception.Message, Is.EqualTo("Interactor.AgentSetting"));
        Assert.That(exception.InnerException, Is.EqualTo(originalException));
    }

    [Test]
    public void Handle_GeneratesCorrectS3Key_ForWindowsDev2()
    {
        // Arrange
        var inputData = new GetAgentDownloadLinkInputData(1, "win");
        _amazonS3Options.Object.BucketName = "tf-smacli-dev2-image-bucket";
        _amazonS3Service.Setup(x => x.GetPreSignedUrlCommons(It.IsAny<string>()))
            .Returns("https://example.com/presigned-url");

        // Act
        var result = _interactor.Handle(inputData);

        // Assert
        _amazonS3Service.Verify(x => x.GetPreSignedUrlCommons("SmartKarteAgent/SmartKarteAgent_Dev2.exe"), Times.Once);
    }

    [Test]
    public void Handle_GeneratesCorrectS3Key_ForMacStg2()
    {
        // Arrange
        var inputData = new GetAgentDownloadLinkInputData(1, "mac");
        _amazonS3Options.Object.BucketName = "tf-smacli-stg2-image-bucket";
        _amazonS3Service.Setup(x => x.GetPreSignedUrlCommons(It.IsAny<string>()))
            .Returns("https://example.com/presigned-url");

        // Act
        var result = _interactor.Handle(inputData);

        // Assert
        _amazonS3Service.Verify(x => x.GetPreSignedUrlCommons("SmartKarteAgent/SmartKarteAgent-Stg2.pkg"), Times.Once);
    }

    [Test]
    public void Handle_GeneratesCorrectS3Key_ForNonSupportedEnvironment()
    {
        // Arrange
        var inputData = new GetAgentDownloadLinkInputData(1, "win");
        _amazonS3Options.Object.BucketName = "tf-smacli-prd-image-bucket";
        _amazonS3Service.Setup(x => x.GetPreSignedUrlCommons(It.IsAny<string>()))
            .Returns("https://example.com/presigned-url");

        // Act
        var result = _interactor.Handle(inputData);

        // Assert
        // Should not call S3 service for non-supported environments
        _amazonS3Service.Verify(x => x.GetPreSignedUrlCommons(It.IsAny<string>()), Times.Never);
    }

    [Test]
    public void Handle_GeneratesCorrectS3Key_ForMacNonSupportedEnvironment()
    {
        // Arrange
        var inputData = new GetAgentDownloadLinkInputData(1, "mac");
        _amazonS3Options.Object.BucketName = "tf-smacli-prd-image-bucket";
        _amazonS3Service.Setup(x => x.GetPreSignedUrlCommons(It.IsAny<string>()))
            .Returns("https://example.com/presigned-url");

        // Act
        var result = _interactor.Handle(inputData);

        // Assert
        // Should not call S3 service for non-supported environments
        _amazonS3Service.Verify(x => x.GetPreSignedUrlCommons(It.IsAny<string>()), Times.Never);
    }
}
