using Domain.Models.Santei;
using Domain.Models.HpInf;
using Domain.Models.SystemConf;
using Domain.Models.MstItem;
using Helper.Constants;
using Interactor.SystemConf;
using Moq;
using UseCase.SystemConf.SaveSystemSetting;
using CalculateService.Interface;

namespace CloudUnitTest.Interactor.SystemConf;

[TestFixture]
public class SaveSystemSettingInteractorTest
{
    private Mock<ISystemConfRepository> _systemConfRepository;
    private Mock<IHpInfRepository> _hpInfRepository;
    private Mock<ISanteiInfRepository> _santeiInfRepository;
    private Mock<IMstItemRepository> _mstItemRepository;
    private SaveSystemSettingInteractor _interactor;
    private Mock<IEmrLogger> _emrLogger;

    [SetUp]
    public void SetUp()
    {
        _systemConfRepository = new Mock<ISystemConfRepository>();
        _hpInfRepository = new Mock<IHpInfRepository>();
        _santeiInfRepository = new Mock<ISanteiInfRepository>();
        _mstItemRepository = new Mock<IMstItemRepository>();
        _emrLogger = new Mock<IEmrLogger>();
        _interactor = new SaveSystemSettingInteractor(
            _systemConfRepository.Object,
            _hpInfRepository.Object,
            _santeiInfRepository.Object,
            _mstItemRepository.Object,
            _emrLogger.Object
        );
    }

    [Test]
    public void Handle_ReturnsSuccess_WhenSanteiInfsAreValid()
    {
        var santeiInfs = new List<SanteiInfDetailItem>
        {
            new(0, 0, "A", 20220101, 20220131, 0, 0, "", "", "", false, ModelStatus.Added),
            new(0, 0, "A", 20220201, 20220228, 0, 0, "", "", "", false, ModelStatus.Added)
        };
        var inputData = new SaveSystemSettingInputData(1, 1, new List<HpInfItem>(), new List<SystemConfMenuItem>(), santeiInfs, new List<KensaCenterMstItem>());
        var santeiInfRepo = new Mock<ISanteiInfRepository>();
        santeiInfRepo.Setup(r => r.GetAutoSanteiMstByItemCd(1, "A")).Returns(new List<AutoSanteiMstModel>());
        var interactor = new SaveSystemSettingInteractor(
            _systemConfRepository.Object,
            _hpInfRepository.Object,
            santeiInfRepo.Object,
            _mstItemRepository.Object,
            _emrLogger.Object
        );

        var result = interactor.Handle(inputData);
        Assert.That(result.Status, Is.EqualTo(SaveSystemSettingStatus.Successed));
    }

    [Test]
    public void Handle_ReturnsAutoSanteiDateOverlap_WhenStartDateGreaterThanEndDate()
    {
        var santeiInfs = new List<SanteiInfDetailItem>
        {
            new(0, 0, "A", 20220131, 20220101, 0, 0, "", "", "", false, ModelStatus.Added)
        };
        var inputData = new SaveSystemSettingInputData(1, 1, new List<HpInfItem>(), new List<SystemConfMenuItem>(), santeiInfs, new List<KensaCenterMstItem>());
        var santeiInfRepo = new Mock<ISanteiInfRepository>();
        santeiInfRepo.Setup(r => r.GetAutoSanteiMstByItemCd(1, "A")).Returns(new List<AutoSanteiMstModel>());
        var interactor = new SaveSystemSettingInteractor(
            _systemConfRepository.Object,
            _hpInfRepository.Object,
            santeiInfRepo.Object,
            _mstItemRepository.Object,
            _emrLogger.Object
        );

        var result = interactor.Handle(inputData);
        Assert.That(result.Status, Is.EqualTo(SaveSystemSettingStatus.AutoSanteiDateOverlap));
    }

    [Test]
    public void Handle_ReturnsAutoSanteiDateOverlap_WhenStartDateEqualsEndDate()
    {
        var santeiInfs = new List<SanteiInfDetailItem>
        {
            new(0, 0, "A", 20220101, 20220101, 0, 0, "", "", "", false, ModelStatus.Added)
        };
        var inputData = new SaveSystemSettingInputData(1, 1, new List<HpInfItem>(), new List<SystemConfMenuItem>(), santeiInfs, new List<KensaCenterMstItem>());
        var santeiInfRepo = new Mock<ISanteiInfRepository>();
        santeiInfRepo.Setup(r => r.GetAutoSanteiMstByItemCd(1, "A")).Returns(new List<AutoSanteiMstModel>());
        var interactor = new SaveSystemSettingInteractor(
            _systemConfRepository.Object,
            _hpInfRepository.Object,
            santeiInfRepo.Object,
            _mstItemRepository.Object,
            _emrLogger.Object
        );
        var result = interactor.Handle(inputData);
        Assert.That(result.Status, Is.EqualTo(SaveSystemSettingStatus.AutoSanteiDateOverlap));
    }

    [Test]
    public void Handle_ReturnsAutoSanteiDateOverlap_WhenDateRangesOverlap()
    {
        var santeiInfs = new List<SanteiInfDetailItem>
        {
            new(0, 0, "A", 20220101, 20220131, 0, 0, "", "", "", false, ModelStatus.Added),
            new(0, 0, "A", 20220115, 20220215, 0, 0, "", "", "", false, ModelStatus.Added)
        };
        var inputData = new SaveSystemSettingInputData(1, 1, new List<HpInfItem>(), new List<SystemConfMenuItem>(), santeiInfs, new List<KensaCenterMstItem>());
        var santeiInfRepo = new Mock<ISanteiInfRepository>();
        santeiInfRepo.Setup(r => r.GetAutoSanteiMstByItemCd(1, "A")).Returns(new List<AutoSanteiMstModel>());
        var interactor = new SaveSystemSettingInteractor(
            _systemConfRepository.Object,
            _hpInfRepository.Object,
            santeiInfRepo.Object,
            _mstItemRepository.Object,
            _emrLogger.Object
        );
        var result = interactor.Handle(inputData);
        Assert.That(result.Status, Is.EqualTo(SaveSystemSettingStatus.AutoSanteiDateOverlap));
    }

    [Test]
    public void Handle_ProcessesDeletedModifiedAddedLogic()
    {
        var santeiInfs = new List<SanteiInfDetailItem>
        {
            // Added
            new(1, 0, "A", 20220101, 20220131, 0, 0, "", "", "", false, ModelStatus.Added),
            // Modified
            new(2, 0, "A", 20220201, 20220228, 0, 0, "", "", "", false, ModelStatus.Modified),
            // Deleted
            new(3, 0, "A", 20220301, 20220331, 0, 0, "", "", "", false, ModelStatus.Deleted)
        };
        var existing = new List<AutoSanteiMstModel>
        {
            new(2, "A", 1, 20220201, 20220228),
            new(3, "A", 1, 20220301, 20220331)
        };
        var inputData = new SaveSystemSettingInputData(1, 1, new List<HpInfItem>(), new List<SystemConfMenuItem>(), santeiInfs, new List<KensaCenterMstItem>());
        var santeiInfRepo = new Mock<ISanteiInfRepository>();
        santeiInfRepo.Setup(r => r.GetAutoSanteiMstByItemCd(1, "A")).Returns(existing);
        var interactor = new SaveSystemSettingInteractor(
            _systemConfRepository.Object,
            _hpInfRepository.Object,
            santeiInfRepo.Object,
            _mstItemRepository.Object,
            _emrLogger.Object
        );
        var result = interactor.Handle(inputData);
        Assert.That(result.Status, Is.EqualTo(SaveSystemSettingStatus.Successed));
    }
}
