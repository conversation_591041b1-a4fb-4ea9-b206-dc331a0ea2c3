using Domain.Models.HpInf;
using Domain.Models.SystemConf;
using Domain.Models.PatientInfor;
using Domain.Models.Reception;
using Domain.Models.User;
using Domain.Models.Fincode;
using Domain.Models.DenkaruApi;
using PostgreDataContext;
using Interactor.Accounting;
using Infrastructure.Repositories;
using Helper.Enum;
using Helper.Constants;
using Microsoft.Extensions.Configuration;
using Moq;
using UseCase.Accounting.SaveAccounting;
using Domain.Models.AuditLog;
using Entity.Tenant;

namespace CloudUnitTest.Interactor.Accounting
{
    public class SaveAccountingInteractorTest : BaseUT
    {
        private Mock<IConfiguration>? _mockConfiguration;
        private Mock<ISystemConfRepository>? _mockSystemConfRepository;
        private Mock<IUserRepository>? _mockUserRepository;
        private Mock<IHpInfRepository>? _mockHpInfRepository;
        private Mock<IPatientInforRepository>? _mockPatientInforRepository;
        private Mock<IReceptionRepository>? _mockReceptionRepository;
        private Mock<IAuditLogRepository>? _mockAuditLogRepository;
        private Mock<IFincodeRepository>? _mockFincodeRepository;
        private AccountingRepository? _accountingRepository;

        [SetUp]
        public void Setup()
        {
            _mockConfiguration = new Mock<IConfiguration>();
            _mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("localhost");
            _mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");

            _mockSystemConfRepository = new Mock<ISystemConfRepository>();
            _mockUserRepository = new Mock<IUserRepository>();
            _mockHpInfRepository = new Mock<IHpInfRepository>();
            _mockPatientInforRepository = new Mock<IPatientInforRepository>();
            _mockReceptionRepository = new Mock<IReceptionRepository>();
            _mockAuditLogRepository = new Mock<IAuditLogRepository>();
            _mockFincodeRepository = new Mock<IFincodeRepository>();

            _mockUserRepository.Setup(repo => repo.CheckExistedUserId(It.IsAny<int>(), It.IsAny<int>()))
                .Returns((int hpId, int userId) => userId > 0);
            _mockHpInfRepository.Setup(repo => repo.CheckHpId(It.IsAny<int>())).Returns(true);
            _mockPatientInforRepository.Setup(repo => repo.CheckExistIdList(It.IsAny<int>(), It.IsAny<List<long>>()))
                .Returns((int hpId, List<long> ptIds) => ptIds.All(ptId => ptId > 0));

            _accountingRepository = new AccountingRepository(TenantProvider, _mockConfiguration.Object);
        }

        [Test]
        public void Handle_PositiveSeikyuAndMisyukin_CheckUpdates()
        {
            // 正の請求額と正の未収金を持つケースを検証する。
            // 確認内容: 入金、請求,来院データの状態が正しく更新されていること。

            var saveAccountDueListInteractor = new SaveAccountingInteractor(_accountingRepository!, _mockSystemConfRepository!.Object, _mockUserRepository!.Object, _mockHpInfRepository!.Object,
                                                                                _mockPatientInforRepository!.Object, _mockReceptionRepository!.Object, _mockAuditLogRepository!.Object, _mockFincodeRepository!.Object);
            var tenant = TenantProvider.GetTrackingTenantDataContext();

            Random random = new Random();
            int hpId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int userId = random.Next(999, 99999);
            long raiinNo1 = random.Next(999, 99999);
            long raiinNo2 = raiinNo1 + 1;
            long raiinNo3 = raiinNo1 + 2;
            long raiinNo4 = raiinNo1 + 3;
            long raiinNo5 = raiinNo1 + 4;
            long raiinNo6 = raiinNo1 + 5;
            long oyaRaiinNo = raiinNo1;
            int sinDate = ********;

            //同一来院のテストデータ
            tenant.RaiinInfs.AddRange(
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo1, oyaRaiinNo, RaiinState.ConsultationCompleted),
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo2, oyaRaiinNo, RaiinState.AmountConfirmed),
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo3, oyaRaiinNo, RaiinState.AmountConfirmed),
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo4, oyaRaiinNo, RaiinState.Paid),
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo5, oyaRaiinNo, RaiinState.Paid),
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo6, oyaRaiinNo, RaiinState.Paid)
            );

            //総請求額 12000円(請求額:60000円 + 未収金:6000円)
            tenant.SyunoSeikyus.AddRange(
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo1, NyukinKbnEnums.Unsettled, 100, 1100, 0, 0),
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo2, NyukinKbnEnums.Unsettled, 100, 2100, 0, 0),
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo3, NyukinKbnEnums.Unsettled, 100, 3100, 0, 0),
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo4, NyukinKbnEnums.PartiallySettled, 100, 4100, 0, 0),
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo5, NyukinKbnEnums.PartiallySettled, 100, 5100, 0, 0),
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo6, NyukinKbnEnums.FullySettled, 100, 6100, 0, 0)
            );
            tenant.SyunoNyukin.AddRange(
                CreateSyunoNyukin(hpId, ptId, sinDate, raiinNo4, 500, 1500, PaymentMethodCdEnum.Cash),
                CreateSyunoNyukin(hpId, ptId, sinDate, raiinNo5, 500, 500, PaymentMethodCdEnum.Cash),
                CreateSyunoNyukin(hpId, ptId, sinDate, raiinNo6, 500, 5500, PaymentMethodCdEnum.Cash)
            );
            tenant.SaveChanges();

            //リクエスト
            var inputData = new SaveAccountingInputData(
                hpId: hpId,
                ptId: ptId,
                userId: userId,
                sinDate: sinDate,
                raiinNo: oyaRaiinNo,
                sumAdjust: 12000,
                thisWari: 1500,
                credit: 10000,
                payType: (int)PaymentMethodCdEnum.Cash,
                comment: "test",
                isDisCharged: false,
                kaikeiTime: "093000",
                isOperator: 1,
                operatorName: "test"
            );

            var result = saveAccountDueListInteractor.Handle(inputData);
            Assert.That(result.Status, Is.EqualTo(SaveAccountingStatus.Success));

            //来院データチェック
            tenant = TenantProvider.GetTrackingTenantDataContext();

            AssertRaiinInfStatus(tenant, raiinNo1, RaiinState.Paid);
            AssertRaiinInfStatus(tenant, raiinNo2, RaiinState.Paid);
            AssertRaiinInfStatus(tenant, raiinNo3, RaiinState.Paid);
            AssertRaiinInfStatus(tenant, raiinNo4, RaiinState.Paid);
            AssertRaiinInfStatus(tenant, raiinNo5, RaiinState.Paid);
            AssertRaiinInfStatus(tenant, raiinNo6, RaiinState.Paid);
            AssertSyunoSeikyuStatus(tenant, raiinNo1, NyukinKbnEnums.FullySettled);
            AssertSyunoSeikyuStatus(tenant, raiinNo2, NyukinKbnEnums.FullySettled);
            AssertSyunoSeikyuStatus(tenant, raiinNo3, NyukinKbnEnums.FullySettled);
            AssertSyunoSeikyuStatus(tenant, raiinNo4, NyukinKbnEnums.FullySettled);
            AssertSyunoSeikyuStatus(tenant, raiinNo5, NyukinKbnEnums.PartiallySettled);
            AssertSyunoSeikyuStatus(tenant, raiinNo6, NyukinKbnEnums.FullySettled);

            //入金データチェック
            AssertSyunoNyukin(tenant, raiinNo1, 0, 0, 1000);
            AssertSyunoNyukin(tenant, raiinNo2, 0, 1500, 500);
            AssertSyunoNyukin(tenant, raiinNo3, 0, 3000, 0);
            AssertSyunoNyukin(tenant, raiinNo4, 0, 1500, 500);
            AssertSyunoNyukin(tenant, raiinNo4, 1, 2000, 0);
            AssertSyunoNyukin(tenant, raiinNo5, 0, 500, 500);
            AssertSyunoNyukin(tenant, raiinNo5, 1, 3500, 0);
            AssertSyunoNyukin(tenant, raiinNo6, 0, 5500, 500);

            // データを削除
            CleanupTestData(tenant, hpId);
        }

        [Test]
        public void Handle_PositiveSeikyuAndMisyukin_NegativeNyukinGakuAndAdjustFutan_CheckUpdates()
        {
            // 正の請求額と正の未収金、負の入金額と負の調整額を持つケースを検証する。
            // 確認内容: 入金、請求,来院データの状態が正しく更新されていること。
            var _auditLogRepository = new AuditLogRepository(TenantProvider);

            var saveAccountDueListInteractor = new SaveAccountingInteractor(_accountingRepository!, _mockSystemConfRepository!.Object, _mockUserRepository!.Object, _mockHpInfRepository!.Object,
                                                                                _mockPatientInforRepository!.Object, _mockReceptionRepository!.Object, _auditLogRepository, _mockFincodeRepository!.Object);
            var tenant = TenantProvider.GetTrackingTenantDataContext();

            Random random = new Random();
            int hpId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int userId = random.Next(999, 99999);
            long raiinNo1 = random.Next(999, 99999);
            long raiinNo2 = raiinNo1 + 1;
            long raiinNo3 = raiinNo1 + 2;
            long raiinNo4 = raiinNo1 + 3;
            long raiinNo5 = raiinNo1 + 4;
            long raiinNo6 = raiinNo1 + 5;
            long oyaRaiinNo = raiinNo1;
            int sinDate = ********;

            //同一来院のテストデータ
            tenant.RaiinInfs.AddRange(
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo1, oyaRaiinNo, RaiinState.ConsultationCompleted),
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo2, oyaRaiinNo, RaiinState.AmountConfirmed),
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo3, oyaRaiinNo, RaiinState.AmountConfirmed),
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo4, oyaRaiinNo, RaiinState.Paid),
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo5, oyaRaiinNo, RaiinState.Paid),
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo6, oyaRaiinNo, RaiinState.Paid)
            );

            //総請求額 12000円(請求額:60000円 + 未収金:6000円)
            tenant.SyunoSeikyus.AddRange(
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo1, NyukinKbnEnums.Unsettled, 100, 1100, 0, 0),
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo2, NyukinKbnEnums.Unsettled, 100, 2100, 0, 0),
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo3, NyukinKbnEnums.Unsettled, 100, 3100, 0, 0),
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo4, NyukinKbnEnums.PartiallySettled, 100, 4100, 0, 0),
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo5, NyukinKbnEnums.PartiallySettled, 100, 5100, 0, 0),
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo6, NyukinKbnEnums.FullySettled, 100, 6100, 0, 0)
            );
            tenant.SyunoNyukin.AddRange(
                CreateSyunoNyukin(hpId, ptId, sinDate, raiinNo4, 500, 1500, PaymentMethodCdEnum.Cash),
                CreateSyunoNyukin(hpId, ptId, sinDate, raiinNo5, 500, 500, PaymentMethodCdEnum.Cash),
                CreateSyunoNyukin(hpId, ptId, sinDate, raiinNo6, 500, 5500, PaymentMethodCdEnum.Cash)
            );
            tenant.SaveChanges();

            //リクエスト
            var inputData = new SaveAccountingInputData(
                hpId: hpId,
                ptId: ptId,
                userId: userId,
                sinDate: sinDate,
                raiinNo: oyaRaiinNo,
                sumAdjust: 12000,
                thisWari: -1500,
                credit: -10000,
                payType: (int)PaymentMethodCdEnum.Cash,
                comment: "test",
                isDisCharged: false,
                kaikeiTime: "093000",
                isOperator: 1,
                operatorName: "test"
            );

            var result = saveAccountDueListInteractor.Handle(inputData);
            Assert.That(result.Status, Is.EqualTo(SaveAccountingStatus.Success));

            //来院データチェック
            tenant = TenantProvider.GetTrackingTenantDataContext();
            AssertRaiinInfStatus(tenant, raiinNo1, RaiinState.Paid);
            AssertRaiinInfStatus(tenant, raiinNo2, RaiinState.Paid);
            AssertRaiinInfStatus(tenant, raiinNo3, RaiinState.Paid);
            AssertRaiinInfStatus(tenant, raiinNo4, RaiinState.Paid);
            AssertRaiinInfStatus(tenant, raiinNo5, RaiinState.Paid);
            AssertRaiinInfStatus(tenant, raiinNo6, RaiinState.Paid);
            AssertSyunoSeikyuStatus(tenant, raiinNo1, NyukinKbnEnums.PartiallySettled);
            AssertSyunoSeikyuStatus(tenant, raiinNo2, NyukinKbnEnums.PartiallySettled);
            AssertSyunoSeikyuStatus(tenant, raiinNo3, NyukinKbnEnums.PartiallySettled);
            AssertSyunoSeikyuStatus(tenant, raiinNo4, NyukinKbnEnums.PartiallySettled);
            AssertSyunoSeikyuStatus(tenant, raiinNo5, NyukinKbnEnums.PartiallySettled);
            AssertSyunoSeikyuStatus(tenant, raiinNo6, NyukinKbnEnums.FullySettled);

            //入金データチェック
            AssertSyunoNyukin(tenant, raiinNo1, 0, -10000, -1500);
            AssertSyunoNyukin(tenant, raiinNo2, 0, 0, 0);
            AssertSyunoNyukin(tenant, raiinNo3, 0, 0, 0);
            AssertSyunoNyukin(tenant, raiinNo4, 0, 1500, 500);
            AssertSyunoNyukin(tenant, raiinNo5, 0, 500, 500);
            AssertSyunoNyukin(tenant, raiinNo6, 0, 5500, 500);

            CleanupTestData(tenant, hpId);
        }

        [Test]
        public void Handle_NegativeSeikyuMatchesAdjustAndCredit_CheckUpdates()
        {
            // 負の請求額で請求額と入金額が一致するケースを検証する。
            // 確認内容: 入金、請求,来院データの状態が正しく更新されていること。

            var saveAccountDueListInteractor = new SaveAccountingInteractor(_accountingRepository!, _mockSystemConfRepository!.Object, _mockUserRepository!.Object, _mockHpInfRepository!.Object,
                                                                                _mockPatientInforRepository!.Object, _mockReceptionRepository!.Object, _mockAuditLogRepository!.Object, _mockFincodeRepository!.Object);
            var tenant = TenantProvider.GetTrackingTenantDataContext();

            Random random = new Random();
            int hpId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int userId = random.Next(999, 99999);
            long raiinNo1 = random.Next(999, 99999);
            long raiinNo2 = raiinNo1 + 1;
            long raiinNo3 = raiinNo1 + 2;
            long raiinNo4 = raiinNo1 + 3;
            long raiinNo5 = raiinNo1 + 4;
            long oyaRaiinNo = raiinNo1;
            int sinDate = ********;

            //同一来院のテストデータ
            tenant.RaiinInfs.AddRange(
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo1, oyaRaiinNo, RaiinState.AmountConfirmed),
                    CreateRaiinInf(hpId, ptId, sinDate, raiinNo2, oyaRaiinNo, RaiinState.AmountConfirmed),
                    CreateRaiinInf(hpId, ptId, sinDate, raiinNo3, oyaRaiinNo, RaiinState.AmountConfirmed),
                    CreateRaiinInf(hpId, ptId, sinDate, raiinNo4, oyaRaiinNo, RaiinState.Paid),
                    CreateRaiinInf(hpId, ptId, sinDate, raiinNo5, oyaRaiinNo, RaiinState.Paid)
                );

            //総請求額 -2400円(請求額:600円 + 未収金:-3000円)
            tenant.SyunoSeikyus.AddRange(
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo1, NyukinKbnEnums.Unsettled, 100, 200, 0, 0),
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo2, NyukinKbnEnums.Unsettled, 100, 300, 0, 0),
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo3, NyukinKbnEnums.Unsettled, 100, 400, 0, 0),
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo4, NyukinKbnEnums.PartiallySettled, 100, 4100, 0, 0),
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo5, NyukinKbnEnums.PartiallySettled, 100, 3100, 0, 0)
            );
            tenant.SyunoNyukin.AddRange(
                CreateSyunoNyukin(hpId, ptId, sinDate, raiinNo4, 1000, 5000, PaymentMethodCdEnum.Cash),
                CreateSyunoNyukin(hpId, ptId, sinDate, raiinNo5, 0, 4000, PaymentMethodCdEnum.Cash)
            );
            tenant.SaveChanges();

            //リクエスト
            var inputData = new SaveAccountingInputData(
                hpId: hpId,
                ptId: ptId,
                userId: userId,
                sinDate: sinDate,
                raiinNo: oyaRaiinNo,
                sumAdjust: -2400,
                thisWari: 0,
                credit: -2400,
                payType: (int)PaymentMethodCdEnum.Cash,
                comment: "test",
                isDisCharged: false,
                kaikeiTime: "093000",
                isOperator: 1,
                operatorName: "test"
            );

            var result = saveAccountDueListInteractor.Handle(inputData);
            Assert.That(result.Status, Is.EqualTo(SaveAccountingStatus.Success));

            //来院データチェック
            tenant = TenantProvider.GetTrackingTenantDataContext();
            AssertRaiinInfStatus(tenant, raiinNo1, RaiinState.Paid);
            AssertRaiinInfStatus(tenant, raiinNo2, RaiinState.Paid);
            AssertRaiinInfStatus(tenant, raiinNo3, RaiinState.Paid);
            AssertRaiinInfStatus(tenant, raiinNo4, RaiinState.Paid);
            AssertRaiinInfStatus(tenant, raiinNo5, RaiinState.Paid);
            AssertSyunoSeikyuStatus(tenant, raiinNo1, NyukinKbnEnums.FullySettled);
            AssertSyunoSeikyuStatus(tenant, raiinNo2, NyukinKbnEnums.FullySettled);
            AssertSyunoSeikyuStatus(tenant, raiinNo3, NyukinKbnEnums.FullySettled);
            AssertSyunoSeikyuStatus(tenant, raiinNo4, NyukinKbnEnums.FullySettled);
            AssertSyunoSeikyuStatus(tenant, raiinNo5, NyukinKbnEnums.FullySettled);

            //入金データチェック
            AssertSyunoNyukin(tenant, raiinNo1, 0, 100, 0);
            AssertSyunoNyukin(tenant, raiinNo2, 0, 200, 0);
            AssertSyunoNyukin(tenant, raiinNo3, 0, 300, 0);
            AssertSyunoNyukin(tenant, raiinNo4, 0, 5000, 1000);
            AssertSyunoNyukin(tenant, raiinNo4, 1, -2000, 0);
            AssertSyunoNyukin(tenant, raiinNo5, 0, 4000, 0);
            AssertSyunoNyukin(tenant, raiinNo5, 1, -1000, 0);


            CleanupTestData(tenant, hpId);
        }

        [Test]
        public void Handle_PositiveSeikyuAndNegativeMisyukin_MatchingSumAdjust_CheckUpdates()
        {
            // 正の請求額,負の未収金および thisWari + credit = sumAdjustになるケース
            // 確認内容: 入金、請求,来院データの状態が正しく更新されていること。

            var saveAccountDueListInteractor = new SaveAccountingInteractor(_accountingRepository!, _mockSystemConfRepository!.Object, _mockUserRepository!.Object, _mockHpInfRepository!.Object,
                                                                                _mockPatientInforRepository!.Object, _mockReceptionRepository!.Object, _mockAuditLogRepository!.Object, _mockFincodeRepository!.Object);
            var tenant = TenantProvider.GetTrackingTenantDataContext();

            Random random = new Random();
            int hpId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int userId = random.Next(999, 99999);
            long raiinNo1 = random.Next(999, 99999);
            long raiinNo2 = raiinNo1 + 1;
            long raiinNo3 = raiinNo1 + 2;
            long raiinNo4 = raiinNo1 + 3;
            long raiinNo5 = raiinNo1 + 4;

            long oyaRaiinNo = raiinNo1;
            int sinDate = ********;

            //同一来院のテストデータ
            tenant.RaiinInfs.AddRange(
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo1, oyaRaiinNo, RaiinState.AmountConfirmed),
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo2, oyaRaiinNo, RaiinState.AmountConfirmed),
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo3, oyaRaiinNo, RaiinState.AmountConfirmed),
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo4, oyaRaiinNo, RaiinState.Paid),
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo5, oyaRaiinNo, RaiinState.Paid)
            );

            //総請求額 200円(請求額:800円 + 未収金:-600円)
            tenant.SyunoSeikyus.AddRange(
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo1, NyukinKbnEnums.Unsettled, 0, 100, 0, 0),
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo2, NyukinKbnEnums.Unsettled, 0, 200, 0, 0),
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo3, NyukinKbnEnums.Unsettled, 0, 500, 0, 0),
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo4, NyukinKbnEnums.PartiallySettled, 0, 500, 0, 0),
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo5, NyukinKbnEnums.PartiallySettled, 0, 100, 0, 0)
            );
            tenant.SyunoNyukin.AddRange(
                CreateSyunoNyukin(hpId, ptId, sinDate, raiinNo4, 500, 500, PaymentMethodCdEnum.Cash),
                CreateSyunoNyukin(hpId, ptId, sinDate, raiinNo5, 100, 100, PaymentMethodCdEnum.Cash)
            );
            tenant.SaveChanges();

            //リクエスト
            var inputData = new SaveAccountingInputData(
                hpId: hpId,
                ptId: ptId,
                userId: userId,
                sinDate: sinDate,
                raiinNo: oyaRaiinNo,
                sumAdjust: 200,
                thisWari: 150,
                credit: 50,
                payType: (int)PaymentMethodCdEnum.Cash,
                comment: "test",
                isDisCharged: false,
                kaikeiTime: "093000",
                isOperator: 1,
                operatorName: "test"
            );

            var result = saveAccountDueListInteractor.Handle(inputData);
            Assert.That(result.Status, Is.EqualTo(SaveAccountingStatus.Success));

            tenant = TenantProvider.GetTrackingTenantDataContext();
            AssertRaiinInfStatus(tenant, raiinNo1, RaiinState.Paid);
            AssertRaiinInfStatus(tenant, raiinNo2, RaiinState.Paid);
            AssertRaiinInfStatus(tenant, raiinNo3, RaiinState.Paid);
            AssertRaiinInfStatus(tenant, raiinNo4, RaiinState.Paid);
            AssertRaiinInfStatus(tenant, raiinNo5, RaiinState.Paid);
            AssertSyunoSeikyuStatus(tenant, raiinNo1, NyukinKbnEnums.FullySettled);
            AssertSyunoSeikyuStatus(tenant, raiinNo2, NyukinKbnEnums.FullySettled);
            AssertSyunoSeikyuStatus(tenant, raiinNo3, NyukinKbnEnums.FullySettled);
            AssertSyunoSeikyuStatus(tenant, raiinNo4, NyukinKbnEnums.FullySettled);
            AssertSyunoSeikyuStatus(tenant, raiinNo5, NyukinKbnEnums.FullySettled);

            //入金データチェック
            AssertSyunoNyukin(tenant, raiinNo1, 0, 0, 100);
            AssertSyunoNyukin(tenant, raiinNo2, 0, 150, 50);
            AssertSyunoNyukin(tenant, raiinNo3, 0, 500, 0);
            AssertSyunoNyukin(tenant, raiinNo4, 0, 500, 500);
            AssertSyunoNyukin(tenant, raiinNo4, 1, -500, 0);
            AssertSyunoNyukin(tenant, raiinNo5, 0, 100, 100);
            AssertSyunoNyukin(tenant, raiinNo5, 1, -100, 0);

            CleanupTestData(tenant, hpId);
        }

        [Test]
        public void Handle_PositiveSeikyuGakuAndNegativeMisyukinWhenThisWariAndCreditLessThanSumAdjust_CheckUpdates()
        {
            // 正の請求額,負の未収金 および thisWari + credit < sumAdjustになるケース
            // 確認内容: 入金、請求,来院データの状態が正しく更新されていること。

            var saveAccountDueListInteractor = new SaveAccountingInteractor(_accountingRepository!, _mockSystemConfRepository!.Object, _mockUserRepository!.Object, _mockHpInfRepository!.Object,
                                                                                _mockPatientInforRepository!.Object, _mockReceptionRepository!.Object, _mockAuditLogRepository!.Object, _mockFincodeRepository!.Object);
            var tenant = TenantProvider.GetTrackingTenantDataContext();

            Random random = new Random();
            int hpId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int userId = random.Next(999, 99999);
            long raiinNo1 = random.Next(999, 99999);
            long raiinNo2 = raiinNo1 + 1;
            long raiinNo3 = raiinNo1 + 2;
            long raiinNo4 = raiinNo1 + 3;
            long raiinNo5 = raiinNo1 + 4;

            long oyaRaiinNo = raiinNo1;
            int sinDate = ********;

            //同一来院のテストデータ
            tenant.RaiinInfs.AddRange(
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo1, oyaRaiinNo, RaiinState.AmountConfirmed),
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo2, oyaRaiinNo, RaiinState.AmountConfirmed),
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo3, oyaRaiinNo, RaiinState.AmountConfirmed),
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo4, oyaRaiinNo, RaiinState.Paid),
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo5, oyaRaiinNo, RaiinState.Paid)
            );

            //総請求額 200円(請求額:800円 + 未収金:-600円)
            tenant.SyunoSeikyus.AddRange(
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo1, NyukinKbnEnums.Unsettled, 0, 100, 0, 0),
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo2, NyukinKbnEnums.Unsettled, 0, 200, 0, 0),
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo3, NyukinKbnEnums.Unsettled, 0, 500, 0, 0),
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo4, NyukinKbnEnums.PartiallySettled, 0, 500, 0, 0),
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo5, NyukinKbnEnums.PartiallySettled, 0, 100, 0, 0)
            );
            tenant.SyunoNyukin.AddRange(
                CreateSyunoNyukin(hpId, ptId, sinDate, raiinNo4, 500, 500, PaymentMethodCdEnum.Cash),
                CreateSyunoNyukin(hpId, ptId, sinDate, raiinNo5, 100, 100, PaymentMethodCdEnum.Cash)
            );
            tenant.SaveChanges();

            //リクエスト
            var inputData = new SaveAccountingInputData(
                hpId: hpId,
                ptId: ptId,
                userId: userId,
                sinDate: sinDate,
                raiinNo: oyaRaiinNo,
                sumAdjust: 200,
                thisWari: 150,
                credit: 10,
                payType: (int)PaymentMethodCdEnum.Cash,
                comment: "test",
                isDisCharged: false,
                kaikeiTime: "093000",
                isOperator: 1,
                operatorName: "test"
            );

            var result = saveAccountDueListInteractor.Handle(inputData);
            Assert.That(result.Status, Is.EqualTo(SaveAccountingStatus.Success));

            //来院データチェック
            tenant = TenantProvider.GetTrackingTenantDataContext();
            AssertRaiinInfStatus(tenant, raiinNo1, RaiinState.Paid);
            AssertRaiinInfStatus(tenant, raiinNo2, RaiinState.Paid);
            AssertRaiinInfStatus(tenant, raiinNo3, RaiinState.Paid);
            AssertRaiinInfStatus(tenant, raiinNo4, RaiinState.Paid);
            AssertRaiinInfStatus(tenant, raiinNo5, RaiinState.Paid);
            AssertSyunoSeikyuStatus(tenant, raiinNo1, NyukinKbnEnums.FullySettled);
            AssertSyunoSeikyuStatus(tenant, raiinNo2, NyukinKbnEnums.PartiallySettled);
            AssertSyunoSeikyuStatus(tenant, raiinNo3, NyukinKbnEnums.PartiallySettled);
            AssertSyunoSeikyuStatus(tenant, raiinNo4, NyukinKbnEnums.PartiallySettled);
            AssertSyunoSeikyuStatus(tenant, raiinNo5, NyukinKbnEnums.PartiallySettled);

            //入金データチェック
            AssertSyunoNyukin(tenant, raiinNo1, 0, 0, 100);
            AssertSyunoNyukin(tenant, raiinNo2, 0, 10, 50);
            AssertSyunoNyukin(tenant, raiinNo3, 0, 0, 0);
            AssertSyunoNyukin(tenant, raiinNo4, 0, 500, 500);
            AssertSyunoNyukin(tenant, raiinNo5, 0, 100, 100);

            CleanupTestData(tenant, hpId);
        }

        [Test]
        public void Handle_NyukinMenjo_CheckUpdates()
        {
            //入金免除を確認するケース

            var saveAccountDueListInteractor = new SaveAccountingInteractor(_accountingRepository!, _mockSystemConfRepository!.Object, _mockUserRepository!.Object, _mockHpInfRepository!.Object,
                                                                                _mockPatientInforRepository!.Object, _mockReceptionRepository!.Object, _mockAuditLogRepository!.Object, _mockFincodeRepository!.Object);
            var tenant = TenantProvider.GetTrackingTenantDataContext();

            Random random = new Random();
            int hpId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int userId = random.Next(999, 99999);
            long raiinNo1 = random.Next(999, 99999);
            long raiinNo2 = raiinNo1 + 1;
            long raiinNo3 = raiinNo1 + 2;
            long oyaRaiinNo = raiinNo1;
            int sinDate = ********;

            //同一来院のテストデータ
            tenant.RaiinInfs.AddRange(
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo1, oyaRaiinNo, RaiinState.AmountConfirmed),
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo2, oyaRaiinNo, RaiinState.AmountConfirmed),
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo3, oyaRaiinNo, RaiinState.Paid)
            );

            //総請求額 4000円(請求額:3500円 + 未収金:500円)
            tenant.SyunoSeikyus.AddRange(
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo1, NyukinKbnEnums.Unsettled, 0, 1000, 0, 0),
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo2, NyukinKbnEnums.Unsettled, 0, 2000, 0, 0),
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo3, NyukinKbnEnums.PartiallySettled, 0, 3000, 0, 0)
            );
            tenant.SyunoNyukin.AddRange(
                CreateSyunoNyukin(hpId, ptId, sinDate, raiinNo3, 500, 2000, PaymentMethodCdEnum.Cash)
            );
            tenant.SaveChanges();

            //リクエスト
            var inputData = new SaveAccountingInputData(
                hpId: hpId,
                ptId: ptId,
                userId: userId,
                sinDate: sinDate,
                raiinNo: oyaRaiinNo,
                sumAdjust: 4000,
                thisWari: 0,
                credit: 0,
                payType: (int)PaymentMethodCdEnum.Cash,
                comment: "test",
                isDisCharged: true,
                kaikeiTime: "093000",
                isOperator: 1,
                operatorName: "test"
            );

            var result = saveAccountDueListInteractor.Handle(inputData);
            Assert.That(result.Status, Is.EqualTo(SaveAccountingStatus.Success));

            tenant = TenantProvider.GetTrackingTenantDataContext();
            AssertRaiinInfStatus(tenant, raiinNo1, RaiinState.Paid);
            AssertRaiinInfStatus(tenant, raiinNo2, RaiinState.Paid);
            AssertRaiinInfStatus(tenant, raiinNo3, RaiinState.Paid);
            AssertSyunoSeikyuStatus(tenant, raiinNo1, NyukinKbnEnums.NoBilling);
            AssertSyunoSeikyuStatus(tenant, raiinNo2, NyukinKbnEnums.NoBilling);
            AssertSyunoSeikyuStatus(tenant, raiinNo3, NyukinKbnEnums.PartiallySettled);

            //入金データチェック
            AssertSyunoNyukin(tenant, raiinNo1, 0, 0, 0);
            AssertSyunoNyukin(tenant, raiinNo2, 0, 0, 0);
            AssertSyunoNyukin(tenant, raiinNo3, 0, 2000, 500);

            CleanupTestData(tenant, hpId);
        }

        [Test]
        public void Handle__OnlineCheckUpdates()
        {

            Random random = new Random();
            int hpId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int userId = random.Next(999, 99999);
            long raiinNo1 = random.Next(999, 99999);
            long oyaRaiinNo = raiinNo1;
            int sinDate = ********;
            int reserveDetailId = 2;
            RequestReservePaymentData data = new()
            {
                RequestReservePayment = new RequestReservePayment(reserveDetailId, "", "")
            };

            // Arrange
            var mockFincodeRepository = new Mock<IFincodeRepository>();

            var expectedResponse = new DenkaruGqlResponse<RequestReservePaymentData>
            { Data = data, Errors = new List<GraphqlError>() };

            // 非同期処理の戻り値の設定が未完了
            mockFincodeRepository.Setup(rep=>rep.RequestReservePayment(hpId, reserveDetailId, (int)PaymentTypeEnums.Card, 1000, userId))
            .ReturnsAsync(expectedResponse);

            //オンライン決済を確認するケース
            var saveAccountingInteractor = new SaveAccountingInteractor(_accountingRepository!, _mockSystemConfRepository!.Object, _mockUserRepository!.Object, _mockHpInfRepository!.Object,
                                                                                _mockPatientInforRepository!.Object, _mockReceptionRepository!.Object, _mockAuditLogRepository!.Object, mockFincodeRepository!.Object);
            var tenant = TenantProvider.GetTrackingTenantDataContext();

            //来院のテストデータ
            tenant.RaiinInfs.AddRange(
                CreateRaiinInfReserve(hpId, ptId, sinDate, raiinNo1, oyaRaiinNo, RaiinState.ConsultationCompleted, reserveDetailId)
            );

            //総請求額 1000円
            tenant.SyunoSeikyus.AddRange(
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo1, NyukinKbnEnums.Unsettled, 0, 1000, 0, 0)
            );

            tenant.SaveChanges();

            //リクエスト
            var inputData = new SaveAccountingInputData(
                hpId: hpId,
                ptId: ptId,
                userId: userId,
                sinDate: sinDate,
                raiinNo: oyaRaiinNo,
                sumAdjust: 1000,
                thisWari: 0,
                credit: 1000,
                payType: (int)PaymentMethodCdEnum.Online,
                comment: "test",
                isDisCharged: false,
                kaikeiTime: "093000",
                isOperator: 1,
                operatorName: "test"
            );

            var result = saveAccountingInteractor.Handle(inputData);
            Assert.That(result.Status, Is.EqualTo(SaveAccountingStatus.Success));

            tenant = TenantProvider.GetTrackingTenantDataContext();
            AssertRaiinInfStatus(tenant, raiinNo1, RaiinState.Paid);
            AssertSyunoSeikyuStatus(tenant, raiinNo1, NyukinKbnEnums.FullySettled);

            //入金データチェック
            AssertSyunoNyukin(tenant, raiinNo1, 0, 1000, 0);

            CleanupTestData(tenant, hpId);
        }

        [Test]
        public void Handle__Online_ElectronicMoney_Updates()
        {

            Random random = new Random();
            int hpId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int userId = random.Next(999, 99999);
            long raiinNo1 = random.Next(999, 99999);
            long oyaRaiinNo = raiinNo1;
            int sinDate = ********;
            int reserveDetailId = 2;
            RequestReservePaymentData data = new()
            {
                RequestReservePayment = new RequestReservePayment(reserveDetailId, "", "")
            };

            // Arrange
            var mockFincodeRepository = new Mock<IFincodeRepository>();

            var expectedResponse = new DenkaruGqlResponse<RequestReservePaymentData>
            { Data = data, Errors = new List<GraphqlError>() };

            // 非同期処理の戻り値の設定が未完了
            mockFincodeRepository.Setup(rep=>rep.RequestReservePayment(hpId, reserveDetailId, (int)PaymentTypeEnums.Cash, 1000, userId))
            .ReturnsAsync(expectedResponse);

            //オンライン決済を確認するケース
            var saveAccountingInteractor = new SaveAccountingInteractor(_accountingRepository!, _mockSystemConfRepository!.Object, _mockUserRepository!.Object, _mockHpInfRepository!.Object,
                                                                                _mockPatientInforRepository!.Object, _mockReceptionRepository!.Object, _mockAuditLogRepository!.Object, mockFincodeRepository!.Object);
            var tenant = TenantProvider.GetTrackingTenantDataContext();

            //来院のテストデータ
            tenant.RaiinInfs.AddRange(
                CreateRaiinInfReserve(hpId, ptId, sinDate, raiinNo1, oyaRaiinNo, RaiinState.ConsultationCompleted, reserveDetailId)
            );

            //総請求額 1000円
            tenant.SyunoSeikyus.AddRange(
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo1, NyukinKbnEnums.Unsettled, 0, 1000, 0, 0)
            );

            tenant.SaveChanges();

            //リクエスト
            var inputData = new SaveAccountingInputData(
                hpId: hpId,
                ptId: ptId,
                userId: userId,
                sinDate: sinDate,
                raiinNo: oyaRaiinNo,
                sumAdjust: 1000,
                thisWari: 0,
                credit: 1000,
                payType: (int)PaymentMethodCdEnum.ElectronicMoney,
                comment: "test",
                isDisCharged: false,
                kaikeiTime: "093000",
                isOperator: 1,
                operatorName: "test"
            );

            var result = saveAccountingInteractor.Handle(inputData);
            Assert.That(result.Status, Is.EqualTo(SaveAccountingStatus.Success));

            tenant = TenantProvider.GetTrackingTenantDataContext();
            AssertRaiinInfStatus(tenant, raiinNo1, RaiinState.Paid);
            AssertSyunoSeikyuStatus(tenant, raiinNo1, NyukinKbnEnums.FullySettled);

            //入金データチェック
            AssertSyunoNyukin(tenant, raiinNo1, 0, 1000, 0);

            CleanupTestData(tenant, hpId);
        }

        [Test]
        public void Handle__NyukinErr_Updates()
        {

            Random random = new Random();
            int hpId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int userId = random.Next(999, 99999);
            long raiinNo1 = random.Next(999, 99999);
            long oyaRaiinNo = raiinNo1;
            int sinDate = ********;
            int reserveDetailId = 2;
            RequestReservePaymentData data = new()
            {
                RequestReservePayment = new RequestReservePayment(reserveDetailId, "CardErr", "")
            };

            // Arrange
            var mockFincodeRepository = new Mock<IFincodeRepository>();

            var expectedResponse = new DenkaruGqlResponse<RequestReservePaymentData>
            { Data = data, Errors = new List<GraphqlError>() };

            // 非同期処理の戻り値の設定が未完了
            mockFincodeRepository.Setup(rep=>rep.RequestReservePayment(hpId, reserveDetailId, (int)PaymentTypeEnums.Card, 1000, userId))
            .ReturnsAsync(expectedResponse);

            //オンライン決済を確認するケース
            var saveAccountingInteractor = new SaveAccountingInteractor(_accountingRepository!, _mockSystemConfRepository!.Object, _mockUserRepository!.Object, _mockHpInfRepository!.Object,
                                                                                _mockPatientInforRepository!.Object, _mockReceptionRepository!.Object, _mockAuditLogRepository!.Object, mockFincodeRepository!.Object);
            var tenant = TenantProvider.GetTrackingTenantDataContext();

            //来院のテストデータ
            tenant.RaiinInfs.AddRange(
                CreateRaiinInfReserve(hpId, ptId, sinDate, raiinNo1, oyaRaiinNo, RaiinState.ConsultationCompleted, reserveDetailId)
            );

            //総請求額 1000円
            tenant.SyunoSeikyus.AddRange(
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo1, NyukinKbnEnums.Unsettled, 0, 1000, 0, 0)
            );

            tenant.SaveChanges();

            //リクエスト
            var inputData = new SaveAccountingInputData(
                hpId: hpId,
                ptId: ptId,
                userId: userId,
                sinDate: sinDate,
                raiinNo: oyaRaiinNo,
                sumAdjust: 1000,
                thisWari: 0,
                credit: 1000,
                payType: (int)PaymentMethodCdEnum.Online,
                comment: "test",
                isDisCharged: false,
                kaikeiTime: "093000",
                isOperator: 1,
                operatorName: "test"
            );

            var result = saveAccountingInteractor.Handle(inputData);
            Assert.That(result.Status, Is.EqualTo(SaveAccountingStatus.CardError));

            tenant = TenantProvider.GetTrackingTenantDataContext();
            AssertRaiinInfStatus(tenant, raiinNo1, RaiinState.Paid);
            AssertSyunoSeikyuStatus(tenant, raiinNo1, NyukinKbnEnums.PartiallySettled);

            //入金データチェック(カードエラー)
            AssertSyunoNyukin(tenant, raiinNo1, 0, 0, 0, 1000);

            CleanupTestData(tenant, hpId);
        }

        [Test]
        public void Handle__Nodata_Return()
        {

            Random random = new Random();
            int hpId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int userId = random.Next(999, 99999);
            long raiinNo1 = random.Next(999, 99999);
            long oyaRaiinNo = raiinNo1;
            int sinDate = ********;

            // Arrange
            var mockFincodeRepository = new Mock<IFincodeRepository>();

            var saveAccountingInteractor = new SaveAccountingInteractor(_accountingRepository!, _mockSystemConfRepository!.Object, _mockUserRepository!.Object, _mockHpInfRepository!.Object,
                                                                                _mockPatientInforRepository!.Object, _mockReceptionRepository!.Object, _mockAuditLogRepository!.Object, mockFincodeRepository!.Object);

            //リクエスト
            var inputData = new SaveAccountingInputData(
                hpId: hpId,
                ptId: ptId,
                userId: userId,
                sinDate: sinDate,
                raiinNo: oyaRaiinNo,
                sumAdjust: 860,
                thisWari: 0,
                credit: 860,
                payType: (int)PaymentMethodCdEnum.Cash,
                comment: "test",
                isDisCharged: true,
                kaikeiTime: "093000",
                isOperator: 1,
                operatorName: "test"
            );

            var result = saveAccountingInteractor.Handle(inputData);
            Assert.That(result.Status, Is.EqualTo(SaveAccountingStatus.InputDataNull));

        }

        [Test]
        public void Handle_ValidatePaymentMethod_FCO_UnMatchSeikyuAndNyukin()
        {
            // 支払方法：FCO連携時のバリデーションチェック 請求額≠入金額
            var saveAccountingInteractor = new SaveAccountingInteractor(_accountingRepository!, _mockSystemConfRepository!.Object, _mockUserRepository!.Object, _mockHpInfRepository!.Object,
                                                                                _mockPatientInforRepository!.Object, _mockReceptionRepository!.Object, _mockAuditLogRepository!.Object, _mockFincodeRepository!.Object);
            var tenant = TenantProvider.GetTrackingTenantDataContext();

            Random random = new Random();
            int hpId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int userId = random.Next(999, 99999);
            long raiinNo1 = random.Next(999, 99999);
            long oyaRaiinNo = raiinNo1;
            int sinDate = ********;

            //同一来院のテストデータ
            tenant.RaiinInfs.AddRange(
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo1, oyaRaiinNo, RaiinState.ConsultationCompleted)
            );

            //請求額:1000円
            tenant.SyunoSeikyus.AddRange(
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo1, NyukinKbnEnums.Unsettled, 100, 1100, 0, 0)
            );
            
            tenant.SaveChanges();

            //リクエスト
            var inputData = new SaveAccountingInputData(
                hpId: hpId,
                ptId: ptId,
                userId: userId,
                sinDate: sinDate,
                raiinNo: oyaRaiinNo,
                sumAdjust: 0,
                thisWari: 300,
                credit: 800,
                payType: (int)PaymentMethodCdEnum.FcoPayment,
                comment: "FCO",
                isDisCharged: false,
                kaikeiTime: "103000",
                isOperator: 1,
                operatorName: "TestOperator001"
            );

            var result = saveAccountingInteractor.Handle(inputData);
            Assert.That(result.Status, Is.EqualTo(SaveAccountingStatus.UnMatchSeikyuAndNyukinInFCO));

            //来院データチェック
            tenant = TenantProvider.GetTrackingTenantDataContext();
            AssertRaiinInfStatus(tenant, raiinNo1, RaiinState.ConsultationCompleted);
            AssertSyunoSeikyuStatus(tenant, raiinNo1, NyukinKbnEnums.Unsettled);

            CleanupTestData(tenant, hpId);
        }

        [Test]
        public void Handle_ValidatePaymentMethod_FCO_UnCollectedBalance()
        {
            // 支払方法：FCO連携時のバリデーションチェック 未収金あり
            var saveAccountingInteractor = new SaveAccountingInteractor(_accountingRepository!, _mockSystemConfRepository!.Object, _mockUserRepository!.Object, _mockHpInfRepository!.Object,
                                                                                _mockPatientInforRepository!.Object, _mockReceptionRepository!.Object, _mockAuditLogRepository!.Object, _mockFincodeRepository!.Object);
            var tenant = TenantProvider.GetTrackingTenantDataContext();

            Random random = new Random();
            int hpId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int userId = random.Next(999, 99999);
            long raiinNo1 = random.Next(999, 99999);
            long raiinNo2 = raiinNo1 + 1;
            long oyaRaiinNo = raiinNo1;
            int sinDate = ********;

            // 同一来院のテストデータ
            tenant.RaiinInfs.AddRange(
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo1, oyaRaiinNo, RaiinState.ConsultationCompleted),
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo2, oyaRaiinNo, RaiinState.ConsultationCompleted)
            );

            // 総請求額 3600円(請求額:2400円 + 未収金:1200円)
            tenant.SyunoSeikyus.AddRange(
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo1, NyukinKbnEnums.PartiallySettled, 100, 1100, 0, 0),
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo2, NyukinKbnEnums.Unsettled, 200, 2200, 0, 0)
            );

            tenant.SyunoNyukin.AddRange(
                CreateSyunoNyukin(hpId, ptId, sinDate, raiinNo1, 100, 500, PaymentMethodCdEnum.Cash)
            );
            
            tenant.SaveChanges();

            // リクエスト
            var inputData = new SaveAccountingInputData(
                hpId: hpId,
                ptId: ptId,
                userId: userId,
                sinDate: sinDate,
                raiinNo: oyaRaiinNo,
                sumAdjust: 0,
                thisWari: 200,
                credit: 800,
                payType: (int)PaymentMethodCdEnum.FcoPayment,
                comment: "FCO",
                isDisCharged: false,
                kaikeiTime: "103000",
                isOperator: 1,
                operatorName: "TestOperator001"
            );

            var result = saveAccountingInteractor.Handle(inputData);
            Assert.That(result.Status, Is.EqualTo(SaveAccountingStatus.UnCollectedBalanceInFCO));

            // 来院データチェック
            tenant = TenantProvider.GetTrackingTenantDataContext();
            AssertRaiinInfStatus(tenant, raiinNo1, RaiinState.ConsultationCompleted);
            AssertRaiinInfStatus(tenant, raiinNo2, RaiinState.ConsultationCompleted);
            AssertSyunoSeikyuStatus(tenant, raiinNo1, NyukinKbnEnums.PartiallySettled);
            AssertSyunoSeikyuStatus(tenant, raiinNo2, NyukinKbnEnums.Unsettled);

            // 入金データチェック
            AssertSyunoNyukin(tenant, raiinNo1, 0, 500, 100);

            CleanupTestData(tenant, hpId);
        }

        [Test]
        public void Handle_ValidatePaymentMethod_FCO_InvalidNyukinGaku()
        {
            // 支払方法：FCO連携時のバリデーションチェック 入金額:0
            var saveAccountingInteractor = new SaveAccountingInteractor(_accountingRepository!, _mockSystemConfRepository!.Object, _mockUserRepository!.Object, _mockHpInfRepository!.Object,
                                                                                _mockPatientInforRepository!.Object, _mockReceptionRepository!.Object, _mockAuditLogRepository!.Object, _mockFincodeRepository!.Object);
            var tenant = TenantProvider.GetTrackingTenantDataContext();

            Random random = new Random();
            int hpId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int userId = random.Next(999, 99999);
            long raiinNo1 = random.Next(999, 99999);
            long oyaRaiinNo = raiinNo1;
            int sinDate = ********;

            // 同一来院のテストデータ
            tenant.RaiinInfs.AddRange(
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo1, oyaRaiinNo, RaiinState.ConsultationCompleted)

            );

            // 請求額:1200円
            tenant.SyunoSeikyus.AddRange(
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo1, NyukinKbnEnums.Unsettled, 100, 1100, 0, 0)
            );

            tenant.SaveChanges();

            // リクエスト
            var inputData = new SaveAccountingInputData(
                hpId: hpId,
                ptId: ptId,
                userId: userId,
                sinDate: sinDate,
                raiinNo: oyaRaiinNo,
                sumAdjust: 0,
                thisWari: 0,
                credit: 0,
                payType: (int)PaymentMethodCdEnum.FcoPayment,
                comment: "FCO",
                isDisCharged: false,
                kaikeiTime: "103000",
                isOperator: 1,
                operatorName: "TestOperator001"
            );

            var result = saveAccountingInteractor.Handle(inputData);
            Assert.That(result.Status, Is.EqualTo(SaveAccountingStatus.InvalidNyukinGaku));

            // 来院データチェック
            tenant = TenantProvider.GetTrackingTenantDataContext();
            AssertRaiinInfStatus(tenant, raiinNo1, RaiinState.ConsultationCompleted);
            AssertSyunoSeikyuStatus(tenant, raiinNo1, NyukinKbnEnums.Unsettled);

            CleanupTestData(tenant, hpId);
        }

        [Test]
        public void Handle_ValidatePaymentMethod_FCO_Success()
        {
            var saveAccountingInteractor = new SaveAccountingInteractor(_accountingRepository!, _mockSystemConfRepository!.Object, _mockUserRepository!.Object, _mockHpInfRepository!.Object,
                                                                                _mockPatientInforRepository!.Object, _mockReceptionRepository!.Object, _mockAuditLogRepository!.Object, _mockFincodeRepository!.Object);
            var tenant = TenantProvider.GetTrackingTenantDataContext();

            Random random = new Random();
            int hpId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int userId = random.Next(999, 99999);
            long raiinNo1 = random.Next(999, 99999);
            long raiinNo2 = raiinNo1 + 1;
            long raiinNo3 = raiinNo2 + 1;
            long oyaRaiinNo = raiinNo1;
            int sinDate = ********;

            // 同一来院のテストデータ
            tenant.RaiinInfs.AddRange(
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo1, oyaRaiinNo, RaiinState.Paid),
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo2, oyaRaiinNo, RaiinState.Paid),
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo3, oyaRaiinNo, RaiinState.ConsultationCompleted)
            );

            // 請求額:1200円
            tenant.SyunoSeikyus.AddRange(
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo1, NyukinKbnEnums.FullySettled, 300, 3300, 0, 0),
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo2, NyukinKbnEnums.FullySettled, 200, 2200, 0, 0),
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo3, NyukinKbnEnums.Unsettled, 100, 1100, 0, 0)
            );

            tenant.SyunoNyukin.AddRange(
                CreateSyunoNyukin(hpId, ptId, sinDate, raiinNo1, 300, 3000, PaymentMethodCdEnum.Cash),
                CreateSyunoNyukin(hpId, ptId, sinDate, raiinNo2, 200, 2000, PaymentMethodCdEnum.Credit)
            );

            tenant.SaveChanges();

            // リクエスト
            var inputData = new SaveAccountingInputData(
                hpId: hpId,
                ptId: ptId,
                userId: userId,
                sinDate: sinDate,
                raiinNo: oyaRaiinNo,
                sumAdjust: 0,
                thisWari: 100,
                credit: 900,
                payType: (int)PaymentMethodCdEnum.FcoPayment,
                comment: "FCO",
                isDisCharged: false,
                kaikeiTime: "103000",
                isOperator: 1,
                operatorName: "TestOperator001"
            );

            var result = saveAccountingInteractor.Handle(inputData);
            Assert.That(result.Status, Is.EqualTo(SaveAccountingStatus.Success));

            // 来院データチェック
            tenant = TenantProvider.GetTrackingTenantDataContext();
            AssertRaiinInfStatus(tenant, raiinNo1, RaiinState.Paid);
            AssertRaiinInfStatus(tenant, raiinNo2, RaiinState.Paid);
            AssertRaiinInfStatus(tenant, raiinNo3, RaiinState.FcoWaiting);
            AssertSyunoSeikyuStatus(tenant, raiinNo1, NyukinKbnEnums.FullySettled);
            AssertSyunoSeikyuStatus(tenant, raiinNo2, NyukinKbnEnums.FullySettled);
            AssertSyunoSeikyuStatus(tenant, raiinNo3, NyukinKbnEnums.Unsettled);

            // 入金データチェック
            AssertSyunoNyukin(tenant, raiinNo1, 0, 3000, 300);
            AssertSyunoNyukin(tenant, raiinNo2, 0, 2000, 200);
            AssertSyunoNyukin(tenant, raiinNo3, 0, 0, 100);

            CleanupTestData(tenant, hpId);
        }
        [Test]
        public void Handle_ValidatePaymentMethod_FCO_UnCollectedBalanceMainus()
        {
            // 支払方法：FCO連携時のバリデーションチェック 未収金あり(マイナス)
            var saveAccountingInteractor = new SaveAccountingInteractor(_accountingRepository!, _mockSystemConfRepository!.Object, _mockUserRepository!.Object, _mockHpInfRepository!.Object,
                                                                                _mockPatientInforRepository!.Object, _mockReceptionRepository!.Object, _mockAuditLogRepository!.Object, _mockFincodeRepository!.Object);
            var tenant = TenantProvider.GetTrackingTenantDataContext();

            Random random = new Random();
            int hpId = random.Next(999, 99999);
            long ptId = random.Next(999, 99999);
            int userId = random.Next(999, 99999);
            long raiinNo1 = random.Next(999, 99999);
            long raiinNo2 = raiinNo1 + 1;
            long oyaRaiinNo = raiinNo1;
            int sinDate = ********;

            // 同一来院のテストデータ
            tenant.RaiinInfs.AddRange(
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo1, oyaRaiinNo, RaiinState.ConsultationCompleted),
                CreateRaiinInf(hpId, ptId, sinDate, raiinNo2, oyaRaiinNo, RaiinState.ConsultationCompleted)
            );

            // 総請求額 3600円(請求額:2400円 + 未収金:-300円)
            tenant.SyunoSeikyus.AddRange(
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo1, NyukinKbnEnums.PartiallySettled, 500, 100, 0, 0),
                CreateSyunoSeikyu(hpId, ptId, sinDate, raiinNo2, NyukinKbnEnums.Unsettled, 200, 2200, 0, 0)
            );

            tenant.SyunoNyukin.AddRange(
                CreateSyunoNyukin(hpId, ptId, sinDate, raiinNo1, 0, 300, PaymentMethodCdEnum.Cash)
            );
            
            tenant.SaveChanges();

            // リクエスト
            var inputData = new SaveAccountingInputData(
                hpId: hpId,
                ptId: ptId,
                userId: userId,
                sinDate: sinDate,
                raiinNo: oyaRaiinNo,
                sumAdjust: 0,
                thisWari: 100,
                credit: 2000,
                payType: (int)PaymentMethodCdEnum.FcoPayment,
                comment: "FCO",
                isDisCharged: false,
                kaikeiTime: "103000",
                isOperator: 1,
                operatorName: "TestOperator001"
            );

            var result = saveAccountingInteractor.Handle(inputData);
            Assert.That(result.Status, Is.EqualTo(SaveAccountingStatus.UnCollectedBalanceInFCO));

            // 来院データチェック
            tenant = TenantProvider.GetTrackingTenantDataContext();
            AssertRaiinInfStatus(tenant, raiinNo1, RaiinState.ConsultationCompleted);
            AssertRaiinInfStatus(tenant, raiinNo2, RaiinState.ConsultationCompleted);
            AssertSyunoSeikyuStatus(tenant, raiinNo1, NyukinKbnEnums.PartiallySettled);
            AssertSyunoSeikyuStatus(tenant, raiinNo2, NyukinKbnEnums.Unsettled);

            CleanupTestData(tenant, hpId);
        }
        private RaiinInf CreateRaiinInf(int hpId, long ptId, int sinDate, long raiinNo, long oyaRaiinNo, int status)
        {
            return new RaiinInf
            {
                HpId = hpId,
                PtId = ptId,
                SinDate = sinDate,
                RaiinNo = raiinNo,
                OyaRaiinNo = oyaRaiinNo,
                Status = status,
                KaikeiTime = "104500",
                KaikeiId = 0,
                HokenPid = 0,
                MonshinStatus = 0,
            };
        }
        private RaiinInf CreateRaiinInfReserve(int hpId, long ptId, int sinDate, long raiinNo, long oyaRaiinNo, int status, int reserveDetailId)
        {
            return new RaiinInf
            {
                HpId = hpId,
                PtId = ptId,
                SinDate = sinDate,
                RaiinNo = raiinNo,
                OyaRaiinNo = oyaRaiinNo,
                Status = status,
                KaikeiTime = "104500",
                KaikeiId = 0,
                HokenPid = 0,
                MonshinStatus = 0,
                ReserveDetailId = reserveDetailId
            };
        }

        private SyunoSeikyu CreateSyunoSeikyu(int hpId, long ptId, int sinDate, long raiinNo, NyukinKbnEnums nyukinKbn, int adjustFutan, int seikyuGaku, int newAdjustFutan, int newSeikyuGaku)
        {
            return new SyunoSeikyu
            {
                HpId = hpId,
                PtId = ptId,
                SinDate = sinDate,
                RaiinNo = raiinNo,
                NyukinKbn = (int)nyukinKbn,
                AdjustFutan = adjustFutan,
                SeikyuGaku = seikyuGaku,
                NewAdjustFutan = newAdjustFutan,
                NewSeikyuGaku = newSeikyuGaku,
            };
        }

        private SyunoNyukin CreateSyunoNyukin(int hpId, long ptId, int sinDate, long raiinNo, int adjustFutan, int nyukinGaku, PaymentMethodCdEnum paymentMethodCd)
        {
            return new SyunoNyukin
            {
                HpId = hpId,
                PtId = ptId,
                SinDate = sinDate,
                RaiinNo = raiinNo,
                AdjustFutan = adjustFutan,
                NyukinGaku = nyukinGaku,
                PaymentMethodCd = (int)paymentMethodCd,
            };
        }

        private void AssertRaiinInfStatus(TenantDataContext tenant, long raiinNo, int expectedStates)
        {
            var result = tenant.RaiinInfs.Where(item => item.RaiinNo == raiinNo).FirstOrDefault();
            Assert.That(result!.Status, Is.EqualTo(expectedStates));
        }

        private void AssertSyunoSeikyuStatus(TenantDataContext tenant, long raiinNo, NyukinKbnEnums expectedStates)
        {
            var result = tenant.SyunoSeikyus.Where(item => item.RaiinNo == raiinNo).FirstOrDefault();
            Assert.That(result!.NyukinKbn, Is.EqualTo((int)expectedStates));
        }

        private void AssertSyunoNyukin(TenantDataContext tenant, long raiinNo, int index, int expectedNyukinGaku, int expectedAdjustFutan, int expectedNyukinGakuYotei = 0)
        {
            var nyukinList = tenant.SyunoNyukin.Where(item => item.RaiinNo == raiinNo).ToList();
            Assert.That(nyukinList[index].NyukinGaku, Is.EqualTo(expectedNyukinGaku));
            Assert.That(nyukinList[index].AdjustFutan, Is.EqualTo(expectedAdjustFutan));
            Assert.That(nyukinList[index].ExpectedNyukinGaku, Is.EqualTo(expectedNyukinGakuYotei));
        }


        private void CleanupTestData(TenantDataContext tenant, int hpId)
        {
            tenant.RaiinInfs.RemoveRange(tenant.RaiinInfs.Where(x => x.HpId == hpId).ToList());
            tenant.SyunoSeikyus.RemoveRange(tenant.SyunoSeikyus.Where(x => x.HpId == hpId).ToList());
            tenant.SyunoNyukin.RemoveRange(tenant.SyunoNyukin.Where(x => x.HpId == hpId).ToList());
            tenant.SaveChanges();
        }
    }

}

