{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"ErrorCodeGenerator/1.0.0": {"dependencies": {"Newtonsoft.Json": "13.0.2"}, "runtime": {"ErrorCodeGenerator.dll": {}}}, "Newtonsoft.Json/13.0.2": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.2.27524"}}}}}, "libraries": {"ErrorCodeGenerator/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Newtonsoft.Json/13.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-R2pZ3B0UjeyHShm9vG+Tu0EBb2lC8b0dFzV9gVn50ofHXh9Smjk6kTn7A/FdAsC8B5cKib1OnGYOXxRBz5XQDg==", "path": "newtonsoft.json/13.0.2", "hashPath": "newtonsoft.json.13.0.2.nupkg.sha512"}}}