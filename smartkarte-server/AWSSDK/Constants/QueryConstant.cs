﻿namespace AWSSDK.Constants
{
    public static class QueryConstant
    {
        public static string CreateAuditLog = @"CREATE TABLE public.""auditlogs"" (
    ""logid"" int8 NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    ""tenantid"" int4 NOT NULL,
    ""domain"" text NOT NULL,
    ""threadid"" text NOT NULL,
    ""logtype"" text NOT NULL,
    ""hpid"" int4 NOT NULL,
    ""userid"" int4 NOT NULL,
    ""loginkey"" text NOT NULL,
    ""departmentid"" int4 NOT NULL,
    ""logdate"" timestamptz NOT NULL,
    ""eventcd"" text NULL,
    ""ptid"" int8 NOT NULL,
    ""sinday"" int4 NOT NULL,
    ""raiinno"" int8 NOT NULL,
    ""path"" text NULL,
    ""requestinfo"" text NOT NULL,
    ""clientip"" text NOT NULL,
    ""desciption"" text NOT NULL,
    CONSTRAINT Auditlog_pk PRIMARY KEY (""logid"", ""tenantid"")
) PARTITION BY LIST(""tenantid"");

CREATE INDEX idx_clientip on ONLY public.""auditlogs"" USING gin (to_tsvector('english'::regconfig, (""ClientIP"")::text));
CREATE INDEX idx_desciption on ONLY public.""auditlogs"" USING gin (to_tsvector('english'::regconfig, (""Desciption"")::text));
CREATE INDEX idx_domain ON ONLY public.""auditlogs"" USING gin (to_tsvector('english'::regconfig, (""Domain"")::text));
CREATE INDEX idx_eventcd on ONLY public.""auditlogs"" USING gin (to_tsvector('english'::regconfig, (""EventCd"")::text));
CREATE INDEX idx_loginkey ON ONLY public.""auditlogs"" USING gin (to_tsvector('english'::regconfig, (""LoginKey"")::text));
CREATE INDEX idx_path on ONLY public.""auditlogs"" USING gin (to_tsvector('english'::regconfig, (""Path"")::text));
CREATE INDEX idx_requestinfo on ONLY public.""auditlogs"" USING gin (to_tsvector('english'::regconfig, ""RequestInfo""));
CREATE INDEX idx_threadid on ONLY public.""auditlogs"" USING gin (to_tsvector('english'::regconfig, (""ThreadId"")::text));
CREATE INDEX idx_AuditLogslogtype on ONLY public.""auditlogs"" USING gin (to_tsvector('english'::regconfig, (""LogType"")::text));
CREATE INDEX ""idx_AuditLogs_TenantId"" ON ONLY public.""auditlogs"" USING btree (""tenantid"");
CREATE INDEX ""idx_AuditLogs_LogDate"" ON ONLY public.""auditlogs"" USING btree (""logdate"");
CREATE INDEX ""idx_AuditLogs_LogId"" ON ONLY public.""auditlogs"" USING btree (""logid"");
CREATE INDEX ""idx_AuditLogs_PtId"" ON ONLY public.""auditlogs"" USING btree (""raiinno"");";

        public static string SqlUserPermission = @"
            INSERT INTO public.""user_permission""
            (""hp_id"", ""user_id"", ""function_cd"", ""permission"", ""create_date"", ""create_id"", ""create_machine"", ""update_date"", ""update_id"", ""update_machine"")
            VALUES
            (@hpId, @adminId, '99201001', 0, '2021-05-30 01:23:14.067', 0, NULL, '2021-05-30 01:23:14.067', 0, NULL),
            (@hpId, @adminId, '99201002', 0, '2021-05-30 01:23:14.067', 0, NULL, '2021-05-30 01:23:14.067', 0, NULL),
            (@hpId, @adminId, '99201004', 0, '2021-05-30 01:23:14.068', 0, NULL, '2021-05-30 01:23:14.068', 0, NULL),
            (@hpId, @adminId, '99201005', 0, '2021-05-30 01:23:14.068', 0, NULL, '2021-05-30 01:23:14.068', 0, NULL),
            (@hpId, @adminId, '99201006', 0, '2021-05-30 01:23:14.068', 0, NULL, '2021-05-30 01:23:14.068', 0, NULL),
            (@hpId, @adminId, '99201007', 0, '2021-05-30 01:23:14.069', 0, NULL, '2021-05-30 01:23:14.069', 0, NULL),
            (@hpId, @adminId, '99201008', 0, '2021-05-30 01:23:14.069', 0, NULL, '2021-05-30 01:23:14.069', 0, NULL),
            (@hpId, @adminId, '99201009', 0, '2021-05-30 01:23:14.069', 0, NULL, '2021-05-30 01:23:14.069', 0, NULL),
            (@hpId, @adminId, '99201010', 0, '2021-05-30 01:23:14.070', 0, NULL, '2021-05-30 01:23:14.070', 0, NULL),
            (@hpId, @adminId, '99201011', 0, '2021-05-30 01:23:14.070', 0, NULL, '2021-05-30 01:23:14.070', 0, NULL),
            (@hpId, @adminId, '99201012', 0, '2021-05-30 01:23:14.071', 0, NULL, '2021-05-30 01:23:14.071', 0, NULL),
            (@hpId, @adminId, '99202000', 0, '2021-05-30 01:23:14.071', 0, NULL, '2021-05-30 01:23:14.071', 0, NULL),
            (@hpId, @adminId, '99203000', 0, '2021-05-30 01:23:14.072', 0, NULL, '2021-05-30 01:23:14.072', 0, NULL),
            (@hpId, @adminId, '99204000', 1, '2021-05-30 01:23:14.072', 0, NULL, '2021-05-30 01:23:14.072', 0, NULL),
            (@hpId, @adminId, '99205000', 0, '2021-05-30 01:23:14.072', 0, NULL, '2021-05-30 01:23:14.072', 0, NULL),
            (@hpId, @adminId, '99206000', 0, '2021-05-30 01:23:14.073', 0, NULL, '2021-05-30 01:23:14.073', 0, NULL),
            (@hpId, @adminId, '97101000', 0, '2021-05-30 01:23:14.075', 0, NULL, '2021-05-30 01:23:14.075', 0, NULL),
            (@hpId, @adminId, '97102000', 0, '2021-05-30 01:23:14.076', 0, NULL, '2021-05-30 01:23:14.076', 0, NULL),
            (@hpId, @adminId, '97110000', 0, '2021-05-30 01:23:14.076', 0, NULL, '2021-05-30 01:23:14.076', 0, NULL),
            (@hpId, @adminId, '97201000', 0, '2021-05-30 01:23:14.077', 0, NULL, '2021-05-30 01:23:14.077', 0, NULL),
            (@hpId, @adminId, '97202000', 0, '2021-05-30 01:23:14.078', 0, NULL, '2021-05-30 01:23:14.078', 0, NULL),
            (@hpId, @adminId, '97203000', 0, '2021-05-30 01:23:14.079', 0, NULL, '2021-05-30 01:23:14.079', 0, NULL),
            (@hpId, @adminId, '97210000', 0, '2021-05-30 01:23:14.080', 0, NULL, '2021-05-30 01:23:14.080', 0, NULL),
            (@hpId, @adminId, '97211000', 0, '2021-05-30 01:23:14.081', 0, NULL, '2021-05-30 01:23:14.081', 0, NULL),
            (@hpId, @adminId, '97220000', 0, '2021-05-30 01:23:14.081', 0, NULL, '2021-05-30 01:23:14.081', 0, NULL),
            (@hpId, @adminId, '97900000', 0, '2021-05-30 01:23:14.082', 0, NULL, '2021-05-30 01:23:14.082', 0, NULL),
            (@hpId, @adminId, '99013000', 0, '2021-05-30 01:23:14.065', 0, NULL, '2021-05-30 01:23:14.065', 0, NULL),
            (@hpId, @adminId, '99201003', 0, '2021-05-30 01:23:14.067', 0, NULL, '2023-11-22 10:42:59.113', 1, 'CATNROSE'),
            (@hpId, @adminId, '97221000', 0, '2023-08-19 22:02:10.821', 2, '', '2023-08-19 22:02:10.864', 2, ''),
            (@hpId, @adminId, '97301000', 0, '2023-08-19 22:02:13.483', 2, '', '2023-08-19 22:02:13.483', 2, ''),
            (@hpId, @adminId, '97310000', 0, '2023-08-19 22:02:13.486', 2, '', '2023-08-19 22:02:13.486', 2, ''),
            (@hpId, @adminId, '97320000', 0, '2023-08-19 22:02:13.488', 2, '', '2023-08-19 22:02:13.488', 2, ''),
            (@hpId, @adminId, '97330000', 0, '2023-08-19 22:02:13.490', 2, '', '2023-08-19 22:02:13.490', 2, ''),
            (@hpId, @adminId, '97340000', 0, '2023-08-19 22:02:13.492', 2, '', '2023-08-19 22:02:13.492', 2, ''),
            (@hpId, @adminId, '97341000', 0, '2023-08-19 22:02:13.494', 2, '', '2023-08-19 22:02:13.494', 2, ''),
            (@hpId, @adminId, '97350000', 0, '2023-08-19 22:02:13.496', 2, '', '2023-08-19 22:02:13.496', 2, ''),
            (@hpId, @adminId, '97360000', 0, '2023-08-19 22:02:13.498', 2, '', '2023-08-19 22:02:13.498', 2, ''),
            (@hpId, @adminId, '97361000', 0, '2023-08-19 22:02:13.501', 2, '', '2023-08-19 22:02:13.501', 2, ''),
            (@hpId, @adminId, '97370000', 0, '2023-08-19 22:02:13.503', 2, '', '2023-08-19 22:02:13.503', 2, ''),
            (@hpId, @adminId, '97380000', 0, '2023-08-19 22:02:13.507', 2, '', '2023-08-19 22:02:13.507', 2, ''),
            (@hpId, @adminId, '97390000', 0, '2023-08-19 22:02:13.509', 2, '', '2023-08-19 22:02:13.509', 2, ''),
            (@hpId, @adminId, '99201000', 0, '2023-08-19 22:02:13.511', 2, '', '2023-08-19 22:02:13.511', 2, ''),
            (@hpId, @adminId, '99999000', 0, '2023-08-19 22:02:13.513', 2, '', '2023-08-19 22:02:13.513', 2, ''),
            (@hpId, @adminId, '02000000', 0, '2021-05-30 01:23:14.065', 0, NULL, '2023-11-22 10:42:59.112', 1, 'CATNROSE'),
            (@hpId, @adminId, '99005000', 0, '2021-05-30 01:23:14.084', 0, NULL, '2023-11-22 10:42:59.113', 1, 'CATNROSE'),
            (@hpId, @adminId, '99201013', 0, '2021-05-30 01:23:14.071', 0, NULL, '2023-11-22 10:42:59.113', 1, 'CATNROSE'),
            (@hpId, @adminId, '02001000', 0, '2023-11-22 10:42:59.114', 1, 'CATNROSE', '2023-11-22 10:42:59.114', 1, 'CATNROSE');";
        public static string SqlUser = "INSERT INTO public.\"user_mst\" (\"hp_id\",\r\n\"user_id\",\r\n\"job_cd\",\r\n\"manager_kbn\",\r\n\"ka_id\",\r\n\"kana_name\",\r\n\"name\",\r\n\"sname\",\r\n\"login_id\",\r\n\"mayaku_license_no\",\r\n\"start_date\",\r\n\"end_date\",\r\n\"sort_no\",\r\n\"is_deleted\",\r\n\"create_date\",\r\n\"create_id\",\r\n\"create_machine\",\r\n\"update_date\",\r\n\"update_id\",\r\n\"update_machine\",\r\n\"renkei_cd1\",\r\n\"dr_name\",\r\n\"login_type\",\r\n\"hpki_sn\",\r\n\"hpki_issuer_dn\",\r\n\"hash_password\",\r\n\"salt\")\r\nVALUES(@hpId, @adminId, 1, 7, 1, '', '', '', @adminId, '', 0, 99999999, 0, 0, '2004-01-10 00:00:00.000', 0, NULL, '2023-10-10 00:11:18.558', 0, '', '', '', 0, NULL, NULL, @hashPassword, @salt);";

        public static string SaveSystemChangeLog = "INSERT INTO public.\"system_change_log\" (\"file_name\",\"is_pg\",\"is_db\",\"is_master\",\"is_note\",\"status\",\"err_message\",\"create_date\",\"update_date\",\"is_run\",\"is_drug_photo\")\r\n\tVALUES (@FileName, @IsPG, @IsDB, @IsMaster, @IsNote, @Status, @ErrMessage, @CreateDate, @UpdateDate, @IsRun, @IsDrugPhoto);\r\n";

        public static string RenameTableNames = @"
            CREATE OR REPLACE FUNCTION public.rename_table_names()
             RETURNS void
             LANGUAGE plpgsql
            AS $function$
            DECLARE 
                table_name_var text;  
                exec_query text; 
            BEGIN
                FOR table_name_var IN (SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' and table_name not like '%partition%') 
                LOOP
                    exec_query := 'ALTER TABLE ""' || table_name_var || '"" RENAME TO ""' || lower(table_name_var) || '"";';
                    EXECUTE exec_query;
                END LOOP;
            END $function$
            ;

            SELECT rename_table_names();
        ";

        public static string RenameFieldNames =
            "CREATE OR REPLACE FUNCTION public.rename_field_names()" +
            " RETURNS void" +
            " LANGUAGE plpgsql" +
            " AS $function$" +
            " DECLARE " +
            "     table_name_var text;  " +
            "     column_name_var text;" +
            "     exec_query text;   " +
            " BEGIN" +
            "     FOR table_name_var IN (SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' and table_name not like '%partition%')" +
            "     LOOP" +
            "         FOR column_name_var IN (SELECT column_name FROM information_schema.columns WHERE table_schema = 'public' AND table_name = table_name_var)" +
            "         LOOP " +
            "             exec_query := " +
            "                 'ALTER TABLE \"' || lower(table_name_var) || " +
            "                 '\" RENAME COLUMN \"' || column_name_var || " +
            "                 '\" TO \"' || lower(column_name_var) || '\";';" +
            "             EXECUTE exec_query;" +
            "         END LOOP;" +
            "     END LOOP;" +
            " END $function$" +
            " ;" +
            " SELECT rename_field_names();";
    }
}
