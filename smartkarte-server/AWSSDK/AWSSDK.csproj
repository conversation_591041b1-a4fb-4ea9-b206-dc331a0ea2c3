﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="AWSSDK.CloudWatch" Version="3.7.300" />
		<PackageReference Include="AWSSDK.CloudFront" Version="3.7.300" />
		<PackageReference Include="AWSSDK.Route53" Version="3.7.300" />
		<PackageReference Include="AWSSDK.RDS" Version="3.7.300.1" />
		<PackageReference Include="CsvHelper" Version="30.0.1" />
		<PackageReference Include="Npgsql" Version="7.0.7" />
		<PackageReference Include="AWSSDK.Core" Version="3.7.300.6" />
		<PackageReference Include="AWSSDK.S3" Version="3.7.301.1" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Helper\Helper.csproj" />
		<ProjectReference Include="..\UseCase\UseCase.csproj" />
	</ItemGroup>

</Project>
