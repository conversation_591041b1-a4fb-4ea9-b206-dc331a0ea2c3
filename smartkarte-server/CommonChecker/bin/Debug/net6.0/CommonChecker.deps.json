{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"CommonChecker/1.0.0": {"dependencies": {"Helper": "1.0.0", "Infrastructure": "1.0.0", "PostgreDataContext": "1.0.0"}, "runtime": {"CommonChecker.dll": {}}}, "AWSSDK.Core/3.7.12.24": {"runtime": {"lib/netcoreapp3.1/AWSSDK.Core.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.7.12.24"}}}, "AWSSDK.S3/3.7.9.42": {"dependencies": {"AWSSDK.Core": "3.7.12.24"}, "runtime": {"lib/netcoreapp3.1/AWSSDK.S3.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.7.9.42"}}}, "GraphQL.Client/6.0.0": {"dependencies": {"GraphQL.Client.Abstractions": "6.0.0", "GraphQL.Client.Abstractions.Websocket": "6.0.0", "System.Reactive": "5.0.0"}, "runtime": {"lib/netstandard2.0/GraphQL.Client.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.0.0"}}}, "GraphQL.Client.Abstractions/6.0.0": {"dependencies": {"GraphQL.Primitives": "6.0.0"}, "runtime": {"lib/netstandard2.0/GraphQL.Client.Abstractions.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.0.0"}}}, "GraphQL.Client.Abstractions.Websocket/6.0.0": {"dependencies": {"GraphQL.Client.Abstractions": "6.0.0"}, "runtime": {"lib/netstandard2.0/GraphQL.Client.Abstractions.Websocket.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.0.0"}}}, "GraphQL.Client.Serializer.Newtonsoft/6.0.0": {"dependencies": {"GraphQL.Client.Abstractions.Websocket": "6.0.0", "Newtonsoft.Json": "13.0.2"}, "runtime": {"lib/netstandard2.0/GraphQL.Client.Serializer.Newtonsoft.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.0.0"}}}, "GraphQL.Primitives/6.0.0": {"runtime": {"lib/netstandard2.0/GraphQL.Primitives.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.0.0"}}}, "Kana.NET/1.0.6": {"runtime": {"lib/net6.0/Umayadia.Kana.dll": {"assemblyVersion": "1.0.6.0", "fileVersion": "1.0.6.0"}}}, "Konscious.Security.Cryptography.Argon2/1.3.0": {"dependencies": {"Konscious.Security.Cryptography.Blake2": "1.1.0", "System.Memory": "4.5.4"}, "runtime": {"lib/net6.0/Konscious.Security.Cryptography.Argon2.dll": {"assemblyVersion": "1.3.0.0", "fileVersion": "1.3.0.0"}}}, "Konscious.Security.Cryptography.Blake2/1.1.0": {"runtime": {"lib/net6.0/Konscious.Security.Cryptography.Blake2.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.1.0.0"}}}, "Microsoft.AspNetCore.Authentication.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Authentication.Core/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Core.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Authorization/2.2.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Authorization.Policy/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Authorization": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.Policy.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Hosting.Abstractions": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Http/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.ObjectPool": "9.0.0", "Microsoft.Extensions.Options": "7.0.0", "Microsoft.Net.Http.Headers": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "4.7.2"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0", "System.Buffers": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Mvc.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.AspNetCore.Mvc.Core/2.2.5": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.2.0", "Microsoft.AspNetCore.Authorization.Policy": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.Mvc.Abstractions": "2.2.0", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "2.2.0", "Microsoft.AspNetCore.Routing": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.DependencyModel": "2.1.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "System.Diagnostics.DiagnosticSource": "4.5.0", "System.Threading.Tasks.Extensions": "4.5.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Core.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.5.19109"}}}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.ResponseCaching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Routing/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.ObjectPool": "9.0.0", "Microsoft.Extensions.Options": "7.0.0"}, "runtime": {"lib/netcoreapp2.2/Microsoft.AspNetCore.Routing.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Routing.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Routing.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"dependencies": {"Microsoft.Net.Http.Headers": "2.2.0", "System.Text.Encodings.Web": "4.7.2"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.CSharp/4.5.0": {}, "Microsoft.Diagnostics.NETCore.Client/0.2.510501": {"dependencies": {"Microsoft.Extensions.Logging": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Diagnostics.NETCore.Client.dll": {"assemblyVersion": "0.2.10.10501", "fileVersion": "0.2.10.10501"}}}, "Microsoft.DotNet.PlatformAbstractions/2.1.0": {"dependencies": {"System.AppContext": "4.1.0", "System.Collections": "4.0.11", "System.IO": "4.1.0", "System.IO.FileSystem": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Runtime.InteropServices.RuntimeInformation": "4.0.0"}, "runtime": {"lib/netstandard1.3/Microsoft.DotNet.PlatformAbstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.EntityFrameworkCore/7.0.1": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "7.0.1", "Microsoft.EntityFrameworkCore.Analyzers": "7.0.1", "Microsoft.Extensions.Caching.Memory": "7.0.0", "Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "7.0.1.0", "fileVersion": "7.0.122.55819"}}}, "Microsoft.EntityFrameworkCore.Abstractions/7.0.1": {"runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "7.0.1.0", "fileVersion": "7.0.122.55819"}}}, "Microsoft.EntityFrameworkCore.Analyzers/7.0.1": {}, "Microsoft.EntityFrameworkCore.DynamicLinq/6.2.20": {"dependencies": {"Microsoft.EntityFrameworkCore": "7.0.1", "System.Linq.Dynamic.Core": "1.2.20"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.DynamicLinq.dll": {"assemblyVersion": "6.2.20.0", "fileVersion": "6.2.20.0"}}}, "Microsoft.EntityFrameworkCore.Relational/7.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "7.0.1", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51807"}}}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.DependencyModel/2.1.0": {"dependencies": {"Microsoft.DotNet.PlatformAbstractions": "2.1.0", "Newtonsoft.Json": "13.0.2", "System.Diagnostics.Debug": "4.0.11", "System.Dynamic.Runtime": "4.0.11", "System.Linq": "4.1.0"}, "runtime": {"lib/netstandard1.6/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Extensions.FileProviders.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18315"}}}, "Microsoft.Extensions.Hosting.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.Extensions.Logging/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Logging.Abstractions/7.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.ObjectPool/9.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}}, "Microsoft.Extensions.Options/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Primitives/7.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.IdentityModel.Abstractions/6.34.0": {"runtime": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "6.34.0.0", "fileVersion": "6.34.0.41017"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.34.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "6.34.0", "System.Text.Encoding": "4.3.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "6.34.0.0", "fileVersion": "6.34.0.41017"}}}, "Microsoft.IdentityModel.Logging/6.34.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.34.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "6.34.0.0", "fileVersion": "6.34.0.41017"}}}, "Microsoft.IdentityModel.Tokens/6.34.0": {"dependencies": {"Microsoft.CSharp": "4.5.0", "Microsoft.IdentityModel.Logging": "6.34.0", "System.Security.Cryptography.Cng": "4.5.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "6.34.0.0", "fileVersion": "6.34.0.41017"}}}, "Microsoft.Net.Http.Headers/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0", "System.Buffers": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Net.Http.Headers.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "MyNihongo.KanaConverter/1.0.3": {"dependencies": {"Microsoft.Extensions.ObjectPool": "9.0.0"}, "runtime": {"lib/netstandard2.0/MyNihongo.KanaConverter.dll": {"assemblyVersion": "1.0.3.0", "fileVersion": "1.0.3.0"}}}, "Newtonsoft.Json/13.0.2": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.2.27524"}}}, "Npgsql/7.0.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "7.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Npgsql.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Npgsql.EntityFrameworkCore.PostgreSQL/7.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "7.0.1", "Microsoft.EntityFrameworkCore.Abstractions": "7.0.1", "Microsoft.EntityFrameworkCore.Relational": "7.0.0", "Npgsql": "7.0.0"}, "runtime": {"lib/net6.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Pipelines.Sockets.Unofficial/2.2.2": {"dependencies": {"System.IO.Pipelines": "5.0.1"}, "runtime": {"lib/net5.0/Pipelines.Sockets.Unofficial.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.2.34088"}}}, "runtime.native.System/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "Sentry/4.13.0": {"runtime": {"lib/net6.0/Sentry.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Sentry.Profiling/4.13.0": {"dependencies": {"Microsoft.Diagnostics.NETCore.Client": "0.2.510501", "Sentry": "4.13.0"}, "runtime": {"lib/net6.0/Microsoft.Diagnostics.FastSerialization.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/net6.0/Microsoft.Diagnostics.Tracing.TraceEvent.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/net6.0/Sentry.Profiling.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "StackExchange.Redis/2.6.111": {"dependencies": {"Pipelines.Sockets.Unofficial": "2.2.2"}, "runtime": {"lib/net5.0/StackExchange.Redis.dll": {"assemblyVersion": "*******", "fileVersion": "2.6.111.64013"}}}, "System.AppContext/4.1.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Buffers/4.5.0": {}, "System.Collections/4.0.11": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.Debug/4.0.11": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/4.5.0": {}, "System.Dynamic.Runtime/4.0.11": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Linq.Expressions": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.1.0", "System.Reflection.Emit": "4.0.1", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}}, "System.Globalization/4.0.11": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt/6.34.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.34.0", "Microsoft.IdentityModel.Tokens": "6.34.0"}, "runtime": {"lib/net6.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "6.34.0.0", "fileVersion": "6.34.0.41017"}}}, "System.IO/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.0.11"}}, "System.IO.FileSystem/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.1.0", "System.IO.FileSystem.Primitives": "4.0.1", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.0.1", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.0.11"}}, "System.IO.FileSystem.Primitives/4.0.1": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.IO.Pipelines/5.0.1": {"runtime": {"lib/netcoreapp3.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.120.57516"}}}, "System.Linq/4.1.0": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.1.0"}}, "System.Linq.Dynamic.Core/1.2.20": {"runtime": {"lib/net6.0/System.Linq.Dynamic.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "System.Linq.Expressions/4.1.0": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Linq": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.1.0", "System.Reflection.Emit": "4.0.1", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Emit.Lightweight": "4.0.1", "System.Reflection.Extensions": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}}, "System.Memory/4.5.4": {}, "System.ObjectModel/4.0.12": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.3.0", "System.Threading": "4.0.11"}}, "System.Reactive/5.0.0": {"runtime": {"lib/net5.0/System.Reactive.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "*******"}}}, "System.Reflection/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.1.0", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit/4.0.1": {"dependencies": {"System.IO": "4.1.0", "System.Reflection": "4.1.0", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.ILGeneration/4.0.1": {"dependencies": {"System.Reflection": "4.1.0", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.Lightweight/4.0.1": {"dependencies": {"System.Reflection": "4.1.0", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.3.0"}}, "System.Reflection.Extensions/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Primitives/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions/4.1.0": {"dependencies": {"System.Reflection": "4.1.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.0.11", "System.Reflection": "4.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Extensions/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Handles/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.InteropServices/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.1.0", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.0.1"}}, "System.Runtime.InteropServices.RuntimeInformation/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.3.0", "System.Runtime.InteropServices": "4.1.0", "System.Threading": "4.0.11", "runtime.native.System": "4.0.0"}}, "System.Security.Cryptography.Cng/4.5.0": {}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.CodePages/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Encodings.Web/4.7.2": {}, "System.Text.Json/4.7.2": {}, "System.Threading/4.0.11": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.0.11"}}, "System.Threading.Tasks/4.0.11": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.5.1": {}, "Domain/1.0.0": {"dependencies": {"AWSSDK.S3": "3.7.9.42", "Entity": "1.0.0", "Helper": "1.0.0", "Microsoft.AspNetCore.Http.Features": "2.2.0"}, "runtime": {"Domain.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "Entity/1.0.0": {"dependencies": {"Helper": "1.0.0", "Microsoft.EntityFrameworkCore.Abstractions": "7.0.1"}, "runtime": {"Entity.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "ErrorCodeGenerator/1.0.0": {"dependencies": {"Newtonsoft.Json": "13.0.2"}, "runtime": {"ErrorCodeGenerator.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "Helper/1.0.0": {"dependencies": {"ErrorCodeGenerator": "1.0.0", "Kana.NET": "1.0.6", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "MyNihongo.KanaConverter": "1.0.3", "StackExchange.Redis": "2.6.111", "System.Text.Encoding.CodePages": "6.0.0"}, "runtime": {"Helper.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "Infrastructure/1.0.0": {"dependencies": {"AWSSDK.S3": "3.7.9.42", "Domain": "1.0.0", "GraphQL.Client": "6.0.0", "GraphQL.Client.Serializer.Newtonsoft": "6.0.0", "Konscious.Security.Cryptography.Argon2": "1.3.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.Mvc.Abstractions": "2.2.0", "Microsoft.AspNetCore.Mvc.Core": "2.2.5", "Microsoft.EntityFrameworkCore.DynamicLinq": "6.2.20", "PostgreDataContext": "1.0.0", "Sentry": "4.13.0", "Sentry.Profiling": "4.13.0", "System.IdentityModel.Tokens.Jwt": "6.34.0"}, "runtime": {"Infrastructure.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "PostgreDataContext/1.0.0": {"dependencies": {"Entity": "1.0.0", "Microsoft.EntityFrameworkCore": "7.0.1", "Npgsql.EntityFrameworkCore.PostgreSQL": "7.0.0"}, "runtime": {"PostgreDataContext.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "Domain.Core/*******": {"runtime": {"Domain.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"CommonChecker/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AWSSDK.Core/3.7.12.24": {"type": "package", "serviceable": true, "sha512": "sha512-uPAZ3a5Wu4k4Ml+TBAcOumZPxMpoHUHai0PdS3iLX0XPsDqAJPYviAIc1QXi2rVSDgqC2uVBxiEVSNYAgzoxbA==", "path": "awssdk.core/3.7.12.24", "hashPath": "awssdk.core.3.7.12.24.nupkg.sha512"}, "AWSSDK.S3/3.7.9.42": {"type": "package", "serviceable": true, "sha512": "sha512-JNpSmndHm0uoqLQ3dypHPaP5p8+Yz3zZrr6MsA9+YhDy17L7I66nUYrmAhaXkgTSHlD/5uTAnvC9D6ZZO9QhUA==", "path": "awssdk.s3/3.7.9.42", "hashPath": "awssdk.s3.3.7.9.42.nupkg.sha512"}, "GraphQL.Client/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-8yPNBbuVBpTptivyAlak4GZvbwbUcjeQTL4vN1HKHRuOykZ4r7l5fcLS6vpyPyLn0x8FsL31xbOIKyxbmR9rbA==", "path": "graphql.client/6.0.0", "hashPath": "graphql.client.6.0.0.nupkg.sha512"}, "GraphQL.Client.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-h7uzWFORHZ+CCjwr/ThAyXMr0DPpzEANDa4Uo54wqCQ+j7qUKwqYTgOrb1W40sqbvNaZm9v/X7It31SUw0maHA==", "path": "graphql.client.abstractions/6.0.0", "hashPath": "graphql.client.abstractions.6.0.0.nupkg.sha512"}, "GraphQL.Client.Abstractions.Websocket/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nr9bPf8gIOvLuXpqEpqr9z9jslYFJOvd0feHth3/kPqeR3uMbjF5pjiwh4jxyMcxHdr8Pb6QiXkV3hsSyt0v7A==", "path": "graphql.client.abstractions.websocket/6.0.0", "hashPath": "graphql.client.abstractions.websocket.6.0.0.nupkg.sha512"}, "GraphQL.Client.Serializer.Newtonsoft/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-6AFZjq4MZhR2e1ZZOi++NXMTq0y3Q+sT3S/UAIcLRJ7H6lQ8nxoAbzrlToui/m2j5gO9UL4Pmc+CdvM/H+t8Xg==", "path": "graphql.client.serializer.newtonsoft/6.0.0", "hashPath": "graphql.client.serializer.newtonsoft.6.0.0.nupkg.sha512"}, "GraphQL.Primitives/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yg72rrYDapfsIUrul7aF6wwNnTJBOFvuA9VdDTQpPa8AlAriHbufeXYLBcodKjfUdkCnaiggX1U/nEP08Zb5GA==", "path": "graphql.primitives/6.0.0", "hashPath": "graphql.primitives.6.0.0.nupkg.sha512"}, "Kana.NET/1.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-xc9teFuWQJ1h0YddJZxVWNTfvNPK648Wed4k8JYViNZN3txcV/5RXW/13nZsKjY/qndFSzJEta7mVr4jsVyXgg==", "path": "kana.net/1.0.6", "hashPath": "kana.net.1.0.6.nupkg.sha512"}, "Konscious.Security.Cryptography.Argon2/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/pNPlNmjx7r7CGvw0WqO8Rk5vHHyUuzAWhB/PRKLZ2cmhQXoM6TYUooJcuES9zE9D+6yTOl+4LmFV2nKtgDyFg==", "path": "konscious.security.cryptography.argon2/1.3.0", "hashPath": "konscious.security.cryptography.argon2.1.3.0.nupkg.sha512"}, "Konscious.Security.Cryptography.Blake2/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-enWOzNAlBjFcKoiBNVVLsa1ZzOciExznu+sUURE8dx322GI1LzrBxzEuZqjeOoK9oo9RubLtg96/7Y4JtUk9zA==", "path": "konscious.security.cryptography.blake2/1.1.0", "hashPath": "konscious.security.cryptography.blake2.1.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-VloMLDJMf3n/9ic5lCBOa42IBYJgyB1JhzLsL68Zqg+2bEPWfGBj/xCJy/LrKTArN0coOcZp3wyVTZlx0y9pHQ==", "path": "microsoft.aspnetcore.authentication.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Core/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-XlVJzJ5wPOYW+Y0J6Q/LVTEyfS4ssLXmt60T0SPP+D8abVhBTl+cgw2gDHlyKYIkcJg7btMVh383NDkMVqD/fg==", "path": "microsoft.aspnetcore.authentication.core/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.core.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-/L0W8H3jMYWyaeA9gBJqS/tSWBegP9aaTM0mjRhxTttBY9z4RVDRYJ2CwPAmAXIuPr3r1sOw+CS8jFVRGHRezQ==", "path": "microsoft.aspnetcore.authorization/2.2.0", "hashPath": "microsoft.aspnetcore.authorization.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization.Policy/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-aJCo6niDRKuNg2uS2WMEmhJTooQUGARhV2ENQ2tO5443zVHUo19MSgrgGo9FIrfD+4yKPF8Q+FF33WkWfPbyKw==", "path": "microsoft.aspnetcore.authorization.policy/2.2.0", "hashPath": "microsoft.aspnetcore.authorization.policy.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ubycklv+ZY7Kutdwuy1W4upWcZ6VFR8WUXU7l7B2+mvbDBBPAcfpi+E+Y5GFe+Q157YfA3C49D2GCjAZc7Mobw==", "path": "microsoft.aspnetcore.hosting.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-1PMijw8RMtuQF60SsD/JlKtVfvh4NORAhF4wjysdABhlhTrYmtgssqyncR0Stq5vqtjplZcj6kbT4LRTglt9IQ==", "path": "microsoft.aspnetcore.hosting.server.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.server.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-YogBSMotWPAS/X5967pZ+yyWPQkThxhmzAwyCHCSSldzYBkW5W5d6oPfBaPqQOnSHYTpSOSOkpZoAce0vwb6+A==", "path": "microsoft.aspnetcore.http/2.2.0", "hashPath": "microsoft.aspnetcore.http.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-2DgZ9rWrJtuR7RYiew01nGRzuQBDaGHGmK56Rk54vsLLsCdzuFUPqbDTJCS1qJQWTbmbIQ9wGIOjpxA1t0l7/w==", "path": "microsoft.aspnetcore.http.extensions/2.2.0", "hashPath": "microsoft.aspnetcore.http.extensions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "path": "microsoft.aspnetcore.http.features/2.2.0", "hashPath": "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ET6uZpfVbGR1NjCuLaLy197cQ3qZUjzl7EG5SL4GfJH/c9KRE89MMBrQegqWsh0w1iRUB/zQaK0anAjxa/pz4g==", "path": "microsoft.aspnetcore.mvc.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Core/2.2.5": {"type": "package", "serviceable": true, "sha512": "sha512-/8sr8ixIUD57UFwUntha9bOwex7/AkZfdk1f9oNJG1Ek7p/uuKVa7fuHmYZpQOf35Oxrt+2Ku4WPwMSbNxOuWg==", "path": "microsoft.aspnetcore.mvc.core/2.2.5", "hashPath": "microsoft.aspnetcore.mvc.core.2.2.5.nupkg.sha512"}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-CIHWEKrHzZfFp7t57UXsueiSA/raku56TgRYauV/W1+KAQq6vevz60zjEKaazt3BI76zwMz3B4jGWnCwd8kwQw==", "path": "microsoft.aspnetcore.responsecaching.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.responsecaching.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Routing/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-jAhDBy0wryOnMhhZTtT9z63gJbvCzFuLm8yC6pHzuVu9ZD1dzg0ltxIwT4cfwuNkIL/TixdKsm3vpVOpG8euWQ==", "path": "microsoft.aspnetcore.routing/2.2.0", "hashPath": "microsoft.aspnetcore.routing.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Routing.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-lRRaPN7jDlUCVCp9i0W+PB0trFaKB0bgMJD7hEJS9Uo4R9MXaMC8X2tJhPLmeVE3SGDdYI4QNKdVmhNvMJGgPQ==", "path": "microsoft.aspnetcore.routing.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.routing.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ErxAAKaDzxXASB/b5uLEkLgUWv1QbeVxyJYEHQwMaxXOeFFVkQxiq8RyfVcifLU7NR0QY0p3acqx4ZpYfhHDg==", "path": "microsoft.aspnetcore.webutilities/2.2.0", "hashPath": "microsoft.aspnetcore.webutilities.2.2.0.nupkg.sha512"}, "Microsoft.CSharp/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-kaj6Wb4qoMuH3HySFJhxwQfe8R/sJsNJnANrvv8WdFPMoNbKY5htfNscv+LHCu5ipz+49m2e+WQXpLXr9XYemQ==", "path": "microsoft.csharp/4.5.0", "hashPath": "microsoft.csharp.4.5.0.nupkg.sha512"}, "Microsoft.Diagnostics.NETCore.Client/0.2.510501": {"type": "package", "serviceable": true, "sha512": "sha512-juoqJYMDs+lRrrZyOkXXMImJHneCF23cuvO4waFRd2Ds7j+ZuGIPbJm0Y/zz34BdeaGiiwGWraMUlln05W1PCQ==", "path": "microsoft.diagnostics.netcore.client/0.2.510501", "hashPath": "microsoft.diagnostics.netcore.client.0.2.510501.nupkg.sha512"}, "Microsoft.DotNet.PlatformAbstractions/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-9KPDwvb/hLEVXYruVHVZ8BkebC8j17DmPb56LnqRF74HqSPLjCkrlFUjOtFpQPA2DeADBRTI/e69aCfRBfrhxw==", "path": "microsoft.dotnet.platformabstractions/2.1.0", "hashPath": "microsoft.dotnet.platformabstractions.2.1.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/7.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-uvzJkdmqbB50COlCiNaBjWc4cl3kHBO9P7glPk6wK8xyLrli4xeVip+pmMyT/kYc2shWm1YdxegxFqKeCvQXAA==", "path": "microsoft.entityframeworkcore/7.0.1", "hashPath": "microsoft.entityframeworkcore.7.0.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/7.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-pPaoi2RHAkrsJ2kcGZGBiJFUXSS1LC+qET6HCn4hXP+X/kvIriJLv8lx6ddI6gTluGvm/lDgPAA0zZDXYaKf3A==", "path": "microsoft.entityframeworkcore.abstractions/7.0.1", "hashPath": "microsoft.entityframeworkcore.abstractions.7.0.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/7.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-4A4NFkPFoRMdry24Hjdb5/sOH3tU3hzFePTGnSg8P+VBrFp/EO8kcA4L0hBzwbXj2HGNL8I/quYyS5GVtb4EOg==", "path": "microsoft.entityframeworkcore.analyzers/7.0.1", "hashPath": "microsoft.entityframeworkcore.analyzers.7.0.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.DynamicLinq/6.2.20": {"type": "package", "serviceable": true, "sha512": "sha512-RD72FFL8F5KTDj6vyM/tbSrUICpkYtpr+6CLnNd4USACQ/CxuzsLQBD15cWTtlBlXIheJwKuak2xew70WrBe8g==", "path": "microsoft.entityframeworkcore.dynamiclinq/6.2.20", "hashPath": "microsoft.entityframeworkcore.dynamiclinq.6.2.20.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eQiYygtR2xZ0Uy7KtiFRHpoEx/U8xNwbNRgu1pEJgSxbJLtg6tDL1y2YcIbSuIRSNEljXIIHq/apEhGm1QL70g==", "path": "microsoft.entityframeworkcore.relational/7.0.0", "hashPath": "microsoft.entityframeworkcore.relational.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IeimUd0TNbhB4ded3AbgBLQv2SnsiVugDyGV1MvspQFVlA07nDC7Zul7kcwH5jWN3JiTcp/ySE83AIJo8yfKjg==", "path": "microsoft.extensions.caching.abstractions/7.0.0", "hashPath": "microsoft.extensions.caching.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xpidBs2KCE2gw1JrD0quHE72kvCaI3xFql5/Peb2GRtUuZX+dYPoK/NTdVMiM67Svym0M0Df9A3xyU0FbMQhHw==", "path": "microsoft.extensions.caching.memory/7.0.0", "hashPath": "microsoft.extensions.caching.memory.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-f34u2eaqIjNO9YLHBz8rozVZ+TcFiFs0F3r7nUJd7FRkVSxk8u4OpoK226mi49MwexHOR2ibP9MFvRUaLilcQQ==", "path": "microsoft.extensions.configuration.abstractions/7.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-elNeOmkeX3eDVG6pYVeV82p29hr+UKDaBhrZyWvWLw/EVZSYEkZlQdkp0V39k/Xehs2Qa0mvoCvkVj3eQxNQ1Q==", "path": "microsoft.extensions.dependencyinjection/7.0.0", "hashPath": "microsoft.extensions.dependencyinjection.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-h3j/QfmFN4S0w4C2A6X7arXij/M/OVw3uQHSOFxnND4DyAzO1F9eMX7Eti7lU/OkSthEE0WzRsfT/Dmx86jzCw==", "path": "microsoft.extensions.dependencyinjection.abstractions/7.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-nS2XKqi+1A1umnYNLX2Fbm/XnzCxs5i+zXVJ3VC6r9t2z0NZr9FLnJN4VQpKigdcWH/iFTbMuX6M6WQJcTjVIg==", "path": "microsoft.extensions.dependencymodel/2.1.0", "hashPath": "microsoft.extensions.dependencymodel.2.1.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-EcnaSsPTqx2MGnHrmWOD0ugbuuqVT8iICqSqPzi45V5/MA1LjUNb0kwgcxBGqizV1R+WeBK7/Gw25Jzkyk9bIw==", "path": "microsoft.extensions.fileproviders.abstractions/2.2.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-+k4AEn68HOJat5gj1TWa6X28WlirNQO9sPIIeQbia+91n03esEtMSSoekSTpMjUzjqtJWQN3McVx0GvSPFHF/Q==", "path": "microsoft.extensions.hosting.abstractions/2.2.0", "hashPath": "microsoft.extensions.hosting.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nw2muoNrOG5U5qa2ZekXwudUn2BJcD41e65zwmDHb1fQegTX66UokLWZkJRpqSSHXDOWZ5V0iqhbxOEky91atA==", "path": "microsoft.extensions.logging/7.0.0", "hashPath": "microsoft.extensions.logging.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-kmn78+LPVMOWeITUjIlfxUPDsI0R6G0RkeAMBmQxAJ7vBJn4q2dTva7pWi65ceN5vPGjJ9q/Uae2WKgvfktJAw==", "path": "microsoft.extensions.logging.abstractions/7.0.0", "hashPath": "microsoft.extensions.logging.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UbsU/gYe4nv1DeqMXIVzDfNNek7Sk2kKuAOXL/Y+sLcAR0HwFUqzg1EPiU88jeHNe0g81aPvvHbvHarQr3r9IA==", "path": "microsoft.extensions.objectpool/9.0.0", "hashPath": "microsoft.extensions.objectpool.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lP1yBnTTU42cKpMozuafbvNtQ7QcBjr/CcK3bYOGEMH55Fjt+iecXjT6chR7vbgCMqy3PG3aNQSZgo/EuY/9qQ==", "path": "microsoft.extensions.options/7.0.0", "hashPath": "microsoft.extensions.options.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-um1KU5kxcRp3CNuI8o/GrZtD4AIOXDk+RLsytjZ9QPok3ttLUelLKpilVPuaFT3TFjOhSibUAso0odbOaCDj3Q==", "path": "microsoft.extensions.primitives/7.0.0", "hashPath": "microsoft.extensions.primitives.7.0.0.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/6.34.0": {"type": "package", "serviceable": true, "sha512": "sha512-5nInt1KKSpKQBlhe6gXz4yKxRzRUQa21vCvSIIKKzAI2e1r9PHQOZc7aRzBA8L/JCvBxLbCxelvUqun6qwWPJg==", "path": "microsoft.identitymodel.abstractions/6.34.0", "hashPath": "microsoft.identitymodel.abstractions.6.34.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/6.34.0": {"type": "package", "serviceable": true, "sha512": "sha512-CZMom/ZoWcgjxLMxmCmcEkuoA0OA4swN1CGeMBQyxF/hEZgRbWK9EnWVJ9/oMUq3D1+OGJjnbN+W6gFq9kZcEg==", "path": "microsoft.identitymodel.jsonwebtokens/6.34.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.6.34.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/6.34.0": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON>0AbluNkI30/VKa96PxJhhFZDx/NGYIXFrRIRq1N5/V0TToaiuc3hM90QLFszT2BBQefnp/wjm12ilSudmt9bg==", "path": "microsoft.identitymodel.logging/6.34.0", "hashPath": "microsoft.identitymodel.logging.6.34.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/6.34.0": {"type": "package", "serviceable": true, "sha512": "sha512-PEPcGMqbEwEwbpQ6nTld9Nqq6V5BPZSOfk71qXZ7h7DuGuxa13bWvjImhJba5Ko88YvIuZuOBJWFZmjLfwbNXA==", "path": "microsoft.identitymodel.tokens/6.34.0", "hashPath": "microsoft.identitymodel.tokens.6.34.0.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-iZNkjYqlo8sIOI0bQfpsSoMTmB/kyvmV2h225ihyZT33aTp48ZpF6qYnXxzSXmHt8DpBAwBTX+1s1UFLbYfZKg==", "path": "microsoft.net.http.headers/2.2.0", "hashPath": "microsoft.net.http.headers.2.2.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "MyNihongo.KanaConverter/1.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-/HK2iltRWiYKGhFGKvcfMAyixzwiOkwQ1Ppp6/DZJQxPG0bKBYMYYIJd5oRyb0hDy11nDNea4UG/0xSsVGolRA==", "path": "mynihongo.kanaconverter/1.0.3", "hashPath": "mynihongo.kanaconverter.1.0.3.nupkg.sha512"}, "Newtonsoft.Json/13.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-R2pZ3B0UjeyHShm9vG+Tu0EBb2lC8b0dFzV9gVn50ofHXh9Smjk6kTn7A/FdAsC8B5cKib1OnGYOXxRBz5XQDg==", "path": "newtonsoft.json/13.0.2", "hashPath": "newtonsoft.json.13.0.2.nupkg.sha512"}, "Npgsql/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tOBFksJZ2MiEz8xtDUgS5IG19jVO3nSP15QDYWiiGpXHe0PsLoQBts2Sg3hHKrrLTuw+AjsJz9iKvvGNHyKDIg==", "path": "npgsql/7.0.0", "hashPath": "npgsql.7.0.0.nupkg.sha512"}, "Npgsql.EntityFrameworkCore.PostgreSQL/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CyUNlFZmtX2Kmw8XK5Tlx5eVUCzWJ+zJHErxZiMo2Y8zCRuH9+/OMGwG+9Mmp5zD5p3Ifbi5Pp3btsqoDDkSZQ==", "path": "npgsql.entityframeworkcore.postgresql/7.0.0", "hashPath": "npgsql.entityframeworkcore.postgresql.7.0.0.nupkg.sha512"}, "Pipelines.Sockets.Unofficial/2.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-Bhk0FWxH1paI+18zr1g5cTL+ebeuDcBCR+rRFO+fKEhretgjs7MF2Mc1P64FGLecWp4zKCUOPzngBNrqVyY7Zg==", "path": "pipelines.sockets.unofficial/2.2.2", "hashPath": "pipelines.sockets.unofficial.2.2.2.nupkg.sha512"}, "runtime.native.System/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-QfS/nQI7k/BLgmLrw7qm7YBoULEvgWnPI+cYsbfCVFTW8Aj+i8JhccxcFMu1RWms0YZzF+UHguNBK4Qn89e2Sg==", "path": "runtime.native.system/4.0.0", "hashPath": "runtime.native.system.4.0.0.nupkg.sha512"}, "Sentry/4.13.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wfw3M1WpFcrYaGzPm7QyUTfIOYkVXQ1ry6p4WYjhbLz9fPwV23SGQZTFDpdox67NHM0V0g1aoQ4YKLm4ANtEEg==", "path": "sentry/4.13.0", "hashPath": "sentry.4.13.0.nupkg.sha512"}, "Sentry.Profiling/4.13.0": {"type": "package", "serviceable": true, "sha512": "sha512-FJM5SDwHGN2+ceVGCeVQLHXNSBQEHSpvPrBVqVdbbduIdf7yWvaCdCn8MZyCJsvOuKMpwbrle6lXtxrKOZ2ESA==", "path": "sentry.profiling/4.13.0", "hashPath": "sentry.profiling.4.13.0.nupkg.sha512"}, "StackExchange.Redis/2.6.111": {"type": "package", "serviceable": true, "sha512": "sha512-49NlwihVG9I1YaPqYBx2e2yPqC4ecXMog8zVXMC3rjj2kufGkC3ofqvhOPBKP0c9ZQdJ3hhzduM7ckOQTE+gxg==", "path": "stackexchange.redis/2.6.111", "hashPath": "stackexchange.redis.2.6.111.nupkg.sha512"}, "System.AppContext/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-3QjO4jNV7PdKkmQAVp9atA+usVnKRwI3Kx1nMwJ93T0LcQfx7pKAYk0nKz5wn1oP5iqlhZuy6RXOFdhr7rDwow==", "path": "system.appcontext/4.1.0", "hashPath": "system.appcontext.4.1.0.nupkg.sha512"}, "System.Buffers/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-pL2ChpaRRWI/p4LXyy4RgeWlYF2sgfj/pnVMvBqwNFr5cXg7CXNnWZWxrOONLg8VGdFB8oB+EG2Qw4MLgTOe+A==", "path": "system.buffers/4.5.0", "hashPath": "system.buffers.4.5.0.nupkg.sha512"}, "System.Collections/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-YUJGz6eFKqS0V//mLt25vFGrrCvOnsXjlvFQs+KimpwNxug9x0Pzy4PlFMU3Q2IzqAa9G2L4LsK3+9vCBK7oTg==", "path": "system.collections/4.0.11", "hashPath": "system.collections.4.0.11.nupkg.sha512"}, "System.Diagnostics.Debug/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-w5U95fVKHY4G8ASs/K5iK3J5LY+/dLFd4vKejsnI/ZhBsWS9hQakfx3Zr7lRWKg4tAw9r4iktyvsTagWkqYCiw==", "path": "system.diagnostics.debug/4.0.11", "hashPath": "system.diagnostics.debug.4.0.11.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-eIHRELiYDQvsMToML81QFkXEEYXUSUT2F28t1SGrevWqP+epFdw80SyAXIKTXOHrIEXReFOEnEr7XlGiC2GgOg==", "path": "system.diagnostics.diagnosticsource/4.5.0", "hashPath": "system.diagnostics.diagnosticsource.4.5.0.nupkg.sha512"}, "System.Dynamic.Runtime/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-db34f6LHYM0U0JpE+sOmjar27BnqTVkbLJhgfwMpTdgTigG/Hna3m2MYVwnFzGGKnEJk2UXFuoVTr8WUbU91/A==", "path": "system.dynamic.runtime/4.0.11", "hashPath": "system.dynamic.runtime.4.0.11.nupkg.sha512"}, "System.Globalization/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-B95h0YLEL2oSnwF/XjqSWKnwKOy/01VWkNlsCeMTFJLLabflpGV26nK164eRs5GiaRSBGpOxQ3pKoSnnyZN5pg==", "path": "system.globalization/4.0.11", "hashPath": "system.globalization.4.0.11.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/6.34.0": {"type": "package", "serviceable": true, "sha512": "sha512-c0misfmFT3QxKY+a16PGlj+DtiUzoPaf26m2avyPZaLRc9vlIdLtmovfRY5MqN+y/SEoBSRXrgVaeZGPgFQQ6w==", "path": "system.identitymodel.tokens.jwt/6.34.0", "hashPath": "system.identitymodel.tokens.jwt.6.34.0.nupkg.sha512"}, "System.IO/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-3KlTJceQc3gnGIaHZ7UBZO26SHL1SHE4ddrmiwumFnId+CEHP+O8r386tZKaE6zlk5/mF8vifMBzHj9SaXN+mQ==", "path": "system.io/4.1.0", "hashPath": "system.io.4.1.0.nupkg.sha512"}, "System.IO.FileSystem/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-IBErlVq5jOggAD69bg1t0pJcHaDbJbWNUZTPI96fkYWzwYbN6D9wRHMULLDd9dHsl7C2YsxXL31LMfPI1SWt8w==", "path": "system.io.filesystem/4.0.1", "hashPath": "system.io.filesystem.4.0.1.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-kWkKD203JJKxJeE74p8aF8y4Qc9r9WQx4C0cHzHPrY3fv/L/IhWnyCHaFJ3H1QPOH6A93whlQ2vG5nHlBDvzWQ==", "path": "system.io.filesystem.primitives/4.0.1", "hashPath": "system.io.filesystem.primitives.4.0.1.nupkg.sha512"}, "System.IO.Pipelines/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-qEePWsaq9LoEEIqhbGe6D5J8c9IqQOUuTzzV6wn1POlfdLkJliZY3OlB0j0f17uMWlqZYjH7txj+2YbyrIA8Yg==", "path": "system.io.pipelines/5.0.1", "hashPath": "system.io.pipelines.5.0.1.nupkg.sha512"}, "System.Linq/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-bQ0iYFOQI0nuTnt+NQADns6ucV4DUvMdwN6CbkB1yj8i7arTGiTN5eok1kQwdnnNWSDZfIUySQY+J3d5KjWn0g==", "path": "system.linq/4.1.0", "hashPath": "system.linq.4.1.0.nupkg.sha512"}, "System.Linq.Dynamic.Core/1.2.20": {"type": "package", "serviceable": true, "sha512": "sha512-J<PERSON>ggojPaq2QM603WCwghxxwNmi8ESDLCSTTjfq/TVFvj0J2XjXfvfa0X7pe8NOruF4CbPG2K46AWmgvk0Umoqg==", "path": "system.linq.dynamic.core/1.2.20", "hashPath": "system.linq.dynamic.core.1.2.20.nupkg.sha512"}, "System.Linq.Expressions/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-I+y02iqkgmCAyfbqOmSDOgqdZQ5tTj80Akm5BPSS8EeB0VGWdy6X1KCoYe8Pk6pwDoAKZUOdLVxnTJcExiv5zw==", "path": "system.linq.expressions/4.1.0", "hashPath": "system.linq.expressions.4.1.0.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.ObjectModel/4.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-tAgJM1xt3ytyMoW4qn4wIqgJYm7L7TShRZG4+Q4Qsi2PCcj96pXN7nRywS9KkB3p/xDUjc2HSwP9SROyPYDYKQ==", "path": "system.objectmodel/4.0.12", "hashPath": "system.objectmodel.4.0.12.nupkg.sha512"}, "System.Reactive/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-erBZjkQHWL9jpasCE/0qKAryzVBJFxGHVBAvgRN1bzM0q2s1S4oYREEEL0Vb+1kA/6BKb5FjUZMp5VXmy+gzkQ==", "path": "system.reactive/5.0.0", "hashPath": "system.reactive.5.0.0.nupkg.sha512"}, "System.Reflection/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-JCKANJ0TI7kzoQzuwB/OoJANy1Lg338B6+JVacPl4TpUwi3cReg3nMLplMq2uqYfHFQpKIlHAUVAJlImZz/4ng==", "path": "system.reflection/4.1.0", "hashPath": "system.reflection.4.1.0.nupkg.sha512"}, "System.Reflection.Emit/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-P2wqAj72fFjpP6wb9nSfDqNBMab+2ovzSDzUZK7MVIm54tBJEPr9jWfSjjoTpPwj1LeKcmX3vr0ttyjSSFM47g==", "path": "system.reflection.emit/4.0.1", "hashPath": "system.reflection.emit.4.0.1.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Ov6dU8Bu15Bc7zuqttgHF12J5lwSWyTf1S+FJouUXVMSqImLZzYaQ+vRr1rQ0OZ0HqsrwWl4dsKHELckQkVpgA==", "path": "system.reflection.emit.ilgeneration/4.0.1", "hashPath": "system.reflection.emit.ilgeneration.4.0.1.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-sSzHHXueZ5Uh0OLpUQprhr+ZYJrLPA2Cmr4gn0wj9+FftNKXx8RIMKvO9qnjk2ebPYUjZ+F2ulGdPOsvj+MEjA==", "path": "system.reflection.emit.lightweight/4.0.1", "hashPath": "system.reflection.emit.lightweight.4.0.1.nupkg.sha512"}, "System.Reflection.Extensions/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-GYrtRsZcMuHF3sbmRHfMYpvxZoIN2bQGrYGerUiWLEkqdEUQZhH3TRSaC/oI4wO0II1RKBPlpIa1TOMxIcOOzQ==", "path": "system.reflection.extensions/4.0.1", "hashPath": "system.reflection.extensions.4.0.1.nupkg.sha512"}, "System.Reflection.Primitives/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-4inTox4wTBaDhB7V3mPvp9XlCbeGYWVEM9/fXALd52vNEAVisc1BoVWQPuUuD0Ga//dNbA/WeMy9u9mzLxGTHQ==", "path": "system.reflection.primitives/4.0.1", "hashPath": "system.reflection.primitives.4.0.1.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-tsQ/ptQ3H5FYfON8lL4MxRk/8kFyE0A+tGPXmVP967cT/gzLHYxIejIYSxp4JmIeFHVP78g/F2FE1mUUTbDtrg==", "path": "system.reflection.typeextensions/4.1.0", "hashPath": "system.reflection.typeextensions.4.1.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-TxwVeUNoTgUOdQ09gfTjvW411MF+w9MBYL7AtNVc+HtBCFlutPLhUCdZjNkjbhj3bNQWMdHboF0KIWEOjJssbA==", "path": "system.resources.resourcemanager/4.0.1", "hashPath": "system.resources.resourcemanager.4.0.1.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-CUOHjTT/vgP0qGW22U4/hDlOqXmcPq5YicBaXdUR2UiUoLwBT+olO6we4DVbq57jeX5uXH2uerVZhf0qGj+sVQ==", "path": "system.runtime.extensions/4.1.0", "hashPath": "system.runtime.extensions.4.1.0.nupkg.sha512"}, "System.Runtime.Handles/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-nCJvEKguXEvk2ymk1gqj625vVnlK3/xdGzx0vOKicQkoquaTBJTP13AIYkocSUwHCLNBwUbXTqTWGDxBTWpt7g==", "path": "system.runtime.handles/4.0.1", "hashPath": "system.runtime.handles.4.0.1.nupkg.sha512"}, "System.Runtime.InteropServices/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-16eu3kjHS633yYdkjwShDHZLRNMKVi/s0bY8ODiqJ2RfMhDMAwxZaUaWVnZ2P71kr/or+X9o/xFWtNqz8ivieQ==", "path": "system.runtime.interopservices/4.1.0", "hashPath": "system.runtime.interopservices.4.1.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hWPhJxc453RCa8Z29O91EmfGeZIHX1ZH2A8L6lYQVSaKzku2DfArSfMEb1/MYYzPQRJZeu0c9dmYeJKxW5Fgng==", "path": "system.runtime.interopservices.runtimeinformation/4.0.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.0.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-WG3r7EyjUe9CMPFSs6bty5doUqT+q9pbI80hlNzo2SkPkZ4VTuZkGWjpp77JB8+uaL4DFPRdBsAY+DX3dBK92A==", "path": "system.security.cryptography.cng/4.5.0", "hashPath": "system.security.cryptography.cng.4.5.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZFCILZuOvtKPauZ/j/swhvw68ZRi9ATCfvGbk1QfydmcXBkIWecWKn/250UH7rahZ5OoDBaiAudJtPvLwzw85A==", "path": "system.text.encoding.codepages/6.0.0", "hashPath": "system.text.encoding.codepages.6.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/4.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-iTUgB/WtrZ1sWZs84F2hwyQhiRH6QNjQv2DkwrH+WP6RoFga2Q1m3f9/Q7FG8cck8AdHitQkmkXSY8qylcDmuA==", "path": "system.text.encodings.web/4.7.2", "hashPath": "system.text.encodings.web.4.7.2.nupkg.sha512"}, "System.Text.Json/4.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-TcMd95wcrubm9nHvJEQs70rC0H/8omiSGGpU4FQ/ZA1URIqD4pjmFJh2Mfv1yH1eHgJDWTi2hMDXwTET+zOOyg==", "path": "system.text.json/4.7.2", "hashPath": "system.text.json.4.7.2.nupkg.sha512"}, "System.Threading/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-N+3xqIcg3VDKyjwwCGaZ9HawG9aC6cSDI+s7ROma310GQo8vilFZa86hqKppwTHleR/G0sfOzhvgnUxWCR/DrQ==", "path": "system.threading/4.0.11", "hashPath": "system.threading.4.0.11.nupkg.sha512"}, "System.Threading.Tasks/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-k1S4Gc6IGwtHGT8188RSeGaX86Qw/wnrgNLshJvsdNUOPP9etMmo8S07c+UlOAx4K/xLuN9ivA1bD0LVurtIxQ==", "path": "system.threading.tasks/4.0.11", "hashPath": "system.threading.tasks.4.0.11.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-WSKUTtLhPR8gllzIWO2x6l4lmAIfbyMAiTlyXAis4QBDonXK4b4S6F8zGARX4/P8wH3DH+sLdhamCiHn+fTU1A==", "path": "system.threading.tasks.extensions/4.5.1", "hashPath": "system.threading.tasks.extensions.4.5.1.nupkg.sha512"}, "Domain/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Entity/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "ErrorCodeGenerator/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Helper/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Infrastructure/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "PostgreDataContext/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Domain.Core/*******": {"type": "reference", "serviceable": false, "sha512": ""}}}