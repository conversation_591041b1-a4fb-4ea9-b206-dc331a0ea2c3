﻿namespace Domain.Enum
{
    public static class PtSocialHistoryEnum
    {
        public enum SmokingStatus
        {
            Smokes = 1, // 吸う
            DoesNotSmoke = 2, // 吸わない
            UsedToSmoke = 3 // 以前吸っていた
        }

        public enum SmokingDuration
        {
            NumberOfYears = 1, // 年数
            YearStarted = 2, // 開始した年
            AgeStarted = 3 // 開始した年齢
        }

        public enum DrinkingStatus
        {
            HardlyDrink = 1, //ほとんど飲まない（飲めない）
            Occasionally = 2, // 時々
            Everyday = 3 // 毎日
        }

        public enum AmountOfAlcohol
        {
            Less_Than_One_Unit = 1, // 1合未満
            One_To_Less_Than_Two_Units = 2, // 1 to less than 2 units
            Two_To_Less_Than_Three_Units = 3, // 2 to less than 3 units
            Three_UnitsOrMore = 4 // 3 units or more

        }
    }
}
