﻿namespace Domain.Enum;

public static class EpsEnum
{
    public enum RequestType
    {
        PreCheckDuplicateMedication = 1, // 1: 重複投薬等チェック事前処理
        CheckDuplicateMedication = 2,    // 2: 重複投薬等チェック
        RegisterElectronicPrescription = 3, // 3: 電子処方箋登録
        RegisterPaperPrescription = 4,    // 4: 紙の処方箋登録
        ModifyPrescriptionInfo = 5,       // 5: 処方箋情報の変更
        CancelPrescriptionInfo = 6,       // 6: 処方箋情報の取消
        GetPrescriptionCopy = 7,          // 7: 処方箋控えの取得
        GetDispensingResult = 8,          // 8: 調剤結果取得
        BulkGetDispensingResults = 9,     // 9: 調剤結果一括取得
        GetIndividualDispensingResult = 10, // 10: 調剤結果一括取得の内の個々の調剤結果取得
        SearchPrescriptionById = 11       // 11: 処方箋ID検索
    }

    public enum RequestStatus
    {
        Unprocessed = 0,   // 0: 未処理
        Processing = 1,    // 1: 処理中
        Error = 8,         // 8: エラー
        Completed = 9      // 9: 正常終了
    }

    public enum PrintEpsReference
    {
        Print = 1,    // 1: 発行する
        DoNotPrint = 2 // 2: 発行しない
    }

    public enum PrescriptionIssueType
    {
        Electronic = 1, // 1: 電子 
        Paper = 2       // 2: 紙 
    }

    public enum RefileCount
    {
        SingleUse = 1,        // 1: リフィルではない通常の処方箋（総使用回数1回）
        RefillTwice = 2,      // 2: リフィル処方箋（総使用回数2回）
        RefillThrice = 3      // 3: リフィル処方箋（総使用回数3回）
    }

    public enum PrescriptionStatus
    {
        Registered = 0,       // 0: 登録
        PendingCancellation = 1, // 1: 取消待ち
        Cancelling = 2,       // 2: 取消中
        Cancelled = 3         // 3: 取消済み
    }

    public enum DeletedReason
    {
        NotDeleted = 0,
        AutoCancellation = 1,   // 1: 自動取消
        Modification = 2,       // 2: 変更
        Error = 3,              // 3: エラー
        RegistrationAborted = 4,// 4: 登録中断
        ManualCancellation = 5, // 5: 手動取消
        PaperPrescriptionIssuedWithoutExchangeNumber = 6 // 6: 紙の処方箋（引換番号なし）発行
    }

    public enum EpsDispensingResultType
    {
        ValidResult = 1,       // 1: 調剤結果（有効
        InvalidResult = 2,     // 2: 調剤結果（無効
        PrescriptionCollected = 3, // 3: 処方箋回収
        DispensingInProgress = 4 // 4: 調剤中
    }

    public enum EpsDispensingMessageFlag
    {
        NoMessage = 1,     // 1: 伝達事項なし
        HasMessage = 2,    // 2: 伝達事項あり
        Confirmed = 3,      // 3: 確認済み
    }

    public enum CheckResult
    {
        DuplicateWithoutDoctorApproval = 0,  // 0: 重複等あり(医師許可なし)
        DuplicateWithDoctorApproval = 1,     // 1: 重複等あり(医師許可)
        NoDuplicate = 2                       // 2: 重複等なし
    }

    public enum SameMedicalInstitutionAlertFlg
    {
        ExcludeOwnInstitution = 1,    // 1: 自院分をチェック対象にしない
        IncludeOwnInstitution = 2      // 2: 自院分をチェック対象にする
    }

    public enum OnlineConsent
    {
        NoConsent = 0,    // 0: オンライン資格確認端末による同意なし
        ConsentGiven = 1  // 1: オンライン資格確認端末による同意あり
    }

    public enum OralBrowsingConsent
    {
        NoConsent = 0,    // 0: オンライン資格確認端末による同意なし
        ConsentGiven = 1  // 1: オンライン資格確認端末による同意あり
    }

    public enum YohoHosokuKbn
    {
        NotSpecified = 0,    // 0: 未指定
        GradualReduction = 1, // 1: 漸減
        Packaged = 2,        // 2: 一包化
        EveryOtherDay = 3,   // 3: 隔日
        Crushing = 4,        // 4: 粉砕
        ContinuedUsage = 5,  // 5: 用法の続き
        ApplicationSite = 6, // 6: 部位 
        SingleDose = 7       // 7: １回使用量
    }

    public enum YohoHosokuRec
    {
        NotSpecified = 0,                // 0: 未指定
        DosageConditionSpecified = 1,    // 1: 頓用の条件指定
        AdministrationTiming = 2,        // 2: 投与タイミング
        AdministrationTime = 3,          // 3: 投与時刻 
        AdministrationInterval = 4,      // 4: 投与間隔 
        ApplicationSiteLeftRightBoth = 50, // 50: 部位（左・右・両）
        ApplicationSiteOther = 51        // 51: 部位（その他）
    }

    public enum LoginType
    {
        Standard = 0,   // 0: 標準(ユーザーID&パスワード)
        StandardPlusHPKI = 1 // 1: 標準+HPKI 
    }

    public enum Delete
    {
        None = 0,
        Deleted = 1,
    }
    public enum EpsCsvType
    {
        Electronic = 1,
        Paper = 2,
        Unfinished = 3
    }
}