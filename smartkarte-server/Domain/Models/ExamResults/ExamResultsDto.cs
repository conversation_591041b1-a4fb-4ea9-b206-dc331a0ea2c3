﻿namespace Domain.Models.ExamResults
{
    public class ExamResultsDto
    {
        public ExamResultsDto()
        {
            ExamResultsModels = new();
            AverageDay = new();
            AverageMonths = new();
        }

        public ExamResultsDto(List<ExamResultsModel> examResultsModels, Dictionary<long, Dictionary<string, double>> averageDay, Dictionary<string, Dictionary<string, double>> averageMonths)
        {
            ExamResultsModels = examResultsModels;
            AverageDay = averageDay;
            AverageMonths = averageMonths;
        }

        public List<ExamResultsModel> ExamResultsModels { get; private set; }

        public Dictionary<long, Dictionary<string, double>> AverageDay { get; private set; }

        public Dictionary<string, Dictionary<string, double>> AverageMonths { get; private set; }
    }
}
