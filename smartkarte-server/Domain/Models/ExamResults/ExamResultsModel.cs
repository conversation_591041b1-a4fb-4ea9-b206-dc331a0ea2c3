﻿using Domain.Models.SpecialNote.PatientInfo;

namespace Domain.Models.ExamResults
{
    public class ExamResultsModel
    {
        public ExamResultsModel(string dspCenterName, string kensaTime, List<KensaInfDetailModel> kensaInfDetailModels, int iraiDate, int inoutKbn, int sikyuKbn, int tosekiKbn, string centerCd = "")
        {
            KensaTime = kensaTime;
            KensaInfDetailModels = kensaInfDetailModels;
            IraiDate = iraiDate;
            DspCenterName = dspCenterName;
            InoutKbn = inoutKbn;
            SikyuKbn = sikyuKbn;
            TosekiKbn = tosekiKbn;
            CenterCd = centerCd;
        }

        public int IraiDate { get; private set; }

        public string KensaTime { get; private set; }
        
        public string DspCenterName { get; private set; }

        public int InoutKbn { get; private set; }

        public int SikyuKbn {  get; private set; }

        public int TosekiKbn {  get; private set; }

        public List<KensaInfDetailModel> KensaInfDetailModels { get; private set; }

        public string CenterCd { get; private set; } = string.Empty;
    }
}
