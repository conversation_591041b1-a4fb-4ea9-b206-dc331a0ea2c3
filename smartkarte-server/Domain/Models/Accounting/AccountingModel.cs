﻿using Helper.Common;

namespace Domain.Models.Accounting;

public class AccountingModel
{
    public AccountingModel(int hpId, long ptId, int sinDate, long raiinNo, int thisWari, int credit, int paymentMethodCd, string comment, int sumAdjust, int nyukinKbn)
    {
        HpId = hpId;
        PtId = ptId;
        SeikyuSinDate = sinDate;
        Month = 0;
        RaiinNo = raiinNo;
        HokenPid = 0;
        OyaRaiinNo = 0;
        NyukinKbn = nyukinKbn;
        SeikyuTensu = 0;
        SeikyuGaku = sumAdjust;
        AdjustFutan = thisWari;
        NyukinGaku = credit;
        PaymentMethodCd = paymentMethodCd;
        NyukinDate = 0;
        UketukeSbt = 0;
        NyukinCmt = comment;
        UnPaid = 0;
        NewSeikyuGaku = 0;
        NewAdjustFutan = 0;
        KaDisplay = string.Empty;
        HokenPatternName = string.Empty;
        IsSeikyuRow = false;
        SortNo = 0;
        SeqNo = 0;
        SeikyuDetail = string.Empty;
        SeikyuAdjustFutan = 0;
        IsDelete = false;
        NewSeikyuDetail = string.Empty;
        NewSeikyuTensu = 0;
        ErrorCode = string.Empty;
        UserMessage = string.Empty;

    }

    public AccountingModel()
    {
        HpId = 0;
        PtId = 0;
        SeikyuSinDate = 0;
        Month = 0;
        RaiinNo = 0;
        HokenPid = 0;
        OyaRaiinNo = 0;
        NyukinKbn = 0;
        SeikyuTensu = 0;
        SeikyuGaku = 0;
        AdjustFutan = 0;
        NyukinGaku = 0;
        PaymentMethodCd = 0;
        NyukinDate = 0;
        UketukeSbt = 0;
        NyukinCmt = string.Empty;
        UnPaid = 0;
        NewSeikyuGaku = 0;
        NewAdjustFutan = 0;
        KaDisplay = string.Empty;
        HokenPatternName = string.Empty;
        IsSeikyuRow = false;
        SortNo = 0;
        SeqNo = 0;
        SeikyuDetail = string.Empty;
        SeikyuAdjustFutan = 0;
        IsDelete = false;
        NewSeikyuDetail = string.Empty;
        NewSeikyuTensu = 0;
        CardError = false;
        ErrorCode = string.Empty;
        UserMessage = string.Empty;
    }

    //入金区分設定
    public void SetNyukinKbn(int nyukinKbn)
    {
        NyukinKbn = nyukinKbn;
    }
    public int HpId { get; private set; }

    public long PtId { get; private set; }

    public int SeikyuSinDate { get; private set; }

    public int Month { get; private set; }

    public long RaiinNo { get; private set; }

    public int HokenPid { get; private set; }

    public long OyaRaiinNo { get; private set; }

    public int NyukinKbn { get; private set; }

    public int SeikyuTensu { get; private set; }

    public int SeikyuGaku { get; private set; }

    public int AdjustFutan { get; private set; }

    public int NyukinGaku { get; private set; }

    public int PaymentMethodCd { get; private set; }

    public int NyukinDate { get; private set; }

    public int UketukeSbt { get; private set; }

    public string NyukinCmt { get; private set; }

    public int UnPaid { get; private set; }

    public int NewSeikyuGaku { get; private set; }

    public int NewAdjustFutan { get; private set; }

    public string KaDisplay { get; private set; }

    public string HokenPatternName { get; private set; }

    public bool IsSeikyuRow { get; private set; }

    public int SortNo { get; private set; }

    public long SeqNo { get; private set; }

    public string SeikyuDetail { get; private set; }

    public int RaiinInfStatus { get; private set; }

    public int SeikyuAdjustFutan { get; private set; }

    public bool IsDelete { get; private set; }

    public string NewSeikyuDetail { get; private set; }

    public int NewSeikyuTensu { get; private set; }

    public bool CardError { get; set; }

    public string ErrorCode { get; set; }

    public string UserMessage { get; set; }
}
