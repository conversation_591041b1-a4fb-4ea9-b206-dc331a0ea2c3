﻿using Domain.Common;
using Domain.Models.AccountDue;
using Domain.Models.Accounting;
using Domain.Models.Diseases;
using Domain.Models.Fincode;
using Domain.Models.Insurance;
using Domain.Models.MstItem;
using Domain.Models.Reception;
using Domain.Models.ReceptionSameVisit;

namespace Domain.Models.Accounting;

public interface IAccountingRepository : IRepositoryBase
{
    List<SyunoSeikyuModel> GetListSyunoSeikyu(int hpId, long ptId, int sinDate, List<long> listRaiinNo, bool getAll = false);

    List<ReceptionDto> GetListRaiinInf(int hpId, long ptId, int sinDate, long raiinNo, bool isGetHeader = false, bool getAll = true);

    List<HokenPatternModel> FindPtHokenPatternList(int hpId, long ptId, int sinDay, List<int> listPatternId);

    List<HokenPatternModel> FindPtHokenPatternList(int hpId, long ptId, int sinDay, bool isGetDeleted = false);

    List<CalcLogModel> GetCalcLog(int hpId, long ptId, int sinDate, List<long> raiinNoList);

    List<PtDiseaseModel> GetPtByoMeiList(int hpId, long ptId, int sinDate = 0);

    List<PaymentMethodMstModel> GetListPaymentMethodMst(int hpId);

    List<KohiInfModel> GetListKohiByKohiId(int hpId, long ptId, int sinDate, List<int> kohiIds);

    RaiinInfOyaModel? GetRaiinInfModel(int hpId, long raiinNo);

    List<SyunoSeikyuModel> GetListSyunoSeikyuModel(int hpId, long raiinNo);

    List<SyunoNyukinModel> GetListSyunoNyukinModel(int hpId, long raiinNo);

    public AccountingModel SaveAccounting(int hpId, long ptId, int userId, long raiinNo, string kaikeiTime, AccountingModel accountingModel, long? paymentClinicDetailId, int nyukinGaku, int adjustFutan, int nyukinStatus);

    int GetRaiinInfOyaCnt(int hpId, long oyaRaiinNo);

    bool CheckRaiinInfExist(int hpId, long ptId, long raiinNo);

    List<long> GetRaiinNos(int hpId, long ptId, long raiinNo, bool getAll = true);

    void CheckOrdInfInOutDrug(int hpId, long ptId, List<long> raiinNos, out bool inDrugExist, out bool outDrugExist);

    List<JihiSbtMstModel> GetListJihiSbtMst(int hpId);

    int GetJihiOuttaxPoint(int hpId, long ptId, List<long> raiinNos);

    byte CheckIsOpenAccounting(int hpId, long ptId, int sinDate, long raiinNo);

    bool CheckSyunoStatus(int hpId, long raiinNo, long ptId);

    ReceptionDto GetRaiinInfModel(int hpId, long ptId, int sinDate, long raiinNo, List<KaikeiInfModel> kaikeis);

    List<AccountingFormMstModel> GetAccountingFormMstModels(int hpId);

    void UpdateAccountingFormMst(int userId, List<AccountingFormMstModel> models);

    List<HokenInfModel> GetListHokenSelect(int hpId, List<KaikeiInfModel> listKaikeiInf, long ptId);

    void UpdateStatusRaiinInf(int hpId, long ptId, long raiinNo, int userId, string kaikeiTime, bool isFCO = false);

    void UpdateStatusSyunoSeikyu(int hpId, long ptId, long raiinNo, int nyuKinKbn, int userId);

    void CreateSyunoNyukin(SyunoSeikyuModel item, int sortNo, int nyukinDate, int outAdjustFutan, int outNyukinGaku, int payType, string comment, int userId);

    int SaveChanges();

    public HokenPatternModel FindPtHokenPatternById(int hpId, long ptId, int sinDay, int patternId = 0, long raiinNo = 0, bool isGetDeleted = false);
}
