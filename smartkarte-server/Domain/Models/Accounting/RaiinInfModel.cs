using Domain.Models.Accounting;
using Helper.Enum;
using System.Text.Json.Serialization;

namespace Domain.Models.AccountDue;

public class RaiinInfOyaModel
{
    public RaiinInfOyaModel(int hpId, long ptId, int sinDate, long raiinNo, long oyaRaiinNo, ReserveDetailModel? reserveDetail)
    {
        HpId = hpId;
        PtId = ptId;
        SinDate = sinDate;
        RaiinNo = raiinNo;
        OyaRaiinNo = oyaRaiinNo;
        ReserveDetail = reserveDetail;
    }

    public int HpId { get; private set; }
    public long PtId { get; private set; } 
    public int SinDate { get; private set; }
    public long RaiinNo{ get; private set; }
    public long OyaRaiinNo { get; private set; }
    public ReserveDetailModel? ReserveDetail { get; private set; }

    //オンライン診療チェック
    public bool IsOnlineTreatment()
    {
        return ReserveDetail != null && ReserveDetail.ReserveType != null && ReserveDetail.ReserveType == (int)ReserveTypeEnum.Online;
    }




    /// <summary>
    /// 状態
    ///		0:予約
    ///		1:受付
    ///		3:一時保存
    ///		5:計算
    ///		7:精算待ち
    ///		9:精算済
    /// </summary>
    public int Status { get; private set; }



}
