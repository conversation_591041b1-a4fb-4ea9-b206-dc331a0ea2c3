﻿namespace Domain.Models.Family;

public class PtFamilyRekiModel
{
    public PtFamilyRekiModel()
    {
        
    }
    public PtFamilyRekiModel(long id, string byomeiCd, string byomei, string cmt, int sortNo, bool isDeleted)
    {
        Id = id;
        ByomeiCd = byomeiCd;
        Byomei = byomei;
        Cmt = cmt;
        SortNo = sortNo;
        IsDeleted = isDeleted;
    }

    public PtFamilyRekiModel(long id, string byomeiCd, string byomei, string cmt, int sortNo)
    {
        Id = id;
        ByomeiCd = byomeiCd;
        Byomei = byomei;
        Cmt = cmt;
        SortNo = sortNo;
        IsDeleted = false;
    }

    public PtFamilyRekiModel(long id, string zokugaraCd, string byomeiCd, string byomei, string cmt, int sortNo)
    {
        Id = id;
        ZokugaraCd = zokugaraCd;
        ByomeiCd = byomeiCd;
        Byomei = byomei;
        Cmt = cmt;
        SortNo = sortNo;
        IsDeleted = false;
    }

    public PtFamilyRekiModel(long id, string byomeiCd, string byomei, string cmt, int sortNo, bool isDeleted, int hpId, long ptId, long familyId, long seqNo,
        string byotaiCd, string zokugaraCd, string zokugaraElse)
    {
        Id = id;
        ByomeiCd = byomeiCd;
        Byomei = byomei;
        Cmt = cmt;
        SortNo = sortNo;
        IsDeleted = isDeleted;
        HpId = hpId;
        PtId = ptId;
        FamilyId = familyId;
        SeqNo = seqNo;
        ByotaiCd = byotaiCd;
        ZokugaraCd = zokugaraCd;
        ZokugaraElse = zokugaraElse;
    }

    public long Id { get; set; }

    public int HpId { get; private set; }

    public long PtId { get; private set; }

    public long FamilyId { get; private set; }

    public long SeqNo { get; private set; }

    public int SortNo { get; private set; }

    public string ByomeiCd { get; private set; }

    public string Byomei { get; private set; }

    public string Cmt { get; private set; }

    public bool IsDeleted { get; private set; }

    public string ZokugaraCd { get; private set; }

    public string ZokugaraElse { get; private set; }

    public string ByotaiCd { get; private set; }
}
