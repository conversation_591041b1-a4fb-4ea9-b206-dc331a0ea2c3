﻿using Helper.Common;

namespace Domain.Models.Family;

public class FamilyModel
{
    public FamilyModel(long familyId, long ptId, long seqNo, string zokugaraCd, long familyPtId, long familyPtNum, string name, string kanaName, int sex, int birthday, int age, int isDead, int isSeparated, string biko, int sortNo, List<PtFamilyRekiModel> listPtFamilyRekis, string diseaseName, string otherZokugara = "", bool isDeleted = false)
    {
        FamilyId = familyId;
        SeqNo = seqNo;
        ZokugaraCd = zokugaraCd;
        FamilyPtId = familyPtId;
        FamilyPtNum = familyPtNum;
        Name = name;
        KanaName = kanaName;
        Sex = sex;
        Birthday = birthday;
        Age = age;
        IsDead = isDead;
        IsSeparated = isSeparated;
        Biko = biko;
        SortNo = sortNo;
        ListPtFamilyRekis = listPtFamilyRekis;
        PtId = ptId;
        IsDeleted = false;
        DiseaseName = diseaseName;
        OtherZokugara = otherZokugara;
        IsDeleted = isDeleted;
    }

    public FamilyModel(long familyId, long ptId, string zokugaraCd, long familyPtId)
    {
        FamilyId = familyId;
        ZokugaraCd = zokugaraCd;
        FamilyPtId = familyPtId;
        SeqNo = 0;
        PtId = ptId;
        FamilyPtNum = 0;
        Name = string.Empty;
        KanaName = string.Empty;
        Sex = 0;
        Birthday = 0;
        Age = 0;
        IsDead = 0;
        IsSeparated = 0;
        Biko = string.Empty;
        SortNo = 0;
        ListPtFamilyRekis = new();
        IsDeleted = false;
        DiseaseName = string.Empty;
        OtherZokugara = string.Empty;
    }

    public FamilyModel()
    {
        ZokugaraCd = string.Empty;
        Name = string.Empty;
        KanaName = string.Empty;
        Biko = string.Empty;
        ListPtFamilyRekis = new();
        DiseaseName = string.Empty;
        OtherZokugara = string.Empty;
    }

    public FamilyModel(long ptId, long ptNum, string name, string kanaName, int sex, int birthday, int isDead, int sinDate)
    {
        FamilyId = 0;
        ZokugaraCd = string.Empty;
        FamilyPtId = 0;
        SeqNo = 0;
        PtId = ptId;
        FamilyPtNum = ptNum;
        Name = name;
        KanaName = kanaName;
        Sex = sex;
        Birthday = birthday;
        Age = CIUtil.SDateToAge(birthday, sinDate);
        IsDead = isDead;
        IsSeparated = 0;
        Biko = string.Empty;
        SortNo = 0;
        ListPtFamilyRekis = new();
        IsDeleted = false;
        DiseaseName = string.Empty;
        OtherZokugara = string.Empty;
    }

    public FamilyModel(long familyId, long ptId, string zokugaraCd, long familyPtId, string name, string kanaName, int sex, int birthday, int isDead, int isSeparated, string biko, int sortNo, bool isDeleted, List<PtFamilyRekiModel> listPtFamilyRekis)
    {
        FamilyId = familyId;
        PtId = ptId;
        SeqNo = 0;
        ZokugaraCd = zokugaraCd;
        FamilyPtId = familyPtId;
        FamilyPtNum = 0;
        Name = name;
        KanaName = kanaName;
        Sex = sex;
        Birthday = birthday;
        Age = 0;
        IsDead = isDead;
        IsSeparated = isSeparated;
        Biko = biko;
        SortNo = sortNo;
        ListPtFamilyRekis = listPtFamilyRekis;
        IsDeleted = isDeleted;
        DiseaseName = string.Empty;
        OtherZokugara = string.Empty;
    }

    public FamilyModel(long familyId, long ptId, string name, string kanaName, string zokugaraCd, string biko, string otherZokugara, string diseaseName, int sortNo, long seqNo, bool isDeleted)
    {
        FamilyId = familyId;
        PtId = ptId;
        ZokugaraCd = zokugaraCd;
        Biko = biko;
        OtherZokugara = otherZokugara;
        DiseaseName = diseaseName;
        SortNo = sortNo;
        SeqNo = seqNo;
        IsDeleted = isDeleted;
        Name = name;
        ListPtFamilyRekis = new();
        KanaName = kanaName;
    }

    public long FamilyId { get; private set; }

    public long PtId { get; private set; }

    public long SeqNo { get; private set; }

    public string ZokugaraCd { get; private set; }

    public long FamilyPtId { get; private set; }

    public long FamilyPtNum { get; private set; }

    public string Name { get; private set; }

    public string KanaName { get; private set; }

    public int Sex { get; private set; }

    public int Birthday { get; private set; }

    public int Age { get; private set; }

    public int IsDead { get; private set; }

    public int IsSeparated { get; private set; }

    public string Biko { get; private set; }

    public int SortNo { get; private set; }

    public List<PtFamilyRekiModel> ListPtFamilyRekis { get; private set; }

    public bool IsDeleted { get; private set; }

    public string DiseaseName { get; private set; }

    public string OtherZokugara { get; private set; } = string.Empty;
}
