﻿namespace Domain.Models.Document;

public class DocCommentModel
{
    public DocCommentModel(int categoryId, string categoryName, string replaceWord, List<DocCommentDetailModel> listDocCommentDetails, int hpId)
    {
        CategoryId = categoryId;
        CategoryName = categoryName;
        ReplaceWord = replaceWord;
        ListDocCommentDetails = listDocCommentDetails;
        HpId = hpId;
    }

    public int CategoryId { get; private set; }

    public string CategoryName { get; private set; }

    public string ReplaceWord { get; private set; }

    public List<DocCommentDetailModel> ListDocCommentDetails { get; private set; }

    public int HpId { get; private set; }
}
