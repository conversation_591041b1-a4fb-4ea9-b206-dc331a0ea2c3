﻿namespace Domain.Models.EpsDispensing;

public class EpsDispensingModel
{
    public EpsDispensingModel()
    {

    }

    public EpsDispensingModel(string hokensyaNo, string bango, string kigo, string edaNo, string kohiFutansyaNo, string kohiJyukyusyaNo, int dispensingTimes, int resultType, string receptionPharmacyName, int dispensingDate, DateTime epsUpdateDateTime, int messageFlg)
    {
        HokensyaNo = hokensyaNo;
        Bango = bango;
        Kigo = kigo;
        EdaNo = edaNo;
        KohiFutansyaNo = kohiFutansyaNo;
        KohiJyukyusyaNo = kohiJyukyusyaNo;
        DispensingTimes = dispensingTimes;
        ResultType = resultType;
        ReceptionPharmacyName = receptionPharmacyName;
        DispensingDate = dispensingDate;
        EpsUpdateDateTime = epsUpdateDateTime;
        MessageFlg = messageFlg;
    }
    
    public EpsDispensingModel(string hokensyaNo, string bango, string kigo, string edaNo, string kohiFutansyaNo, string kohiJyukyusyaNo, int dispensingTimes, int resultType, string receptionPharmacyName, int dispensingDate, DateTime epsUpdateDateTime, int messageFlg, int isDeleted)
    {
        HokensyaNo = hokensyaNo;
        Bango = bango;
        Kigo = kigo;
        EdaNo = edaNo;
        KohiFutansyaNo = kohiFutansyaNo;
        KohiJyukyusyaNo = kohiJyukyusyaNo;
        DispensingTimes = dispensingTimes;
        ResultType = resultType;
        ReceptionPharmacyName = receptionPharmacyName;
        DispensingDate = dispensingDate;
        EpsUpdateDateTime = epsUpdateDateTime;
        MessageFlg = messageFlg;
        IsDeleted = isDeleted;
    }

    /// <summary>
    /// ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 医療機関識別ID
    /// </summary>
    public int HpId { get; set; }

    /// <summary>
    /// 処方箋ID
    /// </summary>
    public string PrescriptionId { get; set; } = string.Empty;

    /// <summary>
    /// 薬局調剤結果更新日時
    /// </summary>
    public DateTime EpsUpdateDateTime { get; set; }

    /// <summary>
    /// 調剤結果ID
    /// </summary>
    public string DispensingResultId { get; set; } = string.Empty;

    /// <summary>
    /// 患者ID
    /// </summary>
    public long PtId { get; set; }

    /// <summary>
    /// 保険者番号
    ///     保険が公費単独の場合は空
    /// </summary>
    public string HokensyaNo { get; set; } = string.Empty;

    /// <summary>
    /// 被保険者記号
    ///     保険が公費単独の場合は空
    /// </summary>
    public string Kigo { get; set; } = string.Empty;

    /// <summary>
    /// 被保険者番号
    ///     保険が公費単独の場合は空
    /// </summary>
    public string Bango { get; set; } = string.Empty;

    /// <summary>
    /// 被保険者枝番
    ///     保険が公費単独の場合は空
    /// </summary>
    public string EdaNo { get; set; } = string.Empty;

    /// <summary>
    /// 公費負担者番号
    ///     保険が公費単独以外の場合は空
    /// </summary>
    public string KohiFutansyaNo { get; set; } = string.Empty;

    /// <summary>
    /// 公費受給者番号
    ///     保険が公費単独以外の場合は空
    /// </summary>
    public string KohiJyukyusyaNo { get; set; } = string.Empty;

    /// <summary>
    /// 調剤結果種別
    ///     1:調剤結果（有効）
    ///     2:調剤結果（無効）
    ///     3:処方箋回収
    ///     4:調剤中
    /// </summary>
    public int ResultType { get; set; }

    /// <summary>
    /// 薬局名
    /// </summary>
    public string ReceptionPharmacyName { get; set; } = string.Empty;

    /// <summary>
    /// 調剤日
    /// </summary>
    public int DispensingDate { get; set; }

    /// <summary>
    /// リフィル処方箋の調剤回数
    /// </summary>
    public int DispensingTimes { get; set; }

    /// <summary>
    /// 調剤結果
    ///     base64エンコードされた調剤結果CSV
    /// </summary>
    public string DispensingDocument { get; set; } = string.Empty;

    /// <summary>
    /// 伝達事項フラグ
    ///     1:伝達事項なし
    ///     2:伝達事項あり
    ///     3:確認済み
    /// </summary>
    public int MessageFlg { get; set; }

    /// <summary>
    /// 処方箋回収理由
    /// </summary>
    public string CancelReason { get; set; } = string.Empty;

    /// <summary>
    /// 削除フラグ
    ///     1: 削除
    /// </summary>
    public int IsDeleted { get; set; }

    /// <summary>
    /// 作成日時
    /// </summary>
    public DateTime CreateDate { get; set; }

    /// <summary>
    /// 作成ID
    /// </summary>
    public int CreateId { get; set; }

    /// <summary>
    /// 作成端末
    /// </summary>
    public string CreateMachine { get; set; } = string.Empty;

    /// <summary>
    /// 更新日時
    /// </summary>
    public DateTime UpdateDate { get; set; }

    /// <summary>
    /// 更新ID
    /// </summary>
    public int UpdateId { get; set; }

    /// <summary>
    /// 更新端末
    /// </summary>
    public string UpdateMachine { get; set; } = string.Empty;
}