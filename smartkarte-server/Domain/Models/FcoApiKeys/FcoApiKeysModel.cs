namespace Domain.Models.FcoApiKey;

public class FcoApiKeyModel
{
    public long FcoApiKeyId { get; private set; }
    public string ApiKey { get; private set; }
    public string SecretKey { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime UpdatedAt { get; private set; }
    public bool IsDeleted { get; private set; }
    public DateTime? DeletedAt { get; private set; }
    public int HpId { get; private set; }
    public string Label { get; private set; }

    // デフォルトコンストラクタ
    public FcoApiKeyModel()
    {
        FcoApiKeyId = 0;
        ApiKey = string.Empty;
        SecretKey = string.Empty;
        CreatedAt = DateTime.Now;
        UpdatedAt = DateTime.Now;
        IsDeleted = false;
        HpId = 0;
        Label = string.Empty;
    }

    public FcoApiKeyModel(long fcoApiKeyId, int hpId, string apiKey, string secretKey, DateTime createdAt, DateTime updatedAt, bool isDeleted, DateTime? deletedAt, string label)
    {
        FcoApiKeyId = fcoApiKeyId;
        HpId = hpId;
        ApiKey = apiKey;
        SecretKey = secretKey;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
        IsDeleted = isDeleted;
        DeletedAt = deletedAt;
        Label = label;
    }
}

