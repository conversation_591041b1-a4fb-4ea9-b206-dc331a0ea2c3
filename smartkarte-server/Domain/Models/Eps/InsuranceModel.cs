using Domain.Models.Eps.CheckErrorPreRegistration;

namespace Domain.Models.Eps;

public class InsurancePtHokenInfModel
{
    public InsurancePtHokenInfModel(int hpId, long ptId, int hokenId, long seqNo, int hokenNo, string? edaNo, int hokenEdaNo, string? hokensyaNo, string? kigo, string? bango, int honkeKbn, int hokenKbn, string? houbetu, string? hokensyaName, string? hokensyaPost, string? hokensyaAddress, string? hokensyaTel, int keizokuKbn, int sikakuDate, int kofuDate, int startDate, int endDate, int rate, int gendogaku, int kogakuKbn, int kogakuType, int tokureiYm1, int tokureiYm2, int tasukaiYm, int syokumuKbn, int genmenKbn, int genmenRate, int genmenGaku, string? tokki1, string? tokki2, string? tokki3, string? tokki4, string? tokki5, string? rousaiKofuNo, int rousaiSaigaiKbn, string? rousaiJigyosyoName, string? rousaiPrefName, string? rousaiCityName, int rousaiSyobyoDate, string? rousaiSyobyoCd, string? rousaiRoudouCd, string? rousaiKantokuCd, int rousaiReceCount, int ryoyoStartDate, int ryoyoEndDate, string? jibaiHokenName, string? jibaiHokenTanto, string? jibaiHokenTel, int jibaiJyusyouDate, int isDeleted, DateTime createDate, int createId, string? createMachine, DateTime updateDate, int updateId, string? updateMachine, string? insuredName)
    {
        HpId = hpId;
        PtId = ptId;
        HokenId = hokenId;
        SeqNo = seqNo;
        HokenNo = hokenNo;
        EdaNo = edaNo;
        HokenEdaNo = hokenEdaNo;
        HokensyaNo = hokensyaNo;
        Kigo = kigo;
        Bango = bango;
        HonkeKbn = honkeKbn;
        HokenKbn = hokenKbn;
        Houbetu = houbetu;
        HokensyaName = hokensyaName;
        HokensyaPost = hokensyaPost;
        HokensyaAddress = hokensyaAddress;
        HokensyaTel = hokensyaTel;
        KeizokuKbn = keizokuKbn;
        SikakuDate = sikakuDate;
        KofuDate = kofuDate;
        StartDate = startDate;
        EndDate = endDate;
        Rate = rate;
        Gendogaku = gendogaku;
        KogakuKbn = kogakuKbn;
        KogakuType = kogakuType;
        TokureiYm1 = tokureiYm1;
        TokureiYm2 = tokureiYm2;
        TasukaiYm = tasukaiYm;
        SyokumuKbn = syokumuKbn;
        GenmenKbn = genmenKbn;
        GenmenRate = genmenRate;
        GenmenGaku = genmenGaku;
        Tokki1 = tokki1;
        Tokki2 = tokki2;
        Tokki3 = tokki3;
        Tokki4 = tokki4;
        Tokki5 = tokki5;
        RousaiKofuNo = rousaiKofuNo;
        RousaiSaigaiKbn = rousaiSaigaiKbn;
        RousaiJigyosyoName = rousaiJigyosyoName;
        RousaiPrefName = rousaiPrefName;
        RousaiCityName = rousaiCityName;
        RousaiSyobyoDate = rousaiSyobyoDate;
        RousaiSyobyoCd = rousaiSyobyoCd;
        RousaiRoudouCd = rousaiRoudouCd;
        RousaiKantokuCd = rousaiKantokuCd;
        RousaiReceCount = rousaiReceCount;
        RyoyoStartDate = ryoyoStartDate;
        RyoyoEndDate = ryoyoEndDate;
        JibaiHokenName = jibaiHokenName;
        JibaiHokenTanto = jibaiHokenTanto;
        JibaiHokenTel = jibaiHokenTel;
        JibaiJyusyouDate = jibaiJyusyouDate;
        IsDeleted = isDeleted;
        CreateDate = createDate;
        CreateId = createId;
        CreateMachine = createMachine;
        UpdateDate = updateDate;
        UpdateId = updateId;
        UpdateMachine = updateMachine;
        InsuredName = insuredName;
    }

    public int HpId { get; set; }
    public long PtId { get; set; }
    public int HokenId { get; set; }
    public long SeqNo { get; set; }
    public int HokenNo { get; set; }
    public string? EdaNo { get; set; }
    public int HokenEdaNo { get; set; }
    public string? HokensyaNo { get; set; }
    public string? Kigo { get; set; }
    public string? Bango { get; set; }
    public int HonkeKbn { get; set; }
    public int HokenKbn { get; set; }
    public string? Houbetu { get; set; }
    public string? HokensyaName { get; set; }
    public string? HokensyaPost { get; set; }
    public string? HokensyaAddress { get; set; }
    public string? HokensyaTel { get; set; }
    public int KeizokuKbn { get; set; }
    public int SikakuDate { get; set; }
    public int KofuDate { get; set; }
    public int StartDate { get; set; }
    public int EndDate { get; set; }
    public int Rate { get; set; }
    public int Gendogaku { get; set; }
    public int KogakuKbn { get; set; }
    public int KogakuType { get; set; }
    public int TokureiYm1 { get; set; }
    public int TokureiYm2 { get; set; }
    public int TasukaiYm { get; set; }
    public int SyokumuKbn { get; set; }
    public int GenmenKbn { get; set; }
    public int GenmenRate { get; set; }
    public int GenmenGaku { get; set; }
    public string? Tokki1 { get; set; }
    public string? Tokki2 { get; set; }
    public string? Tokki3 { get; set; }
    public string? Tokki4 { get; set; }
    public string? Tokki5 { get; set; }
    public string? RousaiKofuNo { get; set; }
    public int RousaiSaigaiKbn { get; set; }
    public string? RousaiJigyosyoName { get; set; }
    public string? RousaiPrefName { get; set; }
    public string? RousaiCityName { get; set; }
    public int RousaiSyobyoDate { get; set; }
    public string? RousaiSyobyoCd { get; set; }
    public string? RousaiRoudouCd { get; set; }
    public string? RousaiKantokuCd { get; set; }
    public int RousaiReceCount { get; set; }
    public int RyoyoStartDate { get; set; }
    public int RyoyoEndDate { get; set; }
    public string? JibaiHokenName { get; set; }
    public string? JibaiHokenTanto { get; set; }
    public string? JibaiHokenTel { get; set; }
    public int JibaiJyusyouDate { get; set; }
    public int IsDeleted { get; set; }
    public DateTime CreateDate { get; set; }
    public int CreateId { get; set; }
    public string? CreateMachine { get; set; }
    public DateTime UpdateDate { get; set; }
    public int UpdateId { get; set; }
    public string? UpdateMachine { get; set; }
    public string? InsuredName { get; set; }
}

public class InsurancePtKohiModel
{
    public InsurancePtKohiModel(int hpId, long ptId, int hokenId, long seqNo, int prefNo, int hokenNo, int hokenEdaNo, string futansyaNo, string jyukyusyaNo, string tokusyuNo, int sikakuDate, int kofuDate, int startDate, int endDate, int rate, int gendoGaku, int isDeleted, DateTime createDate, int createId, string createMachine, DateTime updateDate, int updateId, string updateMachine, int hokenSbtKbn, string houbetu, int birthday)
    {
        HpId = hpId;
        PtId = ptId;
        HokenId = hokenId;
        SeqNo = seqNo;
        PrefNo = prefNo;
        HokenNo = hokenNo;
        HokenEdaNo = hokenEdaNo;
        FutansyaNo = futansyaNo;
        JyukyusyaNo = jyukyusyaNo;
        TokusyuNo = tokusyuNo;
        SikakuDate = sikakuDate;
        KofuDate = kofuDate;
        StartDate = startDate;
        EndDate = endDate;
        Rate = rate;
        GendoGaku = gendoGaku;
        IsDeleted = isDeleted;
        CreateDate = createDate;
        CreateId = createId;
        CreateMachine = createMachine;
        UpdateDate = updateDate;
        UpdateId = updateId;
        UpdateMachine = updateMachine;
        HokenSbtKbn = hokenSbtKbn;
        Houbetu = houbetu;
        Birthday = birthday;
    }

    public int HpId { get; private set; }

    public long PtId { get; private set; }

    public int HokenId { get; private set; }

    public long SeqNo { get; private set; }

    public int PrefNo { get; private set; }
    public int HokenNo { get; private set; }
    public int HokenEdaNo { get; private set; }
    public string FutansyaNo { get; private set; }
    public string JyukyusyaNo { get; private set; }
    public string TokusyuNo { get; private set; }
    public int SikakuDate { get; private set; }
    public int KofuDate { get; private set; }
    public int StartDate { get; private set; }

    public int EndDate { get; private set; } 
    public int Rate { get; private set; }
    public int GendoGaku { get; private set; }
    public int IsDeleted { get; private set; }
    public DateTime CreateDate { get; private set; }
    public int CreateId { get; private set; }
    public string CreateMachine { get; private set; }
    public DateTime UpdateDate { get; private set; }
    public int UpdateId { get; private set; }
    public string UpdateMachine { get; private set; }
    public int HokenSbtKbn { get; private set; }
    public string Houbetu { get; private set; }
    public int Birthday { get; private set; }

}
public class InsurancePtHokenPatternModel
{
    public InsurancePtHokenPatternModel(long ptId, int hokenPid, long seqNo, int hokenKbn, int hokenSbtCd, int hokenId, int kohi1Id, int kohi2Id, int kohi3Id, int kohi4Id, string hokenMemo, int startDate, int endDate, InsurancePtHokenInfModel? ptHokenInf, InsurancePtKohiModel? ptKohi1, InsurancePtKohiModel? ptKohi2, InsurancePtKohiModel? ptKohi3, InsurancePtKohiModel? ptKohi4)
    {
        PtId = ptId;
        HokenPid = hokenPid;
        SeqNo = seqNo;
        HokenKbn = hokenKbn;
        HokenSbtCd = hokenSbtCd;
        HokenId = hokenId;
        Kohi1Id = kohi1Id;
        Kohi2Id = kohi2Id;
        Kohi3Id = kohi3Id;
        Kohi4Id = kohi4Id;
        HokenMemo = hokenMemo;
        StartDate = startDate;
        EndDate = endDate;
        PtHokenInf = ptHokenInf;
        PtKohi1 = ptKohi1;
        PtKohi2 = ptKohi2;
        PtKohi3 = ptKohi3;
        PtKohi4 = ptKohi4;
    }

    public long PtId { get; private set; }

    public int HokenPid { get; private set; }

    public long SeqNo { get; private set; }

    public int HokenKbn { get; private set; }

    public int HokenSbtCd { get; private set; }

    public int HokenId { get; private set; }

    public int Kohi1Id { get; private set; }

    public int Kohi2Id { get; private set; }

    public int Kohi3Id { get; private set; }

    public int Kohi4Id { get; private set; }

    public string HokenMemo { get; private set; }

    public int StartDate { get; private set; }

    public int EndDate { get; private set; }

    public InsurancePtHokenInfModel? PtHokenInf { get; set; }
    public InsurancePtKohiModel? PtKohi1 { get; set; } 

    public InsurancePtKohiModel? PtKohi2 { get; set; }
    public InsurancePtKohiModel? PtKohi3 { get; set; }
    public InsurancePtKohiModel? PtKohi4 { get; set; }
}

public class OnlineConfirmationModel
{
    public OnlineConfirmationModel()
    {
        ReceptionNo = string.Empty;
    }
    public OnlineConfirmationModel(string receptionNo, DateTime receptionDateTime, DateTime processTime, int yoyakuDate, int sin_ym, int consentFrom, int consentTo, int examinationFrom, int examinationTo, int batchConfirmationType)
    {
        ReceptionNo = receptionNo;
        ReceptionDateTime = receptionDateTime;
        ProcessTime = processTime;
        YoyakuDate = yoyakuDate;
        ExaminationFrom = examinationFrom;
        ExaminationTo = examinationTo;
        SinYm = sin_ym;
        ConsentFrom = consentFrom;
        ConsentTo = consentTo;
        BatchConfirmationType = batchConfirmationType;
    }

    public string ReceptionNo { get; private set; } 
    public DateTime ReceptionDateTime { get; private set; }
    public int YoyakuDate { get; private set; }
    public string? SegmentOfResult { get; private set; }
    public string? ErrorMessage { get; private set; }     
    public int ConfirmationType { get; private set; }
    public int ExaminationTo { get; private set; }
    public int SinYm { get; private set; }
    public int ConsentFrom { get; private set; }
    public int ConsentTo { get; private set; }
    public int ExaminationFrom { get; private set; }
    public int BatchConfirmationType { get; private set; }
    public DateTime? ProcessTime { get; private set; }
}

public class InsuranceModel
{
    public RaiinInfModel? RaiinInf { get; set; }
    public List<InsurancePtHokenPatternModel> PtHokenPatterns { get; set; }
    public bool WasConfirmedOnline { get; set; }

    public InsuranceModel()
    {
        RaiinInf = null;
        PtHokenPatterns = new List<InsurancePtHokenPatternModel>();
        WasConfirmedOnline = false;
    }

    public InsuranceModel(RaiinInfModel raiinInf)
    {
        RaiinInf = raiinInf;
        PtHokenPatterns = new List<InsurancePtHokenPatternModel>();
        WasConfirmedOnline = false;
    }

    public InsuranceModel(RaiinInfModel raiinInf, List<InsurancePtHokenPatternModel> ptHokenPatterns, bool wasConfirmedOnline)
    {
        RaiinInf = raiinInf;
        PtHokenPatterns = ptHokenPatterns;
        WasConfirmedOnline = wasConfirmedOnline;
    }
}