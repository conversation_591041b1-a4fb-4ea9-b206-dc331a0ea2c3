using Domain.Models.EpsPrescription;
using Domain.Models.EpsReference;

namespace Domain.Models.Eps;

public class RaiinInfModel
{
    public int HpId { get; set; }
    public long PtId { get; set; }
    public int SinDate { get; set; }
    public long RaiinNo { get; set; }
    public int HokenPid { get; set; }
    public int PrintEpsReference { get; set; }
    public int PrescriptionIssueSelect { get; set; }

    public RaiinInfModel(int hpId, long ptId, int sinDate, long raiinNo, int hokenPid, int printEpsReference, int prescriptionIssueSelect)
    {
        HpId = hpId;
        PtId = ptId;
        SinDate = sinDate;
        RaiinNo = raiinNo;
        HokenPid = hokenPid;
        PrintEpsReference = printEpsReference;
        PrescriptionIssueSelect = prescriptionIssueSelect;
    }
}

public class PtHokenPtn
{
    public PtHokenPtn(long ptId, int hokenPid, long seqNo, int hokenKbn, int hokenSbtCd, int hokenId, int kohi1Id, int kohi2Id, int kohi3Id, int kohi4Id, string hokenMemo, int startDate, int endDate)
    {
        PtId = ptId;
        HokenPid = hokenPid;
        SeqNo = seqNo;
        HokenKbn = hokenKbn;
        HokenSbtCd = hokenSbtCd;
        HokenId = hokenId;
        Kohi1Id = kohi1Id;
        Kohi2Id = kohi2Id;
        Kohi3Id = kohi3Id;
        Kohi4Id = kohi4Id;
        HokenMemo = hokenMemo;
        StartDate = startDate;
        EndDate = endDate;
    }

    public long PtId { get; private set; }

    public int HokenPid { get; private set; }

    public long SeqNo { get; private set; }

    public int HokenKbn { get; private set; }

    public int HokenSbtCd { get; private set; }

    public int HokenId { get; private set; }

    public int Kohi1Id { get; private set; }

    public int Kohi2Id { get; private set; }

    public int Kohi3Id { get; private set; }

    public int Kohi4Id { get; private set; }

    public string HokenMemo { get; private set; }

    public int StartDate { get; private set; }

    public int EndDate { get; private set; }
}


public class ValidateBeforePrintingModel
{
    public RaiinInfModel? RaiinInf { get; set; }
    public List<EpsPrescriptionModel> EpsPrescriptions { get; set; }
    public List<EpsReferenceModel> EpsReferences { get; set; }
    public List<PtHokenPtn> PtHokenPatterns { get; set; }

    public ValidateBeforePrintingModel()
    {
        RaiinInf = null;
        EpsPrescriptions = new List<EpsPrescriptionModel>();
        EpsReferences = new List<EpsReferenceModel>();
        PtHokenPatterns = new List<PtHokenPtn>();
    }

    public ValidateBeforePrintingModel(RaiinInfModel raiinInf)
    {
        RaiinInf = raiinInf;
        EpsPrescriptions = new List<EpsPrescriptionModel>();
        EpsReferences = new List<EpsReferenceModel>();
        PtHokenPatterns = new List<PtHokenPtn>();
    }

    public ValidateBeforePrintingModel(RaiinInfModel raiinInf, List<EpsPrescriptionModel> epsPrescriptions, List<EpsReferenceModel> epsReferences, List<PtHokenPtn> ptHokenPatterns)
    {
        RaiinInf = raiinInf;
        EpsPrescriptions = epsPrescriptions;
        EpsReferences = epsReferences;
        PtHokenPatterns = ptHokenPatterns;
    }
}