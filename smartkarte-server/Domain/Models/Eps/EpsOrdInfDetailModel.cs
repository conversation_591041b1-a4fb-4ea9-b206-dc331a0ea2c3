namespace Domain.Models.Eps;

public class EpsOrdInfDetailModel
{
    public int HpId { get; set; }

    public long RaiinNo { get; set; }

    public long RpNo { get; set; }

    public long RpEdaNo { get; set; }

    public int RowNo { get; set; }

    public long PtId { get; set; }

    public int SinDate { get; set; }

    public int SinKouiKbn { get; set; }

    public string ItemCd { get; set; }

    public string ItemName { get; set; }

    public double Suryo { get; set; }

    public string UnitName { get; set; }

    public int UnitSbt { get; set; }

    public double TermVal { get; set; }

    public int KohatuKbn { get; set; }

    public int SyohoKbn { get; set; }

    public int SyohoLimitKbn { get; set; }

    public int DrugKbn { get; set; }

    public int YohoKbn { get; set; }

    public string Kokuji1 { get; set; }

    public string Kokuji2 { get; set; }

    public int IsNodspRece { get; set; }

    public string IpnCd { get; set; }

    public string IpnName { get; set; }

    public int JissiKbn { get; set; }

    public DateTime JissiDate { get; set; }

    public int JissiId { get; set; }

    public string JissiMachine { get; set; }

    public string ReqCd { get; set; }

    public string Bunkatu { get; set; }

    public string CmtName { get; set; }

    public string CmtOpt { get; set; }

    public string FontColor { get; set; }

    public int CommentNewline { get; set; }

    public int BikoComment { get; set; }

    public string RelationItem { get; set; } = string.Empty;

    public string MasterSbt { get; set; }

    public int InOutKbn { get; set; }

    public double Yakka { get; set; }

    public bool IsGetPriceInYakka { get; set; }

    public double Ten { get; set; }

    public int BunkatuKoui { get; set; }

    public int AlternationIndex { get; set; }

    public int KensaGaichu { get; set; }

    public int RefillSetting { get; set; }

    public int CmtCol1 { get; set; }

    public double OdrTermVal { get; set; }

    public double CnvTermVal { get; set; }

    public string YjCd { get; set; }

    public int Kasan1 { get; set; }

    public int Kasan2 { get; set; }

    public string CnvUnitName { get; set; }

    public string OdrUnitName { get; set; }

    public string CenterItemCd1 { get; set; }

    public string CenterItemCd2 { get; set; }

    public bool IsDummy { get; set; }

    public int CmtColKeta1 { get; set; }

    public int CmtColKeta2 { get; set; }

    public int CmtColKeta3 { get; set; }

    public int CmtColKeta4 { get; set; }

    public int CmtCol2 { get; set; }

    public int CmtCol3 { get; set; }

    public int CmtCol4 { get; set; }

    public int HandanGrpKbn { get; set; }

    public bool IsKensaMstEmpty { get; set; }

    public int OdrKouiKbn { get; set; }

    public int BuiKbn { get; set; }

    public int IsAdopted { get; set; }

    public int SenteiRyoyoKbn { get; set; }

    public string CenterCd { get; set; } = string.Empty;

    public string CenterName { get; set; } = string.Empty;

    public int RousaiKbn { get; set; }

    public int SinYm
    {
        get
        {
            return SinDate / 100;
        }
    }

    public string KikakiUnit { get; set; }

    public string YakkaiUnit { get; set; }

    public decimal RikikaRate { get; set; }

    public string RikikaUnit { get; set; }

    public string YoukaiekiCd { get; set; }

    public string MemoItem { get; set; }

    public string CdKbn { get; set; }

    public int CdKbnNo { get; set; }

    public int CdEdaNo { get; set; }

    public int CdKouNo { get; set; }

    public bool IsGetYakka { get; set; }

    public bool IsDrugUsage
    {
        get => YohoKbn > 0 ;
    }

    public bool IsDrug
    {
        get => (SinKouiKbn == 20 && DrugKbn > 0) 
            || (SinKouiKbn == 20 && ItemCd.StartsWith("Z"));
    }

    public bool IsInjection
    {
        get => SinKouiKbn == 30 && MasterSbt != "S";
    }

    public bool IsShohoComment => SinKouiKbn == 100;

    public bool IsShohoBiko => SinKouiKbn == 101;

    public bool IsStandardUsage
    {
        get => YohoKbn == 1;
    }

    public bool IsInjectionUsage
    {
        get => (SinKouiKbn >= 31 && SinKouiKbn <= 34) || (SinKouiKbn == 30 && ItemCd.StartsWith("Z") && MasterSbt == "S");
    }

    public bool IsSuppUsage
    {
        get => YohoKbn == 2;
    }

    public bool IsNormalComment => !string.IsNullOrEmpty(ItemName) && string.IsNullOrEmpty(ItemCd);

}