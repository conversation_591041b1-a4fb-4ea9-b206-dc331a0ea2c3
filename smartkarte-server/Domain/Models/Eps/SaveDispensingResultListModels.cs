namespace Domain.Models.Eps;

public class SaveDispensingResultListModels
{
    public List<SaveDispensingResultListModelItems> SaveDispensingResultListModelItems { get; set; } 
    public long DispensingResultListStartDate { get; set; }
    public long  DispensingResultListEndDate { get; set; }

    public SaveDispensingResultListModels(List<SaveDispensingResultListModelItems> saveDispensingResultListModelItems, long dispensingResultListStartDate, long dispensingResultListEndDate)
    {
        SaveDispensingResultListModelItems = saveDispensingResultListModelItems;
        DispensingResultListStartDate = dispensingResultListStartDate;
        DispensingResultListEndDate = dispensingResultListEndDate;
    }
}