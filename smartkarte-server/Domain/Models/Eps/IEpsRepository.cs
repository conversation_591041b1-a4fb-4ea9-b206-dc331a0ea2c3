﻿using Domain.Common;
using Domain.Models.Eps;
using Domain.Models.Eps.CheckErrorPreRegistration;
using Domain.Models.EpsPrescription;
using Entity.Tenant;
using Domain.Models.EpsDispensing;
using Domain.Models.EpsReq;
using Domain.Models.EpsDispensingReq;

namespace Domain.Models.Eps
{
    public interface IEpsRepository : IRepositoryBase
    {
        PreRegistrationCheckingModel? GetPreRegistrationCheckingData(int hpId, long ptId, long raiinNo, int sinDate, List<int> statusList, List<EpsOrderInfModel> odrInfs);
        bool CreateEpsReference(int hpId, int userId, long ptId, long raiinNo, int sinDate, string prescriptionId, string prescriptionReferenceInformation);
        bool UpdatePrescriptionStatus(int hpId, long ptId, long raiinNo, long seqNo, int status, int userId, int? deletedReason = null);

        bool UpdatePrescriptionStatus(int hpId, long ptId, long raiinNo, int status, int userId, int? deletedReason = null);

        List<string> GetPrescriptionIdList(int hpId, int userId, long ptId, long raiinNo, List<int> prescriptionStatus, int? issueType, int? refillCount);
        List<SaveEpsPrescriptionInfoModel> SavePrescriptionInfo(int userId, int hpId, List<SaveEpsPrescriptionInfoModel> models);
        bool SaveDispensingResultList(int hpId, int userId, SaveDispensingResultListModels models);
        (List<EpsDispensingModel>, List<EpsPrescriptionModel>) GetDispensingInfList(int hpId, long ptId);
        List<TodayHokenOdrInfModel> GetOdrInfs(int hpId, long PtId, long raiinNo, int sinDate);

        public List<PtHokenPatternModel> FindPtHokenPatternList(int hpId, long ptId, int sinDay, bool isGetDeleted = false);
    
        PtHokenPatternModel FindPtHokenPatternById(int hpId, long ptId, int sinDay, int patternId = 0, long raiinNo = 0, bool isGetDeleted = false);
    
        ReceptionModel GetRaiinInfByRaiinNo(int hpId, long ptId, int sindate, long raiinNo);
    
        List<string> GetIpnKasanMst(int hpId, int sinDate, List<string> itemCdList);

        List<string> GetIpnKasanExclude(int hpId, int sinDate, List<string> itemCdList);
        
        PrescriptionInfListModel? GetPrescriptionInfList(int inputDataHpId, long inputDataPatientNum, int inputDataStartSinDate, int inputDataEndSinDate, int inputDataStartDispensingDate, int inputDataEndDispensingDate);
        
        bool UpdatePrescriptionStatusByIds(int inputDataHpId, long inputDataPtId, long inputDataRaiinNo, List<UpdatePrescriptionStatusModel> inputDataUpdatePrescriptionStatusModels);

        bool GetEpsDispensingByResultType(int hpId, long ptId, long raiinNo, int sinDate,  List<int> listResultType);
        
        public ValidateBeforePrintingModel ValidateBeforePrinting(int hpId, long ptId, long raiinNo, int sinDate, int userId);

        InsuranceModel GetInsuranceInf(int hpId, long ptId, long raiinNo, int sinDate);

        public EpsReqModel? UpsertEpsReq(int hpId, long ptId, long raiinNo, int sinDate, int reqDate, long dateSeqNo, string arbitraryFileIdentifier, string prescriptionId, string dispensingResultId, int reqType, int status, string resultCode, string resultMessage, string result, int userId);

        bool UpdateEpsDispensings(int inputDataHpId, List<EpsDispensingModel> inputDataEpsDispensingModels, bool notAcceptedAtPharmacy, bool preparedAtPharmacy, bool collectedOrDispensedByPharmacy, bool cancelledPrescription, int userId);

        (List<EpsPrescriptionModel>, bool) PreCheckOldPrescription(int hpId, long ptId, long raiinNo, int sinDate);
        
        YohoMst? GetYohoMst(string yohoCd, int hpId);
        
        bool updateDispensingByResponse(int inputDataHpId, List<EpsDispensingModel> inputDataEpsDispensings, List<EpsDispensingReqModel> inputDataEpsDispensingReqs);

        void ClearTrashReference();
        bool UpsertDispensingInf(int inputDataHpId, List<EpsDispensingModel> inputDataEpsDispensingModels);
        (bool UpdateHoken, bool UpdateKohi, bool PrescriptionMatched, long PtId) CheckCsvHokenInf(int inputDataHpId, string inputDataPtId, string inputDataHokensyaNo, string inputDataKigo, string inputDataBango, string inputDataEdaNo, string inputDataFutansyaNo, string inputDataJyukyusyaNo);

        (DateTime, DateTime, DateTime, DateTime) GetPrintSettingFlag(int hpId, long ptId, long raiinNo);
    }
}
