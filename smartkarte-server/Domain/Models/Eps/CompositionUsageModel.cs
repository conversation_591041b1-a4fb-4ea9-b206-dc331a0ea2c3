namespace Domain.Models.Eps;

public class CompositionUsageModel
{
    /// <summary>
    /// 剤型行
    /// </summary>
    public string DosageForm { get; set; }

    /// <summary>
    /// 調剤行
    /// </summary>
    public string Dispensing { get; set; }

    /// <summary>
    /// 用法行
    /// </summary>
    public string Usage { get; set; }

    /// <summary>
    /// １日回数行
    /// </summary>
    public string NumberPerDay { get; set; }

    /// <summary>
    /// 用法補足行
    /// </summary>
    public string UsageSupplementary { get; set; }
}