using Domain.Models.EpsDispensing;
using Domain.Models.EpsPrescription;
using Domain.Models.PatientInfor;
using Domain.Models.SpecialNote.PatientInfo;
using Entity.Tenant;
using Helper.Common;

namespace Domain.Models.Eps;

public class EpsPrescriptionInfModel
{
    public EpsPrescriptionModel EpsPrescription { get; set; }
    public EpsDispensingModel EpsDispensing { get; set; }
    public PatientInforModel PtInf { get; set; }

    public EpsPrescriptionInfModel(EpsPrescriptionModel epsPrescription, PatientInforModel ptInf, EpsDispensingModel epsDispensing,
        string kaSName, string tantoName)
    {
        EpsPrescription = epsPrescription;
        PtInf = ptInf == null ? new PatientInforModel() : ptInf;
        EpsDispensing = epsDispensing;
        KaSName = kaSName;
        TantoName = tantoName;
    }

    public string KaSName { get; set; }

    public string TantoName { get; set; }

    /// <summary>
    /// 電子処方箋登録情報
    ///     電子処方箋管理サービスに登録した処方箋情報
    ///     ここに記録する処方箋は電子処方箋または紙の処方箋(引換番号あり)
    /// </summary>
    /// <summary>
    /// 医療機関識別ID
    /// </summary>
    public int HpId
    {
        get { return EpsPrescription == null ? 0 : EpsPrescription.HpId; }
    }

    /// <summary>
    /// 患者ID
    /// </summary>
    public long PtId
    {
        get { return EpsPrescription == null ? 0 : EpsPrescription.PtId; }
    }

    public long PtNum
    {
        get => PtInf.PtNum;
    }

    public string PtNumDisplay
    {
        get => PtInf.PtNum == 0 ? "" : PtInf.PtNum.ToString();
    }

    public string PtName
    {
        get => PtInf.Name;
    }

    /// <summary>
    /// 来院番号
    /// </summary>
    public long RaiinNo
    {
        get { return EpsPrescription == null ? 0 : EpsPrescription.RaiinNo; }
    }

    /// <summary>
    /// 連番
    /// </summary>
    public long SeqNo
    {
        get { return EpsPrescription == null ? 0 : EpsPrescription.SeqNo; }
    }

    /// <summary>
    /// リフィル回数
    ///     1:リフィルではない通常の処方箋（総使用回数1回）
    ///     2:リフィル処方箋（総使用回数2回）
    ///     3:リフィル処方箋（総使用回数3回）
    /// </summary>
    public int RefileCount
    {
        get { return EpsPrescription == null ? 0 : EpsPrescription.RefileCount; }
    }

    /// <summary>
    /// 診療日
    /// </summary>
    public int SinDate
    {
        get { return EpsPrescription == null ? 0 : EpsPrescription.SinDate; }
    }

    /// <summary>
    /// 保険者番号
    ///     PT_HOKEN_INF.HOKENSYA_NO
    ///     保険が公費単独の場合は空
    /// </summary>
    public string HokensyaNo
    {
        get { return EpsPrescription == null ? "" : EpsPrescription.HokensyaNo; }
    }

    /// <summary>
    /// 被保険者記号
    ///     PT_HOKEN_INF.KIGO
    ///     保険が公費単独の場合は空
    /// </summary>
    public string Kigo
    {
        get { return EpsPrescription == null ? "" : EpsPrescription.Kigo; }
    }

    /// <summary>
    /// 被保険者番号
    ///     PT_HOKEN_INF.BANGO
    ///     保険が公費単独の場合は空
    /// </summary>
    public string Bango
    {
        get { return EpsPrescription == null ? "" : EpsPrescription.Bango; }
    }

    /// <summary>
    /// 被保険者枝番
    ///     PT_HOKEN_INF.EDA_NO
    ///     保険が公費単独の場合は空
    /// </summary>
    public string EdaNo
    {
        get { return EpsPrescription == null ? "" : EpsPrescription.EdaNo; }
    }

    /// <summary>
    /// 公費負担者番号
    ///     保険が公費単独以外の場合は空
    /// </summary>
    public string KohiFutansyaNo
    {
        get { return EpsPrescription == null ? "" : EpsPrescription.KohiFutansyaNo; }
    }

    /// <summary>
    /// 公費受給者番号
    ///     保険が公費単独以外の場合は空
    /// </summary>
    public string KohiJyukyusyaNo
    {
        get { return EpsPrescription == null ? "" : EpsPrescription.KohiJyukyusyaNo; }
    }

    /// <summary>
    /// 処方箋ID
    /// </summary>
    public string PrescriptionId
    {
        get { return EpsPrescription == null ? "" : EpsPrescription.PrescriptionId; }
    }

    /// <summary>
    /// 引換番号
    /// </summary>
    public string AccessCode
    {
        get { return EpsPrescription == null ? "" : EpsPrescription.AccessCode; }
    }

    /// <summary>
    /// 発行形態
    ///     1: 電子
    ///     2: 紙
    /// </summary>
    public int IssueType
    {
        get { return EpsPrescription == null ? 0 : EpsPrescription.IssueType; }
    }

    /// <summary>
    /// 処方箋情報CSV
    ///     base64エンコードされた処方箋情報CSV
    /// </summary>
    public string PrescriptionDocument
    {
        get { return EpsPrescription == null ? "" : EpsPrescription.PrescriptionDocument; }
    }

    /// <summary>
    /// 状態
    ///     0:登録
    ///     1:取消待ち
    ///     2:取消中
    ///     3:取消済み
    /// </summary>
    public int Status
    {
        get { return EpsPrescription == null ? 0 : EpsPrescription.Status; }
        set
        {
            if (EpsPrescription == null || EpsPrescription.Status == value) return;
            EpsPrescription.Status = value;
        }
    }

    /// <summary>
    /// 取消理由
    ///     1:自動取消
    ///     2:変更
    ///     3:エラー
    ///     4:登録中断
    ///     5:手動取消
    ///     6:紙の処方箋（引換番号なし）発行
    /// </summary>
    public int DeletedReason
    {
        get { return EpsPrescription == null ? 0 : EpsPrescription.DeletedReason; }
    }

    /// <summary>
    /// 取消日時
    ///     STATUS>0に更新した日時
    /// </summary>
    public DateTime? DeletedDate
    {
        get { return EpsPrescription == null ? null : EpsPrescription.DeletedDate; }
    }

    public string PrescriptionDeletedDate
    {
        get => (DeletedDate == DateTime.MinValue || DeletedDate == null)
            ? ""
            : DeletedDate?.ToString("yyyy/MM/dd HH:mm:ss");
    }

    /// <summary>
    /// 診療科
    ///     RAIIN_INF.KA_ID
    /// </summary>
    public int KaId
    {
        get { return EpsPrescription == null ? 0 : EpsPrescription.KaId; }
    }

    /// <summary>
    /// 担当医
    ///     RAIIN_INF.TANTO_ID
    /// </summary>
    public int TantoId
    {
        get { return EpsPrescription == null ? 0 : EpsPrescription.TantoId; }
    }

    /// <summary>
    /// 作成日時
    /// </summary>
    public DateTime CreateDate
    {
        get { return EpsPrescription == null ? DateTime.MinValue : EpsPrescription.CreateDate; }
    }

    public string PrescriptionCreateDate
    {
        get => CreateDate == DateTime.MinValue ? "" : CreateDate.ToString("yyyy/MM/dd HH:mm:ss");
    }

    /// <summary>
    /// 作成ID
    /// </summary>
    public int CreateId
    {
        get { return EpsPrescription == null ? 0 : EpsPrescription.CreateId; }
    }

    /// <summary>
    /// 更新日時
    /// </summary>
    public DateTime UpdateDate
    {
        get { return EpsPrescription == null ? DateTime.MinValue : EpsPrescription.UpdateDate; }
    }

    /// <summary>
    /// 更新ID
    /// </summary>
    public int UpdateId
    {
        get { return EpsPrescription == null ? 0 : EpsPrescription.UpdateId; }
    }

    public string SinDateDisplay
    {
        get => CIUtil.SDateToShowSDate(SinDate);
    }

    public string HokenDisplay
    {
        get
        {
            if (CheckDefaultValue()) return "";
            string hokensyaNo = string.IsNullOrEmpty(HokensyaNo) ? EpsDispensing?.HokensyaNo : HokensyaNo;
            string bango = string.IsNullOrEmpty(Bango) ? EpsDispensing?.Bango : Bango;
            string kigo = string.IsNullOrEmpty(Kigo) ? EpsDispensing?.Kigo : Kigo;
            string edaNo = string.IsNullOrEmpty(EdaNo) ? EpsDispensing?.EdaNo : EdaNo;
            string kohiFutansyaNo =
                string.IsNullOrEmpty(KohiFutansyaNo) ? EpsDispensing?.KohiFutansyaNo : KohiFutansyaNo;
            string kohiJyukyusyaNo =
                string.IsNullOrEmpty(KohiJyukyusyaNo) ? EpsDispensing?.KohiJyukyusyaNo : KohiJyukyusyaNo;
            if (!string.IsNullOrEmpty(hokensyaNo))
            {
                return $"{hokensyaNo}/{kigo}・{bango}({edaNo})";
            }
            else
            {
                return string.IsNullOrEmpty(kohiFutansyaNo) && string.IsNullOrEmpty(kohiJyukyusyaNo)
                    ? ""
                    : $"{kohiFutansyaNo}/{kohiJyukyusyaNo}";
            }
        }
    }

    public string Refill
    {
        get
        {
            if (CheckDefaultValue()) return "";
            int? dispensingTimes = EpsDispensing?.DispensingTimes;
            int? refileCount = EpsPrescription?.RefileCount;
            string time = string.Empty;
            string count = string.Empty;

            if (refileCount == 1)
            {
                count = "1";
                if (dispensingTimes == 1)
                {
                    time = "1";
                }
                else
                {
                    time = "0";
                }
            }
            else if (refileCount == 2)
            {
                count = "2";
                if (dispensingTimes == 1)
                {
                    time = "1";
                }
                else if (dispensingTimes == 2)
                {
                    time = "2";
                }
                else
                {
                    time = "0";
                }
            }
            else if (refileCount == 3)
            {
                count = "3";
                if (dispensingTimes == 1)
                {
                    time = "1";
                }
                else if (dispensingTimes == 2)
                {
                    time = "2";
                }
                else if (dispensingTimes == 3)
                {
                    time = "3";
                }
                else
                {
                    time = "0";
                }
            }
            else
            {
                count = "x";
                if (dispensingTimes == 1)
                {
                    time = "1";
                }
                else if (dispensingTimes == 2)
                {
                    time = "2";
                }
                else if (dispensingTimes == 3)
                {
                    time = "3";
                }
                else
                {
                    time = "0";
                }
            }

            return $"{time}/{count}回";
        }
    }

    public string IssueTypeDisplay
    {
        get
        {
            switch (IssueType)
            {
                case 1:
                    return "電子";
                case 2:
                    return "紙";
                default:
                    return "";
            }
        }
    }

    public string StatusDisplay
    {
        get
        {
            if (CheckDefaultValue()) return "";
            switch (Status)
            {
                case 1:
                    return "取消待ち";
                case 2:
                    return "取消中";
                case 3:
                    return "取消済み";
                default:
                    return "登録";
            }
        }
    }

    public string DeleteReasonDisplay
    {
        get
        {
            switch (DeletedReason)
            {
                case 1:
                    return "自動取消";
                case 2:
                    return "変更";
                case 3:
                    return "エラー";
                case 4:
                    return "登録中断";
                case 5:
                    return "手動取消";
                case 6:
                    return "紙の処方箋（引換番号なし）発行";
                default:
                    return "";
            }
        }
    }

    public string ResultTypeDisplay
    {
        get
        {
            if (EpsDispensing == null) return "";
            switch (EpsDispensing.ResultType)
            {
                case 1:
                    return "調剤済み";
                case 2:
                    return "調剤取消";
                case 3:
                    return "処方箋回収";
                case 4:
                    return "調剤中";
                default:
                    return "";
            }
        }
    }

    public int ResultType
    {
        get => EpsDispensing == null ? 0 : EpsDispensing.ResultType;
    }

    public string PharmacyName
    {
        get => EpsDispensing?.ReceptionPharmacyName;
    }

    public string DispensingDateDisplay
    {
        get => EpsDispensing == null ? "" : CIUtil.SDateToShowSDate(EpsDispensing.DispensingDate);
    }

    public int DispensingDate
    {
        get => EpsDispensing == null ? 0 : EpsDispensing.DispensingDate;
    }

    public DateTime EpsUpdateDateTime
    {
        get => EpsDispensing == null ? DateTime.MinValue : EpsDispensing.EpsUpdateDateTime;
    }

    public string MessageFlag
    {
        get
        {
            if (EpsDispensing == null) return "";
            switch (EpsDispensing.MessageFlg)
            {
                case 1:
                    return "";
                case 2:
                    return "未";
                case 3:
                    return "済";
                default:
                    return "";
            }
        }
    }

    public string DispensingCreateDate
    {
        get => (EpsDispensing?.CreateDate == DateTime.MinValue || EpsDispensing == null)
            ? ""
            : EpsDispensing?.CreateDate.ToString("yyyy/MM/dd HH:mm:ss");
    }

    public bool CheckDefaultValue()
    {
        return (EpsDispensing != null && EpsDispensing.Id == 0) ||
               (EpsPrescription != null && EpsPrescription.PtId == 0);
    }
}
