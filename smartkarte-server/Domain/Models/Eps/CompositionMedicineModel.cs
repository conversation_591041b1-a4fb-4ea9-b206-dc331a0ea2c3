namespace Domain.Models.Eps;

public class CompositionMedicineModel
{
    /// <summary>
    /// 薬品行
    /// </summary>
    public string Pharmaceutical { get; set; }

    /// <summary>
    /// 数量行
    /// </summary>
    public string Quantity { get; set; }

    /// <summary>
    /// １回服用量行
    /// </summary>
    public string SingleDose { get; set; }

    /// <summary>
    /// 薬品補足
    /// </summary>
    public List<string> DrugSupplements { get; set; }

    public bool IsEmptySingDoseAndDrugSupplement
    {
        get => string.IsNullOrEmpty(SingleDose) && (DrugSupplements == null || DrugSupplements.Count == 0);
    }
}
