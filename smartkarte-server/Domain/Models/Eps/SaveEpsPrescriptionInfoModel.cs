﻿namespace Domain.Models.Eps;

public class SaveEpsPrescriptionInfoModel
{
    public int HpId { get; set; }
    public long PtId { get; set; }
    public long RaiinNo { get; set; }
    public long SeqNo { get; set; }
    public int RefileCount { get; set; }
    public int SinDate { get; set; }
    public string <PERSON><PERSON>syaNo { get; set; } 
    public string <PERSON>go { get; set; } 
    public string Bango { get; set; } 
    public string EdaNo { get; set; } 
    public string <PERSON>hiFutansyaNo { get; set; } 
    public string KohiJyukyusyaNo { get; set; } 
    public string PrescriptionId { get; set; } 
    public string AccessCode { get; set; } 
    public int IssueType { get; set; }
    public string PrescriptionDocument { get; set; } 
    public int Status { get; set; }
    public int DeletedReason { get; set; }
}