using Domain.Models.Insurance;
using PtHokenPatternModel = Domain.Models.Receipt.Recalculation.PtHokenPatternModel;

namespace Domain.Models.Eps;

public class EpsOrderInfModel
{
    public int HpId { get; set; }
    public long RaiinNo { get; set; }
    public long RpNo { get; set; }
    public long RpEdaNo { get; set; }
    public long PtId { get; set; }
    public int SinDate { get; set; }
    public int HokenPid { get; set; }
    public int OdrKouiKbn { get; set; }
    public string RpName { get; set; }
    public int InoutKbn { get; set; }
    public int SikyuKbn { get; set; }
    public int SyohoSbt { get; set; }
    public int SanteiKbn { get; set; }
    public int TosekiKbn { get; set; }
    public int DaysCnt { get; set; }
    public int SortNo { get; set; }
    public long Id { get; set; }
    public int IsDeleted { get; set; }
    
    public PtHokenPatternModel PtHokenPatternModel { get; set; } = new ();
    
    public HokenInfModel HokenInfModel { get; set; } = new ();
    
    public List<KohiInfModel> KohiInfModel { get; set; } = new ();
    
    public List<EpsOrdInfDetailModel> OrderDetailsNotContainMedicine { get; set; } = new ();
   
    public bool OrderContainsCommonNameNotInCommonNameMedicines { get; set; }
    
    public List<EpsOrdInfDetailModel> OrderDetails{ get; set; } = new ();

}