﻿namespace Domain.Models.Eps.CheckErrorPreRegistration;

public class TodayGroupOdrInfModel
{
    public List<TodayOdrInfModel> OdrInfModels { get; set; }

    public int GroupKouiCode { get; set; }

    public string GroupName { get; set; }

    public int InOutKbn { get; set; }

    public string InOutName { get; set; }

    public bool IsShowSikyu { get; set; }

    public int SikyuKbn { get; set; }

    public int TosekiKbn { get; set; }

    public int SyohoSbt { get; set; }

    public string SikyuName { get; set; }

    public bool IsDrug { get; set; }

    public bool IsKensa { get; set; }

    public int HokenPid { get; set; }

    public bool IsShowSantei { get; set; }

    public int SanteiKbn { get; set; }

    public string SanteiName { get; set; }
    public bool IsExpanded { get; set; }

    public TodayGroupOdrInfModel(List<TodayOdrInfModel> todayOdrInfModels)
    {
        OdrInfModels = new List<TodayOdrInfModel>(todayOdrInfModels);
    }
}