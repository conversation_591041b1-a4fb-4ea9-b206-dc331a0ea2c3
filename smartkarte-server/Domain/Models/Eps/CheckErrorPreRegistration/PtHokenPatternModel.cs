﻿using Entity.Tenant;
using Helper.Common;

namespace Domain.Models.Eps.CheckErrorPreRegistration;

public class PtHokenPatternModel
{
     public PtHokenPattern PtHokenPattern { get; } = null;

        public PtHokenPatternModel(int sinday, int birthday,
            PtHokenPattern ptHokenPattern, PtHokenInfModel HokenInf,
            PtKohiModel Kohi1Inf, PtKohiModel Kohi2Inf, PtKohiModel Kohi3Inf, PtKohiModel Kohi4Inf)
        {
            Sinday = sinday;
            BirthDay = birthday;
            PtHokenPattern = ptHokenPattern;
            this.HokenInf = HokenInf;
            this.Kohi1Inf = Kohi1Inf;
            this.Kohi2Inf = Kohi2Inf;
            this.Kohi3Inf = Kohi3Inf;
            this.Kohi4Inf = Kohi4Inf;
        }

        /// <summary>
        /// 患者ID
        ///  患者を識別するためのシステム固有の番号
        /// </summary>
        public long PtId
        {
            get { return PtHokenPattern.PtId; }
        }

        /// <summary>
        /// 保険ID
        ///  患者別に保険情報を識別するための固有の番号
        /// </summary>
        public int HokenPid
        {
            get { return PtHokenPattern.HokenPid; }
        }

        /// <summary>
        /// 連番
        /// </summary>
        public long SeqNo
        {
            get { return PtHokenPattern.SeqNo; }
        }

        /// <summary>
        /// 保険区分
        ///  0:自費
        ///  1:社保
        ///  2:国保
        ///  11:労災(短期給付)
        ///  12:労災(傷病年金)
        ///  13:アフターケア
        ///  14:自賠責
        /// </summary>
        public int HokenKbn
        {
            get { return PtHokenPattern.HokenKbn; }
        }

        /// <summary>
        /// 保険種別コード
        ///  0:      下記以外
        ///  11..14: 社保単独～４併
        ///  21..24: 国保単独～４併
        ///  31..34: 後期単独～４併
        ///  41..44: 退職単独～４併
        ///  51..54: 公費単独～４併
        /// </summary>
        public int HokenSbtCd
        {
            get { return PtHokenPattern.HokenSbtCd; }
        }

        /// <summary>
        /// 主保険 保険ID
        /// </summary>
        public int HokenId
        {
            get { return PtHokenPattern.HokenId; }
        }

        /// <summary>
        /// 公費１ 保険ID
        /// </summary>
        public int Kohi1Id
        {
            get { return PtHokenPattern.Kohi1Id; }
        }

        /// <summary>
        /// 公費２ 保険ID
        /// </summary>
        public int Kohi2Id
        {
            get { return PtHokenPattern.Kohi2Id; }
        }

        /// <summary>
        /// 公費３ 保険ID
        /// </summary>
        public int Kohi3Id
        {
            get { return PtHokenPattern.Kohi3Id; }
        }

        /// <summary>
        /// 公費４ 保険ID
        /// </summary>
        public int Kohi4Id
        {
            get { return PtHokenPattern.Kohi4Id; }
        }


        /// <summary>
        /// 保険メモ
        /// </summary>
        public string HokenMemo
        {
            get { return PtHokenPattern.HokenMemo; }
        }

        /// <summary>
        /// 適用開始日
        ///  主保険の適用開始日(主保険を持たない場合は公費１ or 労災)          
        /// </summary>
        public int StartDate
        {
            get { return PtHokenPattern.StartDate; }
        }

        /// <summary>
        /// 適用終了日
        ///  主保険の適用終了日(主保険を持たない場合は公費１ or 労災)          
        /// </summary>
        public int EndDate
        {
            get { return PtHokenPattern.EndDate; }
        }

        /// <summary>
        /// 削除区分
        ///  1:削除
        /// </summary>
        public int IsDeleted
        {
            get { return PtHokenPattern.IsDeleted; }
        }

        /// <summary>
        /// 作成日時 
        /// </summary>
        public DateTime CreateDate
        {
            get { return PtHokenPattern.CreateDate; }
        }

        /// <summary>
        /// 作成者  
        /// </summary>
        public int CreateId
        {
            get { return PtHokenPattern.CreateId; }
        }

        /// <summary>
        /// 作成端末   
        /// </summary>
        public string CreateMachine
        {
            get { return PtHokenPattern.CreateMachine; }
        }

        /// <summary>
        /// 更新日時   
        /// </summary>
        public DateTime UpdateDate
        {
            get { return PtHokenPattern.UpdateDate; }
        }

        /// <summary>
        /// 更新者   
        /// </summary>
        public int UpdateId
        {
            get { return PtHokenPattern.UpdateId; }
        }

        /// <summary>
        /// 更新端末   
        /// </summary>
        public string UpdateMachine
        {
            get { return PtHokenPattern.UpdateMachine; }
        }

        private int Sinday;

        private int BirthDay;

        private int Age
        {
            get => CIUtil.SDateToAge(BirthDay, Sinday);
        }

        public bool IsExpirated
        {
            get
            {
                return !(StartDate <= Sinday && EndDate >= Sinday);
            }
        }

        public PtHokenInfModel HokenInf { get; set; }

        public PtKohiModel Kohi1Inf { get; set; }

        public PtKohiModel Kohi2Inf { get; set; }

        public PtKohiModel Kohi3Inf { get; set; }

        public PtKohiModel Kohi4Inf { get; set; }

        public bool IsEmptyHoken
        {
            get => (HokenId == 0 || HokenInf == null);
        }

        public bool IsEmptyKohi1
        {
            get => (Kohi1Id == 0 || Kohi1Inf == null);
        }

        public bool IsEmptyKohi2
        {
            get => (Kohi2Id == 0 || Kohi2Inf == null);
        }

        public bool IsEmptyKohi3
        {
            get => (Kohi3Id == 0 || Kohi3Inf == null);
        }

        public bool IsEmptyKohi4
        {
            get => (Kohi4Id == 0 || Kohi4Inf == null);
        }

        public int HokenNumber
        {
            get { return HokenInf.HokenNo; }
        }

        public bool HaveKohi
        {
            get => Kohi1Id > 0 ||
                   Kohi2Id > 0 ||
                   Kohi3Id > 0 ||
                   Kohi4Id > 0;
        }

        private string GetKohiCountName(int kohicount)
        {
            if (kohicount <= 0)
            {
                return string.Empty;
            }
            if (kohicount == 1)
            {
                return "単独";
            }
            else
            {
                return kohicount + "併";
            }
        }


        /// <summary>
        /// 0: Default
        /// 1: 健保
        /// 2: 労災・自賠
        /// 3: 自費
        /// </summary>
        public int HokenType
        {
            get => GetHokenPatternType();
        }

        private int GetHokenPatternType()
        {
            switch (HokenKbn)
            {
                case 0:
                    return 3;
                case 1:
                case 2:
                    return 1;
                case 11:
                case 12:
                case 13:
                case 14:
                    return 2;
                default:
                    return 0;
            }
        }
}