﻿namespace Domain.Models.Eps.CheckErrorPreRegistration;

public class TodayHokenOdrInfModel
{
    public List<TodayGroupOdrInfModel> GroupOdrInfModels { get; set; }

    public TodayGroupOdrInfModel SelectedGroupModel { get; set; }

    public List<TodayGroupOdrInfModel> HokenGroupOdrInfModelView { get; set; }

    public int MainPatternPid { get; set; }

    public PtHokenPatternModel HokenPatternModel { get; set; }

    public int PatternPid { get; set; }

    public TodayHokenOdrInfModel()
    {
        GroupOdrInfModels = new List<TodayGroupOdrInfModel>();
    }

    public TodayHokenOdrInfModel(List<TodayGroupOdrInfModel> todayGroupOdrInfModels)
    {
        GroupOdrInfModels = new List<TodayGroupOdrInfModel>(todayGroupOdrInfModels);
    }
}