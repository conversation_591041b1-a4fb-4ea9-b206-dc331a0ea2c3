﻿using Entity.Tenant;

namespace Domain.Models.Eps.CheckErrorPreRegistration;

public class TodayOdrInfModel
{
    private OdrInf OdrInf { get; set; }

    public TodayOdrInfModel(OdrInf odrInf, IEnumerable<TodayOdrInfDetailModel> odrInfDetails)
    {
        OdrInf = odrInf;
        OdrInfDetailModels = new List<TodayOdrInfDetailModel>(odrInfDetails);
        Id = odrInf.Id;
        HpId = odrInf.HpId;
        PtId = odrInf.PtId;
        SinDate = odrInf.SinDate;
        RaiinNo = odrInf.RaiinNo;
        RpNo = odrInf.RpNo;
        RpEdaNo = odrInf.RpEdaNo;
        HokenPid = odrInf.HokenPid;
        OdrKouiKbn = odrInf.OdrKouiKbn;
        RpName = odrInf.RpName;
        InoutKbn = odrInf.InoutKbn;
        SikyuKbn = odrInf.SikyuKbn;
        SyohoSbt = odrInf.SyohoSbt;
        SanteiKbn = odrInf.SanteiKbn;
        TosekiKbn = odrInf.TosekiKbn;
        DaysCnt = odrInf.DaysCnt;
        SortNo = odrInf.SortNo;
        IsDeleted = odrInf.IsDeleted;
        CreateDate = odrInf.CreateDate;
        CreateId = odrInf.CreateId;
        CreateMachine = odrInf.CreateMachine;
        UpdateDate = odrInf.UpdateDate;
        UpdateId = odrInf.UpdateId;
        UpdateMachine = odrInf.UpdateMachine;
    }

    public long Id { get; set; }

    public int HpId { get; set; }

    public long PtId { get; set; }

    public int SinDate { get; set; }

    public long RaiinNo { get; set; }

    public long RpNo { get; set; }

    public long RpEdaNo { get; set; }

    public int HokenPid { get; set; }

    public int HokenId { get; set; }

    public int OdrKouiKbn { get; set; }

    public string RpName { get; set; }

    public int InoutKbn { get; set; }

    public int SikyuKbn { get; set; }

    public int SyohoSbt { get; set; }

    public int SanteiKbn { get; set; }

    public int TosekiKbn { get; set; }

    public int DaysCnt { get; set; }

    public int SortNo { get; set; }

    public int IsDeleted { get; set; }

    private bool IsDeleting { get; set; } = false;

    public DateTime CreateDate { get; set; }

    public int CreateId { get; set; }

    public string CreateMachine { get; set; }

    public DateTime UpdateDate { get; set; }

    public int UpdateId { get; set; }

    public string UpdateMachine { get; set; }


    public List<TodayOdrInfDetailModel> OdrInfDetailModels { get; set; }
    
    public List<TodayOdrInfDetailModel> OdrInfDetailModelsIgnoreEmpty
    {
        get
        {
            if (OdrInfDetailModels == null)
            {
                return OdrInfDetailModels;
            }
            return new List<TodayOdrInfDetailModel>(OdrInfDetailModels.Where(o => !o.IsEmpty).ToList());
        }
    }

}