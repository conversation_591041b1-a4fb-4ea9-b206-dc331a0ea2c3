﻿using Domain.Models.KensaMst;
using Domain.Models.OrdInf;
using Entity.Tenant;

namespace Domain.Models.Eps.CheckErrorPreRegistration;

public class TodayOdrInfDetailModel
{
    public OdrInfDetail OdrInfDetail { get; set; }
   
    public TodayOdrInfDetailModel(OdrInfDetail odrInfDetail, string santeiItemCd, string masterSbt)
    {
        OdrInfDetail = odrInfDetail;
        ItemCd = string.Empty;
        IpnName = string.Empty;
        ItemName = string.Empty;
        IpnCd = string.Empty;
        SanteiItemCd = santeiItemCd;
        MasterSbt = masterSbt;
    }

    public TodayOdrInfDetailModel(OdrInfDetail odrInfDetail)
    {
        OdrInfDetail = odrInfDetail;
        ItemCd = string.Empty;
        IpnName = string.Empty;
        ItemName = string.Empty;
        IpnCd = string.Empty;
        SanteiItemCd = string.Empty;
        MasterSbt = string.Empty;
    }

    /// <summary>
    /// オーダー情報詳細
    /// </summary>
    /// <summary>
    /// 医療機関識別ID
    /// </summary>
    public int HpId { get; set; }

    /// <summary>
    /// 患者ID
    ///       患者を識別するためのシステム固有の番号
    /// </summary>
    public long PtId { get; set; }

    /// <summary>
    /// 診療日
    ///       yyyyMMdd
    /// </summary>
    public int SinDate { get; set; }

    /// <summary>
    /// 来院番号
    /// </summary>
    public long RaiinNo { get; set; }

    /// <summary>
    /// 剤番号
    ///     ODR_INF.RP_NO
    /// </summary>
    public long RpNo { get; set; }

    /// <summary>
    /// 剤枝番
    ///     ODR_INF.RP_EDA_NO
    /// </summary>
    public long RpEdaNo { get; set; }

    /// <summary>
    /// 行番号
    /// </summary>
    public int RowNo { get; set; }

    /// <summary>
    /// 診療行為区分
    /// </summary>
    public int SinKouiKbn { get; set; }

    /// <summary>
    /// 項目コード
    /// </summary>
    public string ItemCd { get; set; }

    /// <summary>
    /// 一般名
    /// </summary>
    public string IpnName { get; set; }

    /// <summary>
    /// 用法区分
    ///          0: 用法以外
    ///          1: 基本用法
    ///          2: 補助用法
    /// </summary>
    public int YohoKbn { get; set; }
    
    public string ItemName { get; set; }

    public int SyohoKbn { get; set; }

    public string IpnCd { get; set; }
    
    public string SanteiItemCd { get; set; }
    
    public string MasterSbt { get; set; }
    
    public bool IsEmpty
    {
        get
        {
            return string.IsNullOrEmpty(ItemCd) &&
                   string.IsNullOrEmpty(ItemName) &&
                   SinKouiKbn == 0;
        }
    }

    public KensaMstModel KensaMstModel {  get; set; }

    public double Ten { get; set; }

    public int HandanGrpKbn { get; set; }

    public int CmtCol1 { get; set; }

    public int CmtCol2 { get; set; }

    public int CmtCol3 { get; set; }

    public int CmtCol4 { get; set; }

    public int CmtColKeta1 { get; set; }

    public int CmtColKeta2 { get; set; }

    public int CmtColKeta3 { get; set; }

    public int CmtColKeta4 { get; set; }

    public bool IsGetPriceInYakka {  get; set; }

    public IpnMinYakkaMstModel IpnMinYakkaMstModel { get; set; }

    public int IsDeletedTenMst { get; set; }
}