﻿using Domain.Models.HokenMst;
using Domain.Models.InsuranceMst;
using Entity.Tenant;

namespace Domain.Models.Eps.CheckErrorPreRegistration;

public class PtKohiModel
{
    public PtKohi PtKohi { get; } = null;


    public PtKohiModel(int sinday, PtKohi ptKohi)
    {
        Sinday = sinday;
        PtKohi = ptKohi;
    }

    /// <summary>
    /// 患者公費情報
    /// </summary>
    /// <summary>
    /// 医療機関識別ID
    /// </summary>
    public int HpId
    {
        get { return PtKohi.HpId; }
    }

    /// <summary>
    /// 患者ID
    ///  患者を識別するためのシステム固有の番号
    /// </summary>
    public long PtId
    {
        get { return PtKohi.PtId; }
    }

    /// <summary>
    /// 保険ID
    ///  患者別に保険情報を識別するための固有の番号
    /// </summary>
    public int HokenId
    {
        get { return PtKohi.HokenId; }
    }

    /// <summary>
    /// 連番
    /// </summary>
    public long SeqNo
    {
        get { return PtKohi.SeqNo; }
    }

    /// <summary>
    /// 都道府県番号
    ///  保険マスタの都道府県番号
    /// </summary>
    public int PrefNo
    {
        get { return PtKohi.PrefNo; }
    }

    /// <summary>
    /// 保険番号
    ///  保険マスタに登録された保険番号
    /// </summary>
    public int HokenNo
    {
        get { return PtKohi.HokenNo; }
    }

    public string DisplayHokenNo
    {
        get { return "(" + PtKohi.HokenNo + ")"; }
    }

    public string DisplayHoubetu
    {
        get { return "(" + PtKohi.Houbetu + ")"; }
    }

    /// <summary>
    /// 保険番号枝番
    ///  保険マスタに登録された保険番号枝番
    /// </summary>
    public int HokenEdaNo
    {
        get { return PtKohi.HokenEdaNo; }
    }


    /// <summary>
    /// 負担者番号
    /// </summary>
    public string FutansyaNo
    {
        get { return PtKohi.FutansyaNo; }
    }

    /// <summary>
    /// 受給者番号
    /// </summary>
    public string JyukyusyaNo
    {
        get { return PtKohi.JyukyusyaNo; }
    }

    /// <summary>
    /// 特殊受給者番号
    /// </summary>
    public string TokusyuNo
    {
        get { return PtKohi.TokusyuNo; }
    }

    /// <summary>
    /// 資格取得日
    ///  yyyymmdd 
    /// </summary>
    public int SikakuDate
    {
        get { return PtKohi.SikakuDate; }
    }

    /// <summary>
    /// 交付日
    ///  yyyymmdd
    /// </summary>
    public int KofuDate
    {
        get { return PtKohi.KofuDate; }
    }

    /// <summary>
    /// 適用開始日
    ///  yyyymmdd
    /// </summary>
    public int StartDate
    {
        get { return PtKohi.StartDate; }
    }

    /// <summary>
    /// 適用終了日
    ///  yyyymmdd
    /// </summary>
    public int EndDate
    {
        get { return PtKohi.EndDate > 0 ? PtKohi.EndDate : 99999999; }
    }

    /// <summary>
    /// 負担率
    ///  yyyymmdd
    /// </summary>
    public int Rate
    {
        get { return PtKohi.Rate; }
    }

    /// <summary>
    /// 一部負担限度額
    ///  yyyymmdd
    /// </summary>
    public int GendoGaku
    {
        get { return PtKohi.GendoGaku; }
    }

    /// <summary>
    /// 削除区分
    ///  1:削除
    /// </summary>
    public int IsDeleted
    {
        get { return PtKohi.IsDeleted; }
    }

    /// <summary>
    /// 作成日時 
    /// </summary>
    public DateTime CreateDate
    {
        get { return PtKohi.CreateDate; }
    }

    /// <summary>
    /// 作成者  
    /// </summary>
    public int CreateId
    {
        get { return PtKohi.CreateId; }
    }

    /// <summary>
    /// 作成端末   
    /// </summary>
    public string CreateMachine
    {
        get { return PtKohi.CreateMachine; }
    }

    /// <summary>
    /// 更新日時   
    /// </summary>
    public DateTime UpdateDate
    {
        get { return PtKohi.UpdateDate; }
    }

    /// <summary>
    /// 更新者   
    /// </summary>
    public int UpdateId
    {
        get { return PtKohi.UpdateId; }
    }

    /// <summary>
    /// 更新端末   
    /// </summary>
    public string UpdateMachine
    {
        get { return PtKohi.UpdateMachine; }
    }

    private int Sinday;
}