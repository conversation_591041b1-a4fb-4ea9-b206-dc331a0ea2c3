﻿using Entity.Tenant;

namespace Domain.Models.Eps.CheckErrorPreRegistration;

public class ReceptionModel
{
    public RaiinInf RaiinInf { get; } = null;

    public ReceptionModel(RaiinInf raiinInf)
    {
        RaiinInf = raiinInf;
        HpId = raiinInf.HpId;
        PtId = raiinInf.PtId;
        SinDate = raiinInf.SinDate;
        RaiinNo = raiinInf.RaiinNo;
        HokenPid = raiinInf.HokenPid;
        HokenPid = raiinInf.HokenPid;
        SanteiKbn = raiinInf.SanteiKbn;
        OyaRaiinNo = raiinInf.OyaRaiinNo;
        Status = raiinInf.Status;
        IsYoyaku = raiinInf.IsYoyaku;
        YoyakuTime = raiinInf.YoyakuTime;
        YoyakuId = raiinInf.YoyakuId;
        ConfirmationType = raiinInf.ConfirmationType;
        PrescriptionIssueType = raiinInf.PrescriptionIssueType;
        UketukeSbt = raiinInf.UketukeSbt;
        UketukeTime = raiinInf.UketukeTime;
        UketukeId = raiinInf.UketukeId;
        UketukeNo = raiinInf.UketukeNo;
        SinStartTime = raiinInf.SinStartTime;
        SinEndTime = raiinInf.SinEndTime;
        KaikeiTime = raiinInf.KaikeiTime;
        KaikeiId = raiinInf.KaikeiId;
        KaId = raiinInf.KaId;
        TantoId = raiinInf.TantoId;
        SyosaisinKbn = raiinInf.SyosaisinKbn;
        JikanKbn = raiinInf.JikanKbn;
        PrintEPSReference = raiinInf.PrintEpsReference;
    }

    public int HpId;

    public long PtId;

    public int SinDate;

    public long RaiinNo;

    public int HokenPid;

    public int SanteiKbn;
    public long OyaRaiinNo;

    public int Status;

    public int IsYoyaku;

    public string YoyakuTime;

    public int YoyakuId;

    public int ConfirmationType;

    public int PrescriptionIssueType;

    public int UketukeSbt;

    public string UketukeTime;

    public int UketukeId;

    public int UketukeNo;

    public string SinStartTime;

    public string SinEndTime;

    public string KaikeiTime;

    public int KaikeiId;

    public int KaId;

    public int TantoId;

    public int SyosaisinKbn;

    public int JikanKbn;

    public int CreateId;

    public int UpdateId;

    public DateTime CreateDate;

    public DateTime UpdateDate;

    public string CreateMachine;

    public string UpdateMachine;

    public int PrintEPSReference;

}