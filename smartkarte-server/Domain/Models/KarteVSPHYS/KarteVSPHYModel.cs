﻿using Domain.Models.SpecialNote.PatientInfo;

namespace Domain.Models.KarteVSPHYS
{
    public class KarteVSPHYModel
    {
        public KarteVSPHYModel()
        {
            PysicalInfoModels = new();
            AverageDay = new();
            AverageMonths = new();
        }

        public KarteVSPHYModel(List<PhysicalInfoModel> pysicalInfoModels, Dictionary<long, Dictionary<string, double>> averageDay, Dictionary<string, Dictionary<string, double>> averageMonths, int lastSinDate)
        {
            PysicalInfoModels = pysicalInfoModels;
            AverageDay = averageDay;
            AverageMonths = averageMonths;
            LastSinDate = lastSinDate;
        }

        public List<PhysicalInfoModel> PysicalInfoModels { get; private set; }

        public Dictionary<long, Dictionary<string, double>> AverageDay { get; private set; }

        public Dictionary<string, Dictionary<string, double>> AverageMonths { get; private set; }

        public int LastSinDate { get; private set; }
    }
}
