﻿using Domain.Common;
using Domain.Models.SpecialNote.PatientInfo;

namespace Domain.Models.KarteVSPHYS
{
    public interface IKarteVSPHYSRepository : IRepositoryBase
    {
        bool SaveKartePhysicals(int userId, int hpId, long ptId, KensaInfModel kensaInfModel, bool saveIfEmpty = true);

        bool CheckIsExistedKensaTime(int hpId, int iraiDate, string kensaTime, long raiinNo, long ptId, int inouKbn);

        bool CheckIsExistedKensaTimeWithIraiCd(int hpId, int iraiDate, string kensaTime, long raiinNo, long ptId, long iraiCd, int inouKbn);

        KarteVSPHYModel GetKarteVSPHYSList(int hpId, long ptId, bool isSearch, long? startDate, long? endDate, int raiinNo);

        Dictionary<long, long> SaveRequestExams(int userId, int hpId, List<long> orderInfIds, string centerCd);

        bool DeleteRequestExams(int userId, int hpId, List<long> iraiCds);

        bool SaveKartePhysicalsConsulationResult(int userId, int hpId, long ptId, KensaInfModel? kensaInfModel);
    }
}
