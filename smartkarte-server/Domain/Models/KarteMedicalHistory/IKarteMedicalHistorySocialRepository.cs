﻿using Domain.Common;

namespace Domain.Models.KarteMedicalHistory
{
    public interface IKarteMedicalHistorySocialRepository : IRepositoryBase
    {
        bool SaveKarteMedicalHistorySocial(int hpId, int userId, long ptId, PtSmokingRelatedModel ptSocialHistoryModel);

        bool SaveKarteMedicalHistorySocialConsultationResult(int hpId, int userId, long ptId, PtSmokingRelatedModel? ptSocialHistoryModel);

        int GetPatientAge(int hpId, long ptId);
    }
}
