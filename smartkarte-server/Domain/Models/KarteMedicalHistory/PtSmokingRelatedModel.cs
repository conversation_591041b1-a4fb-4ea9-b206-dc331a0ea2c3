﻿namespace Domain.Models.KarteMedicalHistory
{
    public class PtSmokingRelatedModel
    {
        public PtSmokingRelatedModel() { }
        public PtSmokingRelatedModel(int hpId, long ptId, long seqNo, int smokingStatus, string? smokingDetail, int smokingDailyCount, int smokingDuration, int smokingDurationUnit, int drinkingStatus, int amountOfAlcohol, string? drinkingDetail, int isDeleted)
        {
            HpId = hpId;
            PtId = ptId;
            SeqNo = seqNo;
            SmokingStatus = smokingStatus;
            SmokingDetail = smokingDetail;
            SmokingDailyCount = smokingDailyCount;
            SmokingDuration = smokingDuration;
            SmokingDurationUnit = smokingDurationUnit;
            DrinkingFrequency = drinkingStatus;
            DrinkingAmount = amountOfAlcohol;
            DrinkingDetail = drinkingDetail;
            IsDeleted = isDeleted;
        }

        public PtSmokingRelatedModel(int hpId, long ptId, int smokingStatus, string? smokingDetail, int smokingDailyCount, int smokingDuration, int drinkingStatus, int amountOfAlcohol, string? drinkingDetail, int isDeleted, int? smokingStartAge, int? smokingEndAge)
        {
            HpId = hpId;
            PtId = ptId;
            SmokingStatus = smokingStatus;
            SmokingDetail = smokingDetail;
            SmokingDailyCount = smokingDailyCount;
            SmokingDuration = smokingDuration;
            DrinkingFrequency = drinkingStatus;
            DrinkingAmount = amountOfAlcohol;
            DrinkingDetail = drinkingDetail;
            IsDeleted = isDeleted;
            SmokingStartAge = smokingStartAge;
            SmokingEndAge = smokingEndAge;
        }

        public PtSmokingRelatedModel(int hpId, long ptId, int smokingStatus, string? smokingDetail, int smokingDailyCount, int smokingDuration, int drinkingFrequency, int drinkingAmount, string? drinkingDetail, int isDeleted, int? smokingStartAge, int? smokingEndAge, int smokingStartYear, int smokingEndYear, int totalSmokingDuration, long brinkmanNumber, int age)
        {
            HpId = hpId;
            PtId = ptId;
            SmokingStatus = smokingStatus;
            SmokingDetail = smokingDetail;
            SmokingDailyCount = smokingDailyCount;
            SmokingDuration = smokingDuration;
            DrinkingFrequency = drinkingFrequency;
            DrinkingAmount = drinkingAmount;
            DrinkingDetail = drinkingDetail;
            IsDeleted = isDeleted;
            SmokingStartAge = smokingStartAge;
            SmokingEndAge = smokingEndAge;
            SmokingStartYear = smokingStartYear;
            SmokingEndYear = smokingEndYear;
            TotalSmokingDuration = totalSmokingDuration;
            BrinkmanNumber = brinkmanNumber;
            Age = age;
        }

        public PtSmokingRelatedModel(int hpId, int smokingStatus, int drinkingFrequency)
        {
            HpId = hpId;
            SmokingStatus = smokingStatus;
            DrinkingFrequency = drinkingFrequency;
        }

        public int HpId { get; private set; }

        public long PtId { get; private set; }

        public long SeqNo { get; private set; }

        public int SmokingStatus { get; private set; }

        public string? SmokingDetail { get; private set; }

        public int SmokingDailyCount { get; private set; }

        public int SmokingDuration { get; private set; }

        public int SmokingDurationUnit { get; private set; }

        public int DrinkingFrequency { get; private set; }

        public int DrinkingAmount { get; private set; }

        public string? DrinkingDetail { get; private set; }

        public int IsDeleted { get; private set; }

        public int? SmokingStartAge { get; private set; }

        public int? SmokingEndAge { get; private set; }

        public int SmokingStartYear { get; private set; }

        public int SmokingEndYear { get; private set; }

        public int TotalSmokingDuration { get; private set; }

        public long BrinkmanNumber { get; private set; }

        public int Age { get; private set; }
    }
}