﻿using Domain.Common;
using Domain.Models.Family;

namespace Domain.Models.KarteMedicalHistory
{
    public interface IKarteMedicalHistoryRepository : IRepositoryBase
    {
        List<PtPregnancyRelatedModel> GetPregnantList(int hpId, long ptId);

        List<PtSmokingRelatedModel> GetPtSocialHistoryList(int hpId, long ptId);

        List<PtFamilyRekiModel> GetFamilyList(int hpId, long ptId);
    }
}
