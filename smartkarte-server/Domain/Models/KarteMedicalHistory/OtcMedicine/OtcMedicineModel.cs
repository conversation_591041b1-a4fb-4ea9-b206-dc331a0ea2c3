﻿using System.Text.Json.Serialization;
using Domain.Models.SpecialNote.ImportantNote;

namespace Domain.Models.KarteMedicalHistory
{
    public class OtcMedicineModel
    {
        [JsonConstructor]
        public OtcMedicineModel(List<PtOtcDrugModel> otcDrugItems)
        {
            OtcDrugItems = otcDrugItems;
        }

        public OtcMedicineModel()
        {
            OtcDrugItems = new List<PtOtcDrugModel>();
        }

        public List<PtOtcDrugModel> OtcDrugItems { get; private set; }
    }
}
