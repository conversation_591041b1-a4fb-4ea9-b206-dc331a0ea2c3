﻿namespace Domain.Models.KarteMedicalHistory
{
    public class PtPregnancyRelatedModel
    {
        public PtPregnancyRelatedModel() { }
        public PtPregnancyRelatedModel(int hpId, long ptId, int pregnancyStatus, int breastfeedStatus, int isDeleted)
        {
            HpId = hpId;
            PtId = ptId;
            PregnancyStatus = pregnancyStatus;
            BreastfeedStatus = breastfeedStatus;
            IsDeleted = isDeleted;
        }

        public PtPregnancyRelatedModel(int hpId, int pregnancyStatus, int breastfeedStatus)
        {
            HpId = hpId;
            PregnancyStatus = pregnancyStatus;
            BreastfeedStatus = breastfeedStatus;
        }

        public int HpId { get; private set; }

        public long PtId { get; private set; }

        public int PregnancyStatus { get; private set; }

        public int BreastfeedStatus { get; private set; }

        public int IsDeleted { get; private set; }
    }
}