﻿using Domain.Models.Family;

namespace Domain.Models.KarteMedicalHistory
{
    public class FamilyDto
    {
        public FamilyDto(FamilyModel model)
        {
            FamilyId = model.FamilyId;
            PtId = model.PtId;
            SeqNo = model.SeqNo;
            ZokugaraCd = model.ZokugaraCd;
            FamilyPtNum = model.FamilyPtNum;
            Name = model.Name;
            KanaName = model.KanaName;
            Sex = model.Sex;
            ListPtFamilyRekis = model.ListPtFamilyRekis;
        }

        public long FamilyId { get; private set; }

        public long PtId { get; private set; }

        public long SeqNo { get; private set; }

        public string ZokugaraCd { get; private set; }

        public long FamilyPtNum { get; private set; }

        public string Name { get; private set; }

        public string KanaName { get; private set; }

        public int Sex { get; private set; }

        public List<PtFamilyRekiModel> ListPtFamilyRekis { get; private set; }
    }
}
