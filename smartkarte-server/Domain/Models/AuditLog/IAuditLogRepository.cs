﻿using Domain.Common;

namespace Domain.Models.AuditLog
{
    public interface IAuditLogRepository : IRepositoryBase
    {
        bool SaveAuditLog(int userId, AuditTrailLogModel auditTrailLogModel);

        void AddAuditTrailLog(int userId, int isOperator, string operatorName, ArgumentModel arg, int hpId);

        void AddListAuditTrailLog(int userId, int isOperator, string operatorName, List<ArgumentModel> args, int hpId);
    }
}
