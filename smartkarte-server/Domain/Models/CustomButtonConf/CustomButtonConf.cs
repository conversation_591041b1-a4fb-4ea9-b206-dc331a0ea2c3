﻿namespace Domain.Models.CustomButtonConf
{
    public class CustomButtonConfModel
    {
        public CustomButtonConfModel(int hpId, long id, string name, int isUrl, string path, string workdir,
            string pattern, int? sort, string filename, string? urlImage, string? urlGenerate)
        {
            HpId = hpId;
            Id = id;
            Name = name;
            IsUrl = isUrl;
            Path = path;
            Workdir = workdir;
            Pattern = pattern;
            Sort = sort;
            Filename = filename;
            UrlImage = urlImage;
            UrlGenerate = urlGenerate;
        }
        
        public int HpId { get; set; }
        public long Id { get; private set; }
        public string Name { get; private set; }
        public int IsUrl { get; private set; }
        public string Path { get; private set; }
        public string Workdir { get; private set; }
        public string Pattern { get; private set; }
        public int? Sort { get; private set; }
        public string Filename { get; private set; }
        public  string UrlImage { get;  set; }
        
        public string UrlGenerate { get;  set; }
    }
}

