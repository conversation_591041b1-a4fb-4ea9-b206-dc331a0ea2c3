using Domain.Common;

namespace Domain.Models.CustomButtonConf
{
    public interface ICustomButtonConfRepository : IRepositoryBase
    {
        List<CustomButtonConfModel> GetCustomButtonConfList(int hpId, int ptId, long raiinNo);
        CustomButtonConfModel GetDetailCustomButtonConfList(int id, int hpId, int ptId, long raiinNo);
        bool CheckExistCustomButtonConf(long id, int hpId);
        long SaveCustomButtonConf(CustomButtonConfModel customButtonConfModel);
        bool DeleteCustomButtonConf(long id, int hpId);
        bool UpdateSortCustomButtonConf(List<CustomButtonConfModel> customButtonConfModels);
    }
}