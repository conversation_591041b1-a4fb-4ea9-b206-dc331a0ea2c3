﻿namespace Domain.Models.ConfirmOnline
{
    public class UpdateConfirmOnlineModel
    {
        public UpdateConfirmOnlineModel(int hpId, long aiChartPtId, long portalPtId, int onlineConfirmHisId, int userId)
        {
            HpId = hpId;
            AiChartPtId = aiChartPtId;
            PortalPtId = portalPtId;
            OnlineConfirmHisId = onlineConfirmHisId;
            UserId = userId; 
        }
        public int HpId { get; private set; }
        public long AiChartPtId { get; private set; }
        public long PortalPtId { get; private set; }
        public int OnlineConfirmHisId { get; private set; }
        public int UserId { get; private set; }
    }
}
