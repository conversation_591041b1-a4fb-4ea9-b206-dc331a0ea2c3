﻿namespace Domain.Models.ConfirmOnline
{
    public class UpdateMappingModelDto
    {
        public UpdateMappingModelDto(bool isSucces ,bool isContinue, List<long>? deletedRaiinNoList = null)
        {
            IsContinue = isContinue;
            IsSucces = isSucces;
            DeletedRaiinNoList = deletedRaiinNoList;
        }
        public bool IsSucces { get; private set; }
        public bool IsContinue { get; private set; }
        public List<long>? DeletedRaiinNoList { get; private set; }
    }
}
