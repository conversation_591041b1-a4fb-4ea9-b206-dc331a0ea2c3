﻿namespace Domain.Models.EpsChkDetail;

public class EpsChkDetailModel
{
    /// <summary>
    /// 医療機関識別ID
    ///     EPS_CHK.HP_ID
    /// </summary>
    public int HpId { get; set; }

    /// <summary>
    /// 患者ID
    ///     EPS_CHK.PT_ID
    /// </summary>
    public long PtId { get; set; }

    /// <summary>
    /// 来院番号
    ///     EPS_CHK.RAIIN_NO
    /// </summary>
    public long RaiinNo { get; set; }

    /// <summary>
    /// 連番
    ///     EPS_CHK.SEQ_NO
    /// </summary>
    public long SeqNo { get; set; }

    /// <summary>
    /// メッセージID
    ///     チェック結果内の連番
    /// </summary>
    public string MessageId { get; set; } = string.Empty;

    /// <summary>
    /// メッセージ分類
    /// </summary>
    public string MessageCategory { get; set; } = string.Empty;

    /// <summary>
    /// 対象医薬品・成分名称
    /// </summary>
    public string PharmaceuticalsIngredientName { get; set; } = string.Empty;

    /// <summary>
    /// メッセージ
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 今回＿対象薬品コード種別
    /// </summary>
    public string TargetPharmaceuticalCodeType { get; set; } = string.Empty;

    /// <summary>
    /// 今回＿対象薬品コード
    /// </summary>
    public string TargetPharmaceuticalCode { get; set; } = string.Empty;

    /// <summary>
    /// 今回＿対象薬品名称
    /// </summary>
    public string TargetPharmaceuticalName { get; set; } = string.Empty;

    /// <summary>
    /// 今回＿調剤数量
    /// </summary>
    public string TargetDispensingQuantity { get; set; } = string.Empty;

    /// <summary>
    /// 今回＿用法
    /// </summary>
    public string TargetUsage { get; set; } = string.Empty;

    /// <summary>
    /// 今回＿剤形
    /// </summary>
    public string TargetDosageForm { get; set; } = string.Empty;

    /// <summary>
    /// 過去＿調剤実施日
    /// </summary>
    public string PastDate { get; set; } = string.Empty;

    /// <summary>
    /// 過去＿対象薬品コード種別
    /// </summary>
    public string PastPharmaceuticalCodeType { get; set; } = string.Empty;

    /// <summary>
    /// 過去＿対象薬品コード
    /// </summary>
    public string PastPharmaceuticalCode { get; set; } = string.Empty;

    /// <summary>
    /// 過去＿対象薬品名称
    /// </summary>
    public string PastPharmaceuticalName { get; set; } = string.Empty;

    /// <summary>
    /// 過去＿医療機関名称
    /// </summary>
    public string PastMedicalInstitutionName { get; set; } = string.Empty;

    /// <summary>
    /// 過去＿保険薬局名称
    /// </summary>
    public string PastInsurancePharmacyName { get; set; } = string.Empty;

    /// <summary>
    /// 過去＿調剤数量
    /// </summary>
    public string PastDispensingQuantity { get; set; } = string.Empty;

    /// <summary>
    /// 過去＿用法
    /// </summary>
    public string PastUsage { get; set; } = string.Empty;

    /// <summary>
    /// 過去＿剤形
    /// </summary>
    public string PastDosageForm { get; set; } = string.Empty;

    /// <summary>
    /// 投与理由コメント
    /// </summary>
    public string Comment { get; set; } = string.Empty;
}