﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.Models.KarteFile
{
    public class GetListKarteFileModel
    {
        public GetListKarteFileModel(int hpId, long ptId, int categoryCd, string dspFileName, string fileName, int getDate, int sinDate, string memo, string s3FileName, string filePath, int fileId)
        {
            HpId = hpId;
            PtId = ptId;
            CategoryCd = categoryCd;
            DspFileName = dspFileName;
            FileName = fileName;
            GetDate = getDate;
            SinDate = sinDate;
            S3FileName = s3FileName;
            Memo = memo;
            FilePath = filePath;
            FileId = fileId;
        }

        public int HpId { get; set; }
        public long PtId { get; set; }
        public int CategoryCd { get; set; }
        public string DspFileName { get; set; }
        public string FileName { get; set; }
        public int GetDate { get; set; }
        public int SinDate { get; set; }
        public string S3FileName { get; set; }
        public string Memo { get; set; }
        public string FilePath { get; set; }
        public int FileId { get; set; }
    }
}
