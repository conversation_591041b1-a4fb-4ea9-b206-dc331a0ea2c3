﻿using Microsoft.AspNetCore.Http;

namespace Domain.Models.KarteFile
{
    public class AddKarteFileModel
    {

        public long PtId { get; set; }

        public string FileName { get; set; }

        public string S3FileName { get; set; } = String.Empty;

        public string DspFileName { get; set; }

        public string Memo { get; set; } = String.Empty;

        public int CategoryCd { get; set; }

        public bool OverWrite { get; set; }

        public int UploadDate { get; set; }

        public int SinDate { get; set; }

        public long RaiinNo { get; set; }

        public int? HokenId { get; set; }

        public int? FileNo {  get; set; } 
    }
}
