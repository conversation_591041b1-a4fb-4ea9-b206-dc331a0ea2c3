﻿using Domain.Common;
using Domain.Models.PatientInfor;

namespace Domain.Models.KarteFile
{
    public interface IKarteFileRepository : IRepositoryBase
    {
        bool UpdateKarteFileInfo(int hpId, long ptId, int userId, UpdateKarteFileModel updateKarteFileModel);

        bool MoveKarteFileCategory(int hpId, long ptId, int userId, UpdateKarteFileModel updateKarteFileModel);

        AttachKarteFileModel? GetKarteFileInfo(int hpId, long ptId, int fileId);

        bool AttachKarteFile(int hpId, long ptId, int userId, AttachKarteFileModel attachKarteFileModel);

        List<GetPatientFileModel> GetCopyFilesByName(int hpId, long ptId, string fileName);

        FileLinkModel GetLinkDownload(int hpId, long ptId, int fileId);

        bool AddKarteFile(List<AddKarteFileModel> listItem, int hpId, int userId);

        List<GetListKarteFileModel> GetKarteFiles(int hpId, long ptId, int sinDate, long raiiNo);

        bool UpdateKarteFiles(int hpId, long ptId, int userId, List<UpdateKarteFileSoapModel> updateKarteFileSoapModels);

        bool DeleteKarteFile(int hpId, long ptId, List<int> fileIds);

        Dictionary<int, string> GetS3FileNameOfKarteFile(int hpId, long ptId, List<int> fileIds);

        Dictionary<int, string> GetS3FileNameOfKarteFile(int hpId, long ptId, int sinDate, long raiiNo);

        bool UpdateStatusSaveMedical(int hpId, long ptId, int sinDate, long raiiNo);

        List<GetListFileVersionModel> GetListVersionFile(int hpId, long ptId, int sinDate, long raiinNo, int editionVersion);

    }
}
