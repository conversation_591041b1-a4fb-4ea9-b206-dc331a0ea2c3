
namespace Domain.Models.KarteFile;

public class AttachKarteFileModel
{
    public AttachKarteFileModel()
    {
        FileId = 0;
        DspFileName = string.Empty;
        FileName = string.Empty;
        S3FileName = string.Empty;
        GetDate = 0;
        Memo = string.Empty;
        CategoryCd = 0;
        SinDate = 0;
        HokenId = 0;
        RaiinNo = 0;
    }

    public AttachKarteFileModel(int fileId, string dspFileName, string fileName, string s3FileName, int getDate, string memo, int categoryCd, int sinDate, int hokenId, long raiinNo)
    {
        FileId = fileId;
        DspFileName = dspFileName;
        FileName = fileName;
        S3FileName = s3FileName;
        GetDate = getDate;
        Memo = memo;
        CategoryCd = categoryCd;
        SinDate = sinDate;
        HokenId = hokenId;
        RaiinNo = raiinNo;
    }

    public int FileId { get; set; }
    
    public string DspFileName { get; set; }
    
    public string FileName { get; set; }
    
    public string S3FileName { get; set; }
    
    public int GetDate { get; set; }

    public string Memo { get; set; }

    public int CategoryCd { get; set; }
    
    public int SinDate { get; set; }
    
    public int HokenId { get; set; }
    
    public long RaiinNo { get; set; }
}