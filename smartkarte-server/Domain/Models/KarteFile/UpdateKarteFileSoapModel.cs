﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.Models.KarteFile
{
    public class UpdateKarteFileSoapModel
    {
        public UpdateKarteFileSoapModel(int fileId, string dspFileName, string memo, int upLoadDate, int isDeleted)
        {
            FileId = fileId;
            DspFileName = dspFileName;
            Memo = memo;
            UploadDate = upLoadDate;
            IsDeleted = isDeleted;
        }

        public int FileId { get; set; }

        public string DspFileName { get; set; }

        public string Memo { get; set; }

        public int UploadDate { get; private set; }

        public int IsDeleted { get; set; }
    }
}
