﻿namespace Domain.Models.KarteFile
{
    public class GetListFileVersionModel
    {
        public GetListFileVersionModel(int hpId,
            long ptId, int categoryCd, string? dspFileName,
            int getDate, int sinDate, string? memo, string? s3FileName,
            int fileId, int fileNo, string? fileName,int createId,
            int isDeleted, DateTime createDate, long raiinNo, int hokenId,
            string? createMachine,DateTime updateDate,int updateId,string? updateMachine
            )
        {
            HpId = hpId;
            PtId = ptId;
            CategoryCd = categoryCd;
            DspFileName = dspFileName;
            GetDate = getDate;
            SinDate = sinDate;
            S3FileName = s3FileName;
            Memo = memo;
            FileId = fileId;
            FileNo = fileNo;
            FileName = fileName;
            CreateId = createId;
            IsDeleted = isDeleted;
            CreateDate = createDate;
            RaiinNo = raiinNo;
            HokenId = hokenId;
            CreateMachine = createMachine;
            UpdateDate = updateDate;
            UpdateId = updateId;
            UpdateMachine = updateMachine;
        }

        public int HpId { get; set; }
        public long PtId { get; set; }
        public int CategoryCd { get; set; }
        public string? DspFileName { get; set; }
        public int GetDate { get; set; }
        public int SinDate { get; set; }
        public string? S3FileName { get; set; }
        public string? Memo { get; set; }
        public int FileId { get; set; }
        public int FileNo { get; set; }
        public string? FileName { get; set; }
        public string? CreateMachine { get; set; }
        public string? UpdateMachine { get; set; }
        public int IsDeleted { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }
        public long RaiinNo { get; set; }
        public int HokenId { get; set; }
        public int UpdateId { get; set; }
        public int CreateId { get; set; }

    }
}
