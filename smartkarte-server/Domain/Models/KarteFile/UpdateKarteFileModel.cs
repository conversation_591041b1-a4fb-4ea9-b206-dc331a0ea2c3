
namespace Domain.Models.KarteFile;

public class UpdateKarteFileModel
{
    public UpdateKarteFileModel(int fileId, string dspFileName, int getDate, string memo, int categoryCd)
    {
        FileId = fileId;
        DspFileName = dspFileName;
        GetDate = getDate;
        Memo = memo;
        CategoryCd = categoryCd;
    }

    public UpdateKarteFileModel(int fileId, int categoryCd)
    {
        FileId = fileId;
        CategoryCd = categoryCd;
        DspFileName = String.Empty;
        GetDate = 0;
        Memo = String.Empty;;
    }


    public int FileId { get; set; }
    
    public string DspFileName { get; set; }
    
    public int GetDate { get; set; }

    public string Memo { get; set; }

    public int CategoryCd { get; set; }
}