namespace Domain.Models.KensaCenterPartnership;

public class KensaCenterPartnershipModel
{
    public KensaCenterPartnershipModel()
    {
        HpId = 0;
        CenterCd = string.Empty;
    }
    
    public KensaCenterPartnershipModel(int hpId, string centerCd)
    {
        HpId = hpId;
        CenterCd = centerCd;
    }

    // Save 用
    public KensaCenterPartnershipModel(int hpId, string centerCd, int startDate, int endDate)
    {
        HpId = hpId;
        CenterCd = centerCd;
        StartDate = startDate;
        EndDate = endDate;
    }

    // Get 用
    public KensaCenterPartnershipModel(int? hpId, string centerCd, int? startDate, int? endDate, DateTime? masterUpdateDate, string centerKey, string centerName, string dspCenterName)
    {
        HpId = hpId;
        CenterCd = centerCd;
        StartDate = startDate;
        EndDate = endDate;
        MasterUpdateDate = masterUpdateDate;
        CenterKey = centerKey;
        CenterName = centerName;
        DspCenterName = dspCenterName;
    }

    public int? HpId { get; private set; }
    public string CenterCd { get; private set; }
    public int? StartDate { get; private set; }
    public int? EndDate { get; private set; }
    public DateTime? MasterUpdateDate { get; private set; }
    public string? CenterKey { get; private set; }
    public string? CenterName { get; private set; }
    public string? DspCenterName { get; private set; }
}