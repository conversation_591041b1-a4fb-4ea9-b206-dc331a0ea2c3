﻿using Domain.Common;
using Domain.Models.Eps;
using Domain.Models.Insurance;
using Domain.Models.KarteFilterMst;
using Domain.Models.Receipt.Recalculation;
using Domain.Models.Reception;

namespace Domain.Models.HistoryOrder
{
    public interface IHistoryOrderRepository : IRepositoryBase
    {
        (int, int, List<HistoryOrderModel>) GetList(int hpId, int userId, long ptId, int sinDate, int offset, int limit, List<Tuple<long, bool>> raiinNos, int isShowApproval = 0, int type = 0, string? keyWord = null, List<int>? treatmentDepartmentIds = null, bool hasSOAP = false, List<int>? odrKouiKbns = null, List<int>? userIds = null, List<InsuranceSummaryModel>? insuranceModels = null);

        (int totalCount, int totalKeyWordMatched, List<HistoryOrderModel> historyOrderModelList) GetList(int hpId, long ptId, int sinDate, int startDate, int endDate, int isDeleted, int isShowApproval = 0, int type = 0, string? keyWord = null, bool inCludeDraf = false, List<InsuranceSummaryModel>? insuranceModels = null);

        (int, List<HistoryOrderVersionModel>) GetListVersion(long raiinNo, int hpId, int userId, long ptId, int sinDate, int offset, int limit, int type = 0, bool includeDraft = false);
        Dictionary<long, List<HistoryOrderVersionModel>> GetListVersionAllRaiinNo(int hpId, long ptId, int sinDate, int type = 0, bool includeDraft = false);
        
        public (int, ReceptionModel) Search(int hpId, int userId, long ptId, int sinDate, int currentIndex, int filterId, int isDeleted, string keyWord, int searchType, bool isNext, List<Tuple<long, bool>> raiinNos);

        KarteFilterMstModel GetFilter(int hpId, int userId, int filterId);

        bool CheckExistedFilter(int hpId, int userId, int filterId);

        long GetHistoryIndex(int hpId, long ptId, long raiinNo, int userId, int filterId, int isDeleted, List<Tuple<long, bool>> raiinNos);

        List<HistoryOrderModel> GetListByRaiin(int hpId, int userId, long ptId, int sinDate, int filterId, int isDeleted, long raiin, byte flag, List<Tuple<long, bool>> raiinNos, int isShowApproval);

        (int totalCount, List<HistoryOrderModel> historyOrderModels) GetOrdersForOneOrderSheetGroup(int hpId, long ptId, int odrKouiKbn, int grpKouiKbn, int sinDate, int offset, int limit);

        List<SinKouiListModel> GetSinkouiList(int hpId, long ptId, List<int> sinDateList, List<long> raiinNoList, List<int> mainPidList);
    }
}
