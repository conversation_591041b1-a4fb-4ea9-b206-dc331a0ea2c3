﻿namespace Domain.Models.EpsReq;

public class EpsReqModel
{
    public int HpId { get; set; }
    /// <summary>
    /// 要求送信日
    ///     要求送信時点のシステム日付(yyyymmdd)
    /// </summary>
    public int ReqDate { get; set; }

    /// <summary>
    /// 診療日内連番
    ///     req_date内の連番
    /// </summary>
    public long DateSeqNo { get; set; }

    /// <summary>
    /// ファイル識別子
    ///     <要求作成日時(yyyymmddhhmmsszzz)>+<端末名>
    /// </summary>
    public string ArbitraryFileIdentifier { get; set; } = string.Empty;

    /// <summary>
    /// 患者ID
    /// </summary>
    public long PtId { get; set; }

    /// <summary>
    /// 診療日
    /// </summary>
    public int SinDate { get; set; }

    /// <summary>
    /// 来院番号
    /// </summary>
    public long RaiinNo { get; set; }

    /// <summary>
    /// 処方箋ID
    /// </summary>
    public string PrescriptionId { get; set; } = string.Empty;

    /// <summary>
    /// 調剤結果ID
    /// </summary>
    public string DispensingResultId { get; set; } = string.Empty;

    /// <summary>
    /// 要求タイプ
    ///     1:重複投薬等チェック事前処理
    ///     2:重複投薬等チェック
    ///     3:電子処方箋登録
    ///     4:紙の処方箋登録
    ///     5:処方箋情報の変更
    ///     6:処方箋情報の取消	
    ///     7:処方箋控えの取得	
    ///     8:調剤結果取得
    ///     9:調剤結果一括取得	
    ///     10:調剤結果一括取得の内の個々の調剤結果取得
    ///     11:処方箋ID検索
    /// </summary>
    public int ReqType { get; set; }

    /// <summary>
    /// 状態
    ///     0:未処理
    ///     1:処理中
    ///     8:エラー
    ///     9:正常終了
    /// </summary>
    public int Status { get; set; }

    /// <summary>
    /// 処理結果コード
    ///     結果ファイルにMessageBodyがある場合、MessageBody.ProcessingResultCode
    ///     ない場合MessageHeader.ErrorCode
    ///     
    ///     ※各コードについては
    ///     \\192.168.1.102\share\医事関連\オンライン資格確認\ONS資料\電子処方箋\別紙6-1_処理結果コード（オンライン資格確認、電子処方箋管理サービス）_Ver3.05.pdf
    ///     を参照"
    /// </summary>
    public string ResultCode { get; set; } = string.Empty;

    /// <summary>
    /// 処理結果メッセージ
    ///     結果ファイルにMessageBodyがある場合、MessageBody.ProcessingResultMessage
    ///     MessageBodyがない場合MessageHeader.ErrorMessage
    ///     ただし、エラーファイルが返ってきた場合はエラーファイルのデータ
    /// </summary>
    public string ResultMessage { get; set; } = string.Empty;

    /// <summary>
    /// 結果ファイル
    ///     結果ファイルのデータ
    /// </summary>
    public string Result { get; set; } = string.Empty;

    /// <summary>
    /// 作成日時
    /// </summary>
    public DateTime CreateDate { get; set; }

    /// <summary>
    /// 作成ID
    /// </summary>
    public int CreateId { get; set; }

    /// <summary>
    /// 作成端末
    /// </summary>
    public string CreateMachine { get; set; } = string.Empty;

    /// <summary>
    /// 更新日時
    /// </summary>
    public DateTime UpdateDate { get; set; }

    /// <summary>
    /// 更新ID
    /// </summary>
    public int UpdateId { get; set; }

    /// <summary>
    /// 更新端末
    /// </summary>
    public string UpdateMachine { get; set; } = string.Empty;

    public EpsReqModel(int hpId, int reqDate, long dateSeqNo, string arbitraryFileIdentifier, long ptId, int sinDate, long raiinNo, string prescriptionId, string dispensingResultId, int reqType, int status, string resultCode, string resultMessage, string result, DateTime createDate, int createId, string createMachine, DateTime updateDate, int updateId, string updateMachine)
    {
        HpId = hpId;
        ReqDate = reqDate;
        DateSeqNo = dateSeqNo;
        ArbitraryFileIdentifier = arbitraryFileIdentifier;
        PtId = ptId;
        SinDate = sinDate;
        RaiinNo = raiinNo;
        PrescriptionId = prescriptionId;
        DispensingResultId = dispensingResultId;
        ReqType = reqType;
        Status = status;
        ResultCode = resultCode;
        ResultMessage = resultMessage;
        Result = result;
        CreateDate = createDate;
        CreateId = createId;
        CreateMachine = createMachine;
        UpdateDate = updateDate;
        UpdateId = updateId;
        UpdateMachine = updateMachine;
    }
}