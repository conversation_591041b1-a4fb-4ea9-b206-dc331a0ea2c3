using System.Text.Json.Serialization;
using Domain.Models.SpecialNote.ImportantNote;

namespace Domain.Models.KarteAllergy;

public class KarteAllergyModel
{
    [JsonConstructor]
    public KarteAllergyModel(List<PtAlrgyFoodModel> alrgyFoodItems, List<PtAlrgyElseModel> alrgyElseItems, List<PtAlrgyDrugModel> alrgyDrugItems)
    {
        AlrgyFoodItems = alrgyFoodItems;
        AlrgyElseItems = alrgyElseItems;
        AlrgyDrugItems = alrgyDrugItems;
    }

    public KarteAllergyModel()
    {
        AlrgyFoodItems = new List<PtAlrgyFoodModel>();
        AlrgyElseItems = new List<PtAlrgyElseModel>();
        AlrgyDrugItems = new List<PtAlrgyDrugModel>();
    }

    public List<PtAlrgyFoodModel> AlrgyFoodItems { get; private set; }
    public List<PtAlrgyElseModel> AlrgyElseItems { get; private set; }
    public List<PtAlrgyDrugModel> AlrgyDrugItems { get; private set; }
}