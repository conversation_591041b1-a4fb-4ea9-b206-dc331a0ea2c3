﻿namespace Domain.Models.Diseases
{
    public class GetListRecentRegisteredDiseaseModel
    {
        public GetListRecentRegisteredDiseaseModel(string byomeiCd, int sikkanKbn, int nanbyoCd, string byomei, string hosokuCmt)
        {
            ByomeiCd = byomeiCd;
            SikkanKbn = sikkanKbn;
            NanbyoCd = nanbyoCd;
            Byomei = byomei;
            HosokuCmt = hosokuCmt;
        }
        public string ByomeiCd { get; private set; }
        public int SikkanKbn { get; private set; }
        public int NanbyoCd { get; private set; }
        public string Byomei { get; private set; }
        public string HosokuCmt { get; private set; }
        public string FullByomei { get => string.Concat(Byomei, HosokuCmt); }
    }
}
