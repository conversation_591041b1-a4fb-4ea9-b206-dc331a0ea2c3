﻿using Domain.Models.EpsDispensing;
using Domain.Models.Ka;
using Domain.Models.PatientInfor;
using Domain.Models.User;
using Helper.Common;

namespace Domain.Models.EpsPrescription;

public class EpsPrescriptionModel
{
    public List<EpsDispensingModel> EpsDispensingModel { get; set; } = new();

    public KaMstModel KaInf { get; set; }

    public UserMstModel TantoInf { get; set; }

    public PatientInforModel PtInf { get; set; }

    public EpsDispensingModel EpsDispensing { get; set; }

    public List<int> HokenKbns { get; set; }

    public EpsPrescriptionModel()
    {
        HokensyaNo = string.Empty;
        Kigo = string.Empty;
        Bango = string.Empty;
        EdaNo = string.Empty;
        KohiFutansyaNo = string.Empty;
        KohiJyukyusyaNo = string.Empty;
        PrescriptionId = string.Empty;
        AccessCode = string.Empty;
        PrescriptionDocument = string.Empty;
        DeletedDate = new();
        PtInf = new();
        EpsDispensing = new();
        KaSName = string.Empty;
        TantoName = string.Empty;
        KaInf = new();
        TantoInf = new();
        HokenKbns = new();
    }

    public EpsPrescriptionModel(int hpId, long ptId, long raiinNo, long seqNo, int refileCount, int sinDate, string hokensyaNo, string kigo, string bango, string edaNo, string kohiFutansyaNo, string kohiJyukyusyaNo, string prescriptionId,
                                string accessCode, int issueType, string prescriptionDocument, int status, int deletedReason, DateTime? deletedDate, int kaId, int tantoId, DateTime createDate, int createId, DateTime updateDate, int updateId, List<int> hokenKbn)
    {
        HpId = hpId;
        PtId = ptId;
        RaiinNo = raiinNo;
        SeqNo = seqNo;
        RefileCount = refileCount;
        SinDate = sinDate;
        HokensyaNo = hokensyaNo;
        Kigo = kigo;
        Bango = bango;
        EdaNo = edaNo;
        KohiFutansyaNo = kohiFutansyaNo;
        KohiJyukyusyaNo = kohiJyukyusyaNo;
        PrescriptionId = prescriptionId;
        AccessCode = accessCode;
        IssueType = issueType;
        PrescriptionDocument = prescriptionDocument;
        Status = status;
        DeletedReason = deletedReason;
        DeletedDate = deletedDate;
        KaId = kaId;
        TantoId = tantoId;
        CreateDate = createDate;
        CreateId = createId;
        UpdateDate = updateDate;
        UpdateId = updateId;
        PtInf = new();
        EpsDispensing = new();
        KaSName = string.Empty;
        TantoName = string.Empty;
        KaInf = new();
        TantoInf = new();
        HokenKbns = hokenKbn;
    }

    public EpsPrescriptionModel(int hpId, long ptId, long raiinNo, long seqNo, int refileCount, int sinDate, string hokensyaNo, string kigo, string bango, string edaNo, string kohiFutansyaNo, string kohiJyukyusyaNo, string prescriptionId,
                                string accessCode, int issueType, string prescriptionDocument, int status, int deletedReason, DateTime? deletedDate, int kaId, int tantoId, DateTime createDate, int createId, DateTime updateDate, int updateId, PatientInforModel ptInf, EpsDispensingModel epsDispensing, string kaSName, string tantoName)
    {
        HpId = hpId;
        PtId = ptId;
        RaiinNo = raiinNo;
        SeqNo = seqNo;
        RefileCount = refileCount;
        SinDate = sinDate;
        HokensyaNo = hokensyaNo;
        Kigo = kigo;
        Bango = bango;
        EdaNo = edaNo;
        KohiFutansyaNo = kohiFutansyaNo;
        KohiJyukyusyaNo = kohiJyukyusyaNo;
        PrescriptionId = prescriptionId;
        AccessCode = accessCode;
        IssueType = issueType;
        PrescriptionDocument = prescriptionDocument;
        Status = status;
        DeletedReason = deletedReason;
        DeletedDate = deletedDate;
        KaId = kaId;
        TantoId = tantoId;
        PtInf = ptInf;
        EpsDispensing = epsDispensing;
        KaSName = kaSName;
        TantoName = tantoName;
        CreateDate = createDate;
        CreateId = createId;
        UpdateDate = updateDate;
        UpdateId = updateId;
        KaInf = new();
        TantoInf = new();
        HokenKbns = new();
    }

    public EpsPrescriptionModel ChangeHokenKbns(List<int> hokenKbns)
    {
        HokenKbns = hokenKbns;
        return this;
    }

    public int HpId { get; set; }

    public int Id { get; set; }

    public long PtId { get; set; }

    public string KaSName { get; set; } = string.Empty;

    public string TantoName { get; set; } = string.Empty;

    public long PtNum
    {
        get => PtInf.PtNum;
    }

    public string PtNumDisplay
    {
        get => PtInf.PtNum == 0 ? "" : PtInf.PtNum.ToString();
    }

    public string PtName
    {
        get => PtInf.Name;
    }

    public long RaiinNo { get; set; }

    public long SeqNo { get; set; }

    public int RefileCount { get; set; }

    public int SinDate { get; set; }

    public string SinDateDisplay
    {
        get => CIUtil.SDateToShowSDate(SinDate);
    }

    public string HokensyaNo { get; set; } = string.Empty;

    public string Kigo { get; set; } = string.Empty;

    public string Bango { get; set; } = string.Empty;

    public string EdaNo { get; set; } = string.Empty;

    public string KohiFutansyaNo { get; set; } = string.Empty;

    public string KohiJyukyusyaNo { get; set; } = string.Empty;

    public string PrescriptionId { get; set; } = string.Empty;

    public string AccessCode { get; set; } = string.Empty;

    public int IssueType { get; set; }

    public string PrescriptionDocument { get; set; } = string.Empty;

    public int Status { get; set; }

    public int DeletedReason { get; set; }

    public DateTime? DeletedDate { get; set; }

    public int KaId { get; set; }

    public int TantoId { get; set; }

    public string HokenDisplay
    {
        get
        {
            if (CheckDefaultValue()) return "";
            string hokensyaNo = string.IsNullOrEmpty(HokensyaNo) ? EpsDispensing?.HokensyaNo ?? string.Empty : HokensyaNo;
            string bango = string.IsNullOrEmpty(Bango) ? EpsDispensing?.Bango ?? string.Empty : Bango;
            string kigo = string.IsNullOrEmpty(Kigo) ? EpsDispensing?.Kigo ?? string.Empty : Kigo;
            string edaNo = string.IsNullOrEmpty(EdaNo) ? EpsDispensing?.EdaNo ?? string.Empty : EdaNo;
            string kohiFutansyaNo = string.IsNullOrEmpty(KohiFutansyaNo) ? EpsDispensing?.KohiFutansyaNo ?? string.Empty : KohiFutansyaNo;
            string kohiJyukyusyaNo = string.IsNullOrEmpty(KohiJyukyusyaNo) ? EpsDispensing?.KohiJyukyusyaNo ?? string.Empty : KohiJyukyusyaNo;
            if (!string.IsNullOrEmpty(hokensyaNo))
            {
                return $"{hokensyaNo}/{kigo}・{bango}({edaNo})";
            }
            else
            {
                return string.IsNullOrEmpty(kohiFutansyaNo) && string.IsNullOrEmpty(kohiJyukyusyaNo) ? "" : $"{kohiFutansyaNo}/{kohiJyukyusyaNo}";
            }
        }
    }

    public string Refill
    {
        get
        {
            if (CheckDefaultValue()) return "";
            int? dispensingTimes = EpsDispensing?.DispensingTimes;
            int? refileCount = RefileCount;
            string time = string.Empty;
            string count = string.Empty;

            if (refileCount == 1)
            {
                count = "1";
                if (dispensingTimes == 1)
                {
                    time = "1";
                }
                else
                {
                    time = "0";
                }
            }
            else if (refileCount == 2)
            {
                count = "2";
                if (dispensingTimes == 1)
                {
                    time = "1";
                }
                else if (dispensingTimes == 2)
                {
                    time = "2";
                }
                else
                {
                    time = "0";
                }
            }
            else if (refileCount == 3)
            {
                count = "3";
                if (dispensingTimes == 1)
                {
                    time = "1";
                }
                else if (dispensingTimes == 2)
                {
                    time = "2";
                }
                else if (dispensingTimes == 3)
                {
                    time = "3";
                }
                else
                {
                    time = "0";
                }
            }
            else
            {
                count = "x";
                if (dispensingTimes == 1)
                {
                    time = "1";
                }
                else if (dispensingTimes == 2)
                {
                    time = "2";
                }
                else if (dispensingTimes == 3)
                {
                    time = "3";
                }
                else
                {
                    time = "0";
                }
            }
            return $"{time}/{count}回";
        }
    }

    public string IssueTypeDisplay
    {
        get
        {
            switch (IssueType)
            {
                case 1:
                    return "電子";
                case 2:
                    return "紙";
                default:
                    return "";
            }
        }
    }

    public string StatusDisplay
    {
        get
        {
            if (CheckDefaultValue()) return "";
            switch (Status)
            {
                case 1:
                    return "取消待ち";
                case 2:
                    return "取消中";
                case 3:
                    return "取消済み";
                default:
                    return "登録";
            }
        }
    }

    public string DeleteReasonDisplay
    {
        get
        {
            switch (DeletedReason)
            {
                case 1:
                    return "自動取消";
                case 2:
                    return "変更";
                case 3:
                    return "エラー";
                case 4:
                    return "登録中断";
                case 5:
                    return "手動取消";
                case 6:
                    return "紙の処方箋（引換番号なし）発行";
                default:
                    return "";
            }
        }
    }

    public int ResultType
    {
        get => EpsDispensing == null ? 0 : EpsDispensing.ResultType;
    }

    public string ResultTypeDisplay
    {
        get
        {
            if (EpsDispensing == null) return "";
            switch (EpsDispensing.ResultType)
            {
                case 1:
                    return "調剤済み";
                case 2:
                    return "調剤取消";
                case 3:
                    return "処方箋回収";
                case 4:
                    return "調剤中";
                default:
                    return "";
            }
        }
    }

    public string PharmacyName
    {
        get => EpsDispensing?.ReceptionPharmacyName ?? string.Empty;
    }

    public string DispensingDateDisplay
    {
        get => EpsDispensing == null ? "" : CIUtil.SDateToShowSDate(EpsDispensing.DispensingDate);
    }

    public int DispensingDate
    {
        get => EpsDispensing == null ? 0 : EpsDispensing.DispensingDate;
    }

    public DateTime EpsUpdateDateTime
    {
        get => EpsDispensing == null ? DateTime.MinValue : EpsDispensing.EpsUpdateDateTime;
    }

    public string MessageFlag
    {
        get
        {
            if (EpsDispensing == null) return "";
            switch (EpsDispensing.MessageFlg)
            {
                case 1:
                    return "";
                case 2:
                    return "未";
                case 3:
                    return "済";
                default:
                    return "";
            }
        }
    }

    public int CreateId { get; set; }

    public DateTime CreateDate { get; set; }

    public int UpdateId { get; set; }

    public DateTime UpdateDate { get; set; }

    public bool CheckDefaultValue()
    {
        return (Id == 0 || PtId == 0);
    }
}