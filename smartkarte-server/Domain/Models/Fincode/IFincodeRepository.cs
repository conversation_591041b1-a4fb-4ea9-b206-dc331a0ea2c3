using Domain.Common;
using Domain.Models.DenkaruApi;

namespace Domain.Models.Fincode
{
    public interface IFincodeRepository : IRepositoryBase
    {
        public Task<DenkaruGqlResponse<RequestReservePaymentData>> RequestReservePayment(int hpId, int reserveDetailId, int paymentType, int amount, int staffId);
        public Task<DenkaruGqlResponse<UpdateReservePaymentData>> UpdateReservePayment(long paymentClinicDetailId, int paymentType, int amount, int staffId);
        public Task<DenkaruGqlResponse<CancelReservePaymentData>> CancelReservePayment(long paymentClinicDetailId, int staffId);
    }
}