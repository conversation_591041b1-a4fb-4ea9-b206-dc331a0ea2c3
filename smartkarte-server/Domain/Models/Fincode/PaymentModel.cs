using System;
using System.Runtime.Serialization;


namespace Domain.Models.Fincode
{
    public class RequestReservePaymentData
    {
        public RequestReservePayment? RequestReservePayment { get; set; }
    }

    public class RequestReservePayment
    {
        public RequestReservePayment(long paymentClinicDetailId, string errorCode, string userMessage)
        {
            PaymentClinicDetailId = paymentClinicDetailId;
            ErrorCode = errorCode;
            UserMessage = userMessage;
        }

        public long PaymentClinicDetailId { get; set; }
        public string? ErrorCode { get; set; }
        public string? UserMessage { get; set; }
    }

    public class UpdateReservePaymentData
    {
        public UpdateReservePayment? UpdateReservePayment { get; set; }
    }

    public class UpdateReservePayment
    {
        public bool IsSuccess { get; set; }
    }

    public class CancelReservePaymentData
    {
        public CancelReservePayment? CancelReservePayment { get; set; }
    }

    public class CancelReservePayment
    {
        public bool IsSuccess { get; set; }
    }
}
