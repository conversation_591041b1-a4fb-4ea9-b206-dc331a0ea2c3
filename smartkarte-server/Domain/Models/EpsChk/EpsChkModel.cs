﻿using Domain.Models.EpsChkDetail;

namespace Domain.Models.EpsChk;

public class EpsChkModel
{
    /// <summary>
    /// 医療機関識別ID
    /// </summary>
    public int HpId { get; set; }

    /// <summary>
    /// 患者ID
    /// </summary>
    public long PtId { get; set; }

    /// <summary>
    /// 来院番号
    /// </summary>
    public long RaiinNo { get; set; }

    /// <summary>
    /// 連番
    /// </summary>
    public long SeqNo { get; set; }

    /// <summary>
    /// 診療日
    /// </summary>
    public int SinDate { get; set; }

    /// <summary>
    /// チェック結果
    ///     0:重複等あり(医師許可なし)
    ///     1:重複等あり(医師許可)
    ///     2:重複等なし
    /// </summary>
    public int CheckResult { get; set; }

    /// <summary>
    /// 同一処方箋発行医療機関チェックフラグ
    ///     1:自院分をチェック対象にしない
    ///     2:自院分をチェック対象にする
    /// </summary>
    public int SameMedicalInstitutionAlertFlg { get; set; }

    /// <summary>
    /// オンライン資格確認同意区分
    ///     0:オンライン資格確認端末による同意なし
    ///     1:オンライン資格確認端末による同意あり
    /// </summary>
    public int OnlineConsent { get; set; }

    /// <summary>
    /// 口頭同意区分
    ///     0:口頭等による同意なし
    ///     1:口頭等による同意あり
    /// </summary>
    public int OralBrowsingConsent { get; set; }

    /// <summary>
    /// 処方情報
    ///     処方箋情報CSVの101,111,201レコード
    /// </summary>
    public string DrugInfo { get; set; } = string.Empty;

    /// <summary>
    /// 削除フラグ
    ///     0:有効…最新のチェック結果
    ///     1:無効…過去のチェック結果
    ///     2:未確定…カルテ保存していない処方に対するチェック結果
    ///     ※処方箋情報の登録には有効なチェック結果が必要
    /// </summary>
    public int IsDeleted { get; set; }

    /// <summary>
    /// 作成日時
    /// </summary>
    public DateTime CreateDate { get; set; }

    /// <summary>
    /// 作成ID
    /// </summary>
    public int CreateId { get; set; }

    /// <summary>
    /// 作成端末
    /// </summary>
    public string CreateMachine { get; set; } = string.Empty;

    /// <summary>
    /// 更新日時
    /// </summary>
    public DateTime UpdateDate { get; set; }

    /// <summary>
    /// 更新ID
    /// </summary>
    public int UpdateId { get; set; }

    /// <summary>
    /// 更新端末
    /// </summary>
    public string UpdateMachine { get; set; } = string.Empty;

    public List<EpsChkDetailModel> EpsChkDetailModels { get; set; } = new List<EpsChkDetailModel>();
}