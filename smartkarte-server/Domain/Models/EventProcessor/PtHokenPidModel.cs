﻿using Entity.Tenant;

namespace Domain.Models.EventProcessor
{
    public class PtHokenPidModel
    {
        public PtHokenInf PtHokenInf { get; } = null;
        public HokensyaMst HokensyaMst { get; } = null;

        public PtHokenPidModel(PtHokenInf ptHokenInf, HokensyaMst hokensyaMst)
        {
            PtHokenInf = ptHokenInf;
            HokensyaMst = hokensyaMst;
        }

        public string HokensyaNo
        {
            get { return PtHokenInf != null ? PtHokenInf.HokensyaNo : ""; }
        }
        public string HokensyaKigo
        {
            get { return PtHokenInf != null ? PtHokenInf.Kigo : ""; }
        }
        public string HokensyaBango
        {
            get { return PtHokenInf != null ? PtHokenInf.Bango : ""; }
        }
        public string HokensyaEdaNo
        {
            get { return PtHokenInf != null ? PtHokenInf.EdaNo : ""; }
        }
        public int HokenStartDate
        {
            get { return PtHokenInf != null ? PtHokenInf.StartDate : 0; }
        }
        public int HokenEndDate
        {
            get { return PtHokenInf != null ? PtHokenInf.EndDate : 0; }
        }
        public int HonkeKbn
        {
            get { return PtHokenInf != null ? PtHokenInf.HonkeKbn : 0; }
        }
        public string HokensyaName
        {
            get { return HokensyaMst != null ? HokensyaMst.Name : ""; }
        }
        public string HokensyaAddress
        {
            get { return HokensyaMst != null ? HokensyaMst.Address1 + HokensyaMst.Address2 : ""; }
        }
        public string HokensyaTel
        {
            get { return HokensyaMst != null ? HokensyaMst.Tel1 : ""; }
        }
    }
}