﻿using Domain.Models.Renkei;

namespace Domain.Models.EventProcessor
{
    public class OuputCommonFileModel
    {
        public OuputCommonFileModel()
        {
        }

        public OuputCommonFileModel(bool isSaveDataCommonFile, OuputCommonFile ouputCommonFile, RenkeiModel renkeiModel)
        {
            IsSaveDataCommonFile = isSaveDataCommonFile;
            OuputCommonFile = ouputCommonFile;
            RenkeiModel = renkeiModel;
        }

        public OuputCommonFileModel(string param, RenkeiModel renkeiModel)
        {
            ParamCommonProgram = param;
            RenkeiModel = renkeiModel;
        }

        public OuputCommonFileModel(bool isSaveData130, List<ParammetersRenkei130> parammetersRenkei130, RenkeiModel renkeiModel)
        {
            IsSaveData130 = isSaveData130;
            ParammetersRenkei130 = parammetersRenkei130;
            RenkeiModel = renkeiModel;
        }

        public OuputCommonFileModel(bool isSaveData260, ParammetersRenkei260 parammetersRenkei260, RenkeiModel renkeiModel)
        {
            IsSaveData260 = isSaveData260;
            ParammetersRenkei260 = parammetersRenkei260;
            RenkeiModel = renkeiModel;
        }

        public RenkeiModel RenkeiModel { get; private set; }

        public bool IsSaveDataCommonFile {  get; private set; }

        public OuputCommonFile OuputCommonFile { get; private set; }

        public string ParamCommonProgram { get; private set; }

        public bool IsSaveData130 { get; private set; }

        public List<ParammetersRenkei130> ParammetersRenkei130 { get; private set; }

        public bool IsSaveData260 { get; private set; }

        public ParammetersRenkei260 ParammetersRenkei260 { get; private set; }
    }

    public class ParammetersRenkei130
    {
        public ParammetersRenkei130(List<string> @params, bool includeTimeParam, bool includeSecondsParam, string outputPath, string tmpPath)
        {
            Params = @params;
            IncludeTimeParam = includeTimeParam;
            IncludeSecondsParam = includeSecondsParam;
            OutputPath = outputPath;
            TmpPath = tmpPath;
        }

        public List<string> Params { get; private set; }

        public bool IncludeTimeParam { get; private set; }

        public bool IncludeSecondsParam { get; private set; }

        public string OutputPath { get; private set; }

        public string TmpPath { get; private set; }
    }

    public class ParammetersRenkei260
    {
        public ParammetersRenkei260()
        {
        }

        public ParammetersRenkei260(List<string> lines, string path, string wrkPath, string sPtNum, int sinDate, long raiinNo)
        {
            Lines = lines;
            Path = path;
            WrkPath = wrkPath;
            SPtNum = sPtNum;
            SinDate = sinDate;
            RaiinNo = raiinNo;
        }

        public List<string> Lines { get; private set; }

        public string Path { get; private set; }

        public string WrkPath { get; private set; }

        public string SPtNum { get; private set; }

        public int SinDate { get; private set; }

        public long RaiinNo { get; private set; }
    }

    public class OuputCommonFile
    {
        public OuputCommonFile()
        {
        }

        public OuputCommonFile(string path, string param, bool includeTimeParam, bool includeSecondsParam, string outputPath, string tmpPath)
        {
            Path = path;
            Param = param;
            IncludeTimeParam = includeTimeParam;
            IncludeSecondsParam = includeSecondsParam;
            OutputPath = outputPath;
            TmpPath = tmpPath;
        }

        public string Path { get; private set; }

        public string Param {  get; private set; }

        public bool IncludeTimeParam { get; private set; }

        public bool IncludeSecondsParam { get; private set; }

        public string OutputPath { get; private set; }

        public string TmpPath { get; private set; }
    }
}
