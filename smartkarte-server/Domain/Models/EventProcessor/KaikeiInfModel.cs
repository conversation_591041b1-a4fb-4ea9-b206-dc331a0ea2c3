﻿using Entity.Tenant;
using Helper.Common;
using Helper.Constants;

namespace Domain.Models.EventProcessor
{
    public class KaikeiInfModel
    {
        public KaikeiInf KaikeiInf { get; } = null;
        public PtInf PtInf { get; } = null;
        public PtHokenInf PtHokenInf { get; } = null;
        public Entity.Tenant.HokenMst HokenMst { get; } = null;
        public List<PtKohiModel> PtKohis { get; } = null;
        public HokensyaMst HokensyaMst { get; } = null;
        public KaikeiInfModel(KaikeiInf kaikeiInf, PtInf ptInf, PtHokenInf ptHokenInf, Entity.Tenant.HokenMst hokenMst, HokensyaMst hokensyaMst, List<PtKohiModel> ptKohis)
        {
            KaikeiInf = kaikeiInf;
            PtInf = ptInf;
            PtHokenInf = ptHokenInf;
            HokenMst = hokenMst;
            HokensyaMst = hokensyaMst;
            PtKohis = ptKohis;
        }
        /// <summary>
        /// 患者ID
        /// </summary>
        public long PtId
        {
            get { return KaikeiInf != null ? KaikeiInf.PtId : 0; }
        }
        /// <summary>
        /// 保険ID
        /// </summary>
        public int HokenId
        {
            get { return KaikeiInf != null ? KaikeiInf.HokenId : 0; }
        }
        /// <summary>
        /// 診療日
        /// </summary>
        public int SinDate
        {
            get { return KaikeiInf != null ? KaikeiInf.SinDate : 0; }
        }
        /// <summary>
        /// 来院番号
        /// </summary>
        public long RaiinNo
        {
            get { return KaikeiInf != null ? KaikeiInf.RaiinNo : 0; }
        }
        /// <summary>
        /// 患者負担額
        /// </summary>
        public int PtFutan
        {
            get { return KaikeiInf != null ? KaikeiInf.PtFutan : 0; }
        }
        /// <summary>
        /// まるめ調整額
        /// </summary>
        public int AdjustRound
        {
            get { return KaikeiInf != null ? KaikeiInf.AdjustRound : 0; }
        }
        /// <summary>
        /// 自費負担額
        /// </summary>
        public int JihiFutan
        {
            get { return KaikeiInf != null ? KaikeiInf.JihiFutan : 0; }
        }
        /// <summary>
        /// 自費外税
        /// </summary>
        public int JihiOuttax
        {
            get { return KaikeiInf != null ? KaikeiInf.JihiOuttax : 0; }
        }
        /// <summary>
        /// 自費負担合計
        /// </summary>
        public int JihiFutanTotal
        {
            get { return KaikeiInf != null ? KaikeiInf.JihiFutan + KaikeiInf.JihiOuttax : 0; }
        }
        /// <summary>
        /// 保険区分
        /// </summary>
        public int HokenKbn
        {
            get { return KaikeiInf != null ? KaikeiInf.HokenKbn : 0; }
        }
        /// <summary>
        /// 保険者番号
        /// </summary>
        public string HokensyaNo
        {
            get { return PtHokenInf != null ? PtHokenInf.HokensyaNo : ""; }
        }
        /// <summary>
        /// 被保険者証記号
        /// </summary>
        public string HokensyaKigo
        {
            get { return PtHokenInf != null ? PtHokenInf.Kigo : ""; }
        }
        /// <summary>
        /// 被保険者証番号
        /// </summary>
        public string HokensyaBango
        {
            get { return PtHokenInf != null ? PtHokenInf.Bango : ""; }
        }
        /// <summary>
        /// 被保険者証枝番
        /// </summary>
        public string HokensyaEdaNo
        {
            get { return PtHokenInf != null ? PtHokenInf.EdaNo : ""; }
        }
        /// <summary>
        /// 保険開始日
        /// </summary>
        public int HokenStartDate
        {
            get { return PtHokenInf != null ? PtHokenInf.StartDate : 0; }
        }
        /// <summary>
        /// 保険終了日
        /// </summary>
        public int HokenEndDate
        {
            get { return PtHokenInf != null ? PtHokenInf.EndDate : 0; }
        }
        /// <summary>
        /// 本人家族区分
        /// </summary>
        public int HonkeKbn
        {
            get { return PtHokenInf != null ? PtHokenInf.HonkeKbn : 0; }
        }
        /// <summary>
        /// 保険者名
        /// </summary>
        public string HokensyaName
        {
            get { return HokensyaMst != null ? HokensyaMst.Name : ""; }
        }
        /// <summary>
        /// 保険者住所
        /// </summary>
        public string HokensyaAddress
        {
            get { return HokensyaMst != null ? HokensyaMst.Address1 + HokensyaMst.Address2 : ""; }
        }
        /// <summary>
        /// 保険者電話番号
        /// </summary>
        public string HokensyaTel
        {
            get { return HokensyaMst != null ? HokensyaMst.Tel1 : ""; }
        }
        /// <summary>
        /// 使用している公費の数
        /// </summary>
        public int KohiCount
        {
            get
            {
                if (PtKohis == null || PtKohis.Any() == false)
                {
                    return 0;
                }
                else
                {
                    return PtKohis.Count(p => new int[] { 5, 6, 7 }.Contains(p.HokenMst.HokenSbtKbn));
                }
            }
        }
        /// <summary>
        /// 主保険の負担率
        /// </summary>
        public int HokenRate
        {
            get => GetHokenRate(PtHokenInf.Rate, HokenMst.HokenSbtKbn, PtHokenInf.KogakuKbn, PtHokenInf.Houbetu);
        }

        /// <summary>
        /// 負担率(%)
        /// </summary>
        public int? FutanRate
        {
            get
            {
                int? ret = null;

                if (PtHokenInf != null)
                {
                    if (new int[] { 1, 2 }.Contains(PtHokenInf.HokenKbn))
                    {

                        ret = GetHokenRate(PtHokenInf.Rate, HokenMst.HokenSbtKbn, PtHokenInf.KogakuKbn, PtHokenInf.Houbetu);

                    }
                    else if (PtHokenInf.HokenKbn == 0)
                    {
                        // 自費
                        ret = 100;
                    }
                    else
                    {
                        // 労災・自賠
                        //ret = null;
                    }
                }

                if (PtKohis != null)
                {
                    for (int i = 0; i < PtKohis.Count(); i++)
                    {
                        if (PtKohis[i].HokenMst.FutanKbn == 0)
                        {
                            ret = 0;
                        }
                        else if (ret == null || ret > PtKohis[i].FutanRate)
                        {
                            ret = PtKohis[i].FutanRate;
                        }
                    }
                }

                return ret;
            }
        }

        /// <summary>
        /// 主保険負担率計算
        /// </summary>
        /// <param name="futanRate">負担率</param>
        /// <param name="hokenSbtKbn">保険種別区分</param>
        /// <param name="kogakuKbn">高額療養費区分</param>
        /// <param name="honkeKbn">本人家族区分</param>
        /// <param name="houbetu">法別番号</param>
        /// <param name="receSbt">レセプト種別</param>
        /// <returns></returns>
        private int GetHokenRate(int futanRate, int hokenSbtKbn, int kogakuKbn, string houbetu)
        {
            int wrkRate = futanRate;

            switch (hokenSbtKbn)
            {
                case 0:
                    //主保険なし
                    break;
                case 1:
                    //主保険
                    if (IsPreSchool())
                    {
                        //６歳未満未就学児
                        wrkRate = 20;
                    }
                    else if (IsElder() && houbetu != "39")
                    {
                        wrkRate =
                            IsElder20per() ? wrkRate = 20 :  //前期高齢
                            IsElderExpat() ? wrkRate = 20 :  //75歳以上海外居住者
                            wrkRate = 10;
                    }

                    if (IsElder() || houbetu == "39")
                    {
                        wrkRate = 10;

                        if ((kogakuKbn == 3 && KaikeiInf.SinDate < KaiseiDate.d20180801) ||
                            (new int[] { 26, 27, 28 }.Contains(kogakuKbn) && KaikeiInf.SinDate >= KaiseiDate.d20180801))
                        {
                            //後期７割 or 高齢７割
                            wrkRate = 30;
                        }
                        else if (houbetu == "39" && kogakuKbn == 41 &&
                            KaikeiInf.SinDate >= KaiseiDate.d20221001)
                        {
                            //後期８割
                            wrkRate = 20;
                        }
                    }
                    break;
                default:
                    break;
            }

            return wrkRate;
        }

        /// <summary>
        /// 未就学かどうか
        /// </summary>
        /// <returns></returns>
        public bool IsPreSchool()
        {
            return !CIUtil.IsStudent(PtInf.Birthday, KaikeiInf.SinDate);
        }

        /// <summary>
        /// 70歳以上かどうか
        /// </summary>
        /// <returns></returns>
        public bool IsElder()
        {
            return CIUtil.AgeChk(PtInf.Birthday, KaikeiInf.SinDate, 70);
        }
        /// <summary>
        /// 前期高齢2割かどうか
        /// </summary>
        /// <returns></returns>
        public bool IsElder20per()
        {
            return CIUtil.Is70Zenki_20per(PtInf.Birthday, KaikeiInf.SinDate);
        }
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public bool IsElderExpat()
        {
            //75歳以上で海外居住者の方は後期高齢者医療には加入せず、
            //協会、健保組合に加入することになり、高齢受給者証を提示した場合、
            //H26.5診療分からは所得に合わせ2割または3割負担となる。
            return CIUtil.AgeChk(PtInf.Birthday, KaikeiInf.SinDate, 75) && KaikeiInf.SinDate >= 20140501;
        }
        /// <summary>
        /// 公費負担者番号
        /// </summary>
        /// <param name="index">1からの連番</param>
        /// <returns></returns>
        public string KohiFutansyaNo(int index)
        {
            string ret = "";

            if (PtKohis != null && PtKohis.Any() && index > 0 && index <= PtKohis.Count())
            {
                ret = PtKohis[index - 1].FutansyaNo;
            }

            return ret;
        }
        /// <summary>
        /// 公費受給者番号
        /// </summary>
        /// <param name="index"></param>
        /// <returns></returns>
        public string KohiJyukyusyaNo(int index)
        {
            string ret = "";

            if (PtKohis != null && PtKohis.Any() && index > 0 && index <= PtKohis.Count())
            {
                ret = PtKohis[index - 1].JyukyusyaNo;
            }

            return ret;
        }
        /// <summary>
        /// 公費使用開始日
        /// </summary>
        /// <param name="index">1～4</param>
        /// <returns></returns>
        public int KohiStartDate(int index)
        {
            int ret = 0;
            if (PtKohis != null && PtKohis.Any() && index > 0 && index <= PtKohis.Count())
            {
                ret = PtKohis[index - 1].StartDate;
            }

            return ret;
        }
        /// <summary>
        /// 公費使用終了日
        /// </summary>
        /// <param name="index">1～4</param>
        /// <returns></returns>
        public int KohiEndDate(int index)
        {
            int ret = 0;
            if (PtKohis != null && PtKohis.Any() && index > 0 && index <= PtKohis.Count())
            {
                ret = PtKohis[index - 1].EndDate;
            }

            return ret;
        }
    }
}