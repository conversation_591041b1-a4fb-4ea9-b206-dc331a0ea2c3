﻿namespace Domain.Models.EventProcessor
{
    public class ArgumentModel
    {
        public ArgumentModel(string eventCd, long ptId, int sinDate, long raiinNo, int? misyu, int nyukinDate, int? nyukin, int nyukinSortNo, int userId)
        {
            EventCd = eventCd;
            PtId = ptId;
            SinDate = sinDate;
            RaiinNo = raiinNo;
            Misyu = misyu;
            NyukinDate = nyukinDate;
            Nyukin = nyukin;
            NyukinSortNo = nyukinSortNo;
            UserId = userId;
        }

        /// <summary>
        /// イベントコード（EVENT_MST.EVENT_CD）
        /// </summary>
        public string EventCd { get; set; } = string.Empty;
        /// <summary>
        /// 患者ID（0-未指定）
        /// </summary>
        public long PtId { get; set; } = 0;
        /// <summary>
        /// 診療日（0-未指定）
        /// </summary>
        public int SinDate { get; set; } = 0;
        /// <summary>
        /// 来院番号（0-未指定）
        /// </summary>
        public long RaiinNo { get; set; } = 0;
        /// <summary>
        /// 未収金額
        ///     窓口精算からの呼び出しの場合、画面に表示している未収金額
        ///     収納一覧から呼び出しの場合、当該来院の未収金額
        ///     null：会計に関わる連携情報を出力しない
        /// </summary>
        public int? Misyu { get; set; } = null;
        /// <summary>
        /// 入金日（0-未指定）
        ///     窓口精算からの呼び出しの場合、画面で入力した入金日
        ///     収納一覧から呼び出しの場合、当該来院の最終入金日
        ///     null：会計に関わる連携情報を出力しない
        /// </summary>
        public int NyukinDate { get; set; } = 0;
        /// <summary>
        /// 入金額（null-未指定）
        ///     窓口精算からの呼び出しの場合、画面で入力した入金額
        ///     収納一覧から呼び出しの場合、当該来院の今回入力した入金額
        ///     null：会計に関わる連携情報を出力しない
        /// </summary>
        public int? Nyukin { get; set; } = null;
        /// <summary>
        /// 入金順番
        ///     同一来院に対して分割入金した場合の入金の順番
        /// </summary>
        public int NyukinSortNo { get; set; } = 0;

        public int UserId { get; set; }
    }
}
