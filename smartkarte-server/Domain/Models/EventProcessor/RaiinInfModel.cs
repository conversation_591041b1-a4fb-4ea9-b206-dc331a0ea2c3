﻿using Entity.Tenant;

namespace Domain.Models.EventProcessor
{
    public class RaiinInfModel
    {
        public RaiinInf RaiinInf { get; } = null;
        public KaMst KaMst { get; } = null;
        public Entity.Tenant.RsvInf RsvInf { get; } = null;
        public RsvFrameMst RsvFrameMst { get; } = null;
        public Entity.Tenant.RaiinCmtInf RaiinCmtInf { get; } = null;
        public RsvRenkeiInf RsvRenkeiInf { get; } = null;

        public PtLastVisitDate PtLastVisitDate { get; } = null;
        public RaiinInfModel(RaiinInf raiinInf, KaMst kaMst, Entity.Tenant.RsvInf rsvInf, RsvFrameMst rsvFrameMst, Entity.Tenant.RaiinCmtInf raiinCmtInf, RsvRenkeiInf rsvRenkeiInf, PtLastVisitDate ptLastVisitDate)
        {
            RaiinInf = raiinInf;
            KaMst = kaMst;
            RsvInf = rsvInf;
            RsvFrameMst = rsvFrameMst;
            RaiinCmtInf = raiinCmtInf;
            RsvRenkeiInf = rsvRenkeiInf;
            PtLastVisitDate = ptLastVisitDate;
        }

        /// <summary>
        /// 来院情報
        /// </summary>
        /// <summary>
        /// 医療機関識別ID
        /// </summary>
        public int HpId
        {
            get { return RaiinInf.HpId; }
        }

        /// <summary>
        /// 患者ID
        ///  患者を識別するためのシステム固有の番号
        /// </summary>
        public long PtId
        {
            get { return RaiinInf.PtId; }
        }

        /// <summary>
        /// 診療日
        ///  yyyymmdd 
        /// </summary>
        public int SinDate
        {
            get { return RaiinInf.SinDate; }
        }

        /// <summary>
        /// 来院番号
        /// </summary>
        public long RaiinNo
        {
            get { return RaiinInf.RaiinNo; }
        }

        /// <summary>
        /// 親来院番号
        /// </summary>
        public long OyaRaiinNo
        {
            get { return RaiinInf.OyaRaiinNo; }
        }

        /// <summary>
        /// 状態
        ///  0:予約
        ///  1:受付
        ///  3:一時保存
        ///  5:計算
        ///  7:精算待ち
        ///  9:精算済
        /// </summary>
        public int Status
        {
            get { return RaiinInf.Status; }
        }

        /// <summary>
        /// 予約フラグ
        ///  1:予約の来院  
        /// </summary>
        public int IsYoyaku
        {
            get { return RaiinInf.IsYoyaku; }
        }

        /// <summary>
        /// 予約時間
        ///  HH24MISS
        /// </summary>
        public string YoyakuTime
        {
            get { return RaiinInf.YoyakuTime; }
        }

        /// <summary>
        /// 予約者ID
        /// </summary>
        public int YoyakuId
        {
            get { return RaiinInf.YoyakuId; }
        }

        /// <summary>
        /// 受付種別
        /// </summary>
        public int UketukeSbt
        {
            get { return RaiinInf.UketukeSbt; }
        }

        /// <summary>
        /// 受付時間
        ///  HH24MISS
        /// </summary>
        public string UketukeTime
        {
            get { return RaiinInf.UketukeTime; }
        }

        /// <summary>
        /// 受付者ID
        /// </summary>
        public int UketukeId
        {
            get { return RaiinInf.UketukeId; }
        }

        /// <summary>
        /// 受付番号
        /// </summary>
        public int UketukeNo
        {
            get { return RaiinInf.UketukeNo; }
        }

        /// <summary>
        /// 診察開始時間
        ///  HH24MISS
        /// </summary>
        public string SinStartTime
        {
            get { return RaiinInf.SinStartTime; }
        }

        /// <summary>
        /// 診察終了時間
        ///  HH24MISS　※状態が計算以上になった時間        
        /// </summary>
        public string SinEndTime
        {
            get { return RaiinInf.SinEndTime; }
        }

        /// <summary>
        /// 精算時間
        ///  HH24MISS
        /// </summary>
        public string KaikeiTime
        {
            get { return RaiinInf.KaikeiTime; }
        }

        /// <summary>
        /// 精算者ID
        /// </summary>
        public int KaikeiId
        {
            get { return RaiinInf.KaikeiId; }
        }

        /// <summary>
        /// 診療科ID
        /// </summary>
        public int KaId
        {
            get { return RaiinInf.KaId; }
        }

        /// <summary>
        /// 担当医ID
        /// </summary>
        public int TantoId
        {
            get { return RaiinInf.TantoId; }
        }

        /// <summary>
        /// 保険組合せID
        ///  患者別に保険情報を識別するための固有の番号
        /// </summary>
        public int HokenPid
        {
            get { return RaiinInf.HokenPid; }
        }

        /// <summary>
        /// 初再診区分
        ///  受付時設定、ODR_INF更新後はトリガーで設定       
        /// </summary>
        public int SyosaisinKbn
        {
            get { return RaiinInf.SyosaisinKbn; }
        }

        /// <summary>
        /// 時間枠区分
        ///  受付時設定、ODR_INF更新後はトリガーで設定       
        /// </summary>
        public int JikanKbn
        {
            get { return RaiinInf.JikanKbn; }
        }

        /// <summary>
        /// 削除区分
        ///  1: 削除
        /// </summary>
        public int IsDeleted
        {
            get { return RaiinInf.IsDeleted; }
        }

        /// <summary>
        /// 作成日時 
        /// </summary>
        public DateTime CreateDate
        {
            get { return RaiinInf.CreateDate; }
        }

        /// <summary>
        /// 作成者  
        /// </summary>
        public int CreateId
        {
            get { return RaiinInf.CreateId; }
        }

        /// <summary>
        /// 作成端末   
        /// </summary>
        public string CreateMachine
        {
            get { return RaiinInf.CreateMachine; }
        }

        /// <summary>
        /// 更新日時   
        /// </summary>
        public DateTime UpdateDate
        {
            get { return RaiinInf.UpdateDate; }
        }

        /// <summary>
        /// 更新者   
        /// </summary>
        public int UpdateId
        {
            get { return RaiinInf.UpdateId; }
        }

        /// <summary>
        /// 更新端末   
        /// </summary>
        public string UpdateMachine
        {
            get { return RaiinInf.UpdateMachine; }
        }

        public string KaName
        {
            get { return KaMst != null ? KaMst.KaName : ""; }
        }
        public string KaSname
        {
            get { return KaMst != null ? KaMst.KaSname : ""; }
        }
        public string RsvFrameName
        {
            get { return RsvFrameMst != null ? RsvFrameMst.RsvFrameName : ""; }
        }

        public long OtherPtId
        {
            get { return RsvRenkeiInf != null ? RsvRenkeiInf.OtherPtId : 0; }
        }

        public long OtherSeqNo
        {
            get { return RsvRenkeiInf != null ? RsvRenkeiInf.OtherSeqNo : 0; }
        }

        public long OtherSeqNo2
        {
            get { return RsvRenkeiInf != null ? RsvRenkeiInf.OtherSeqNo2 : 0; }
        }

        public int LastDate
        {
            get { return PtLastVisitDate != null ? PtLastVisitDate.LastVisitDate : 0; }
        }

        public string RaiinCmt
        {
            get { return RaiinCmtInf != null ? RaiinCmtInf.Text : ""; }
        }
    }
}