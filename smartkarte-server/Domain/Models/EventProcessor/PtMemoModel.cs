﻿namespace Domain.Models.EventProcessor
{
    public class PtMemoModel
    {
        public Entity.Tenant.PtMemo PtMemo { get; } = null;
        public string[] Memos = null;
        public PtMemoModel(Entity.Tenant.PtMemo ptMemo)
        {
            PtMemo = ptMemo;

            string[] del = { "\r\n", "\r", "\n" };
            Memos = ptMemo.Memo.Split(del, StringSplitOptions.None);
        }

        public string GetMemoLine(int index)
        {
            string ret = "";
            if (Memos.Count() > index)
            {
                ret = Memos[index];
            }
            return ret;
        }

        /// <summary>
        /// 患者メモ
        /// </summary>
        /// <summary>
        /// 医療機関識別ID
        /// </summary>
        public int HpId
        {
            get { return PtMemo.HpId; }
        }

        /// <summary>
        /// 患者ID
        ///  患者を識別するためのシステム固有の番号      
        /// </summary>
        public long PtId
        {
            get { return PtMemo.PtId; }
        }

        /// <summary>
        /// 連番
        /// </summary>
        public long SeqNo
        {
            get { return PtMemo.SeqNo; }
        }

        /// <summary>
        /// メモ
        /// </summary>
        public string Memo
        {
            get { return PtMemo.Memo; }
        }

        /// <summary>
        /// 削除区分
        /// </summary>
        public int IsDeleted
        {
            get { return PtMemo.IsDeleted; }
        }

        /// <summary>
        /// 作成日時 
        /// </summary>
        public DateTime CreateDate
        {
            get { return PtMemo.CreateDate; }
        }

        /// <summary>
        /// 作成者  
        /// </summary>
        public int CreateId
        {
            get { return PtMemo.CreateId; }
        }

        /// <summary>
        /// 作成端末   
        /// </summary>
        public string CreateMachine
        {
            get { return PtMemo.CreateMachine; }
        }

        /// <summary>
        /// 更新日時   
        /// </summary>
        public DateTime UpdateDate
        {
            get { return PtMemo.UpdateDate; }
        }

        /// <summary>
        /// 更新者   
        /// </summary>
        public int UpdateId
        {
            get { return PtMemo.UpdateId; }
        }

        /// <summary>
        /// 更新端末   
        /// </summary>
        public string UpdateMachine
        {
            get { return PtMemo.UpdateMachine; }
        }

    }
}
