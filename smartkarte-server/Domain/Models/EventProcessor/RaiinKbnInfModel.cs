﻿using Entity.Tenant;
namespace Domain.Models.EventProcessor
{
    public class RaiinKbnInfModel
    {
        RaiinKbnInf RaiinKbnInf { get; } = null;
        RaiinKbnDetail RaiinKbnDetail { get; } = null;

        public RaiinKbnInfModel(RaiinKbnInf raiinKbnInf, RaiinKbnDetail raiinKbnDetail)
        {
            RaiinKbnInf = raiinKbnInf;
            RaiinKbnDetail = raiinKbnDetail;
        }

        public int GrpId
        {
            get { return RaiinKbnInf.GrpId; }
        }

        public int KbnCd
        {
            get { return RaiinKbnInf.KbnCd; }
        }

        public string KbnName
        {
            get { return RaiinKbnDetail != null ? RaiinKbnDetail.KbnName : ""; }
        }
    }
}
