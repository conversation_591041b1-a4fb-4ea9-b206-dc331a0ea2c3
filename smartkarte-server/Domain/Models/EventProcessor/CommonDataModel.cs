﻿using Domain.Models.AuditLog;
using Domain.Models.HpInf;
using Domain.Models.RaiinKubunMst;
using Domain.Models.User;
using Helper.Common;

namespace Domain.Models.EventProcessor
{
    public class CommonDataModel
    {
        public CommonDataModel
           (ArgumentModel arg,
           PtInfModel ptInf, List<PtGrpInfModel> ptGrpInfs, PtMemoModel ptMemo,
           RaiinInfModel raiinInf, List<RaiinKbnInfModel> raiinKbnInfs, List<KaikeiInfModel> kaikeiInfs,
           UserMstModel userMst, PtHokenPidModel ptHokenPid)
        {
            PtId = arg.PtId;
            SinDate = arg.SinDate;
            RaiinNo = arg.RaiinNo;
            Misyu = arg.Misyu;
            NyukinDate = arg.NyukinDate;
            Nyukin = arg.Nyukin;

            PtInf = ptInf;
            PtGrpInfs = ptGrpInfs;
            PtMemo = ptMemo;
            PtHokenPid = ptHokenPid;

            RaiinInf = raiinInf;
            RaiinKbnInfs = raiinKbnInfs;
            KaikeiInfs = kaikeiInfs;
            UserMst = userMst;

            Seikyu = SumPtFutan;
            SeikyuJihi = SumJihiFutanTotal;
            SeikyuGokei = Seikyu + SeikyuJihi + (Misyu ?? 0);
        }
        public CommonDataModel
            (long ptId, int sinDate, long raiinNo, int? misyu, int nyukinDate, int? nyukin,
            PtInfModel ptInf, List<PtGrpInfModel> ptGrpInfs, PtMemoModel ptMemo,
            RaiinInfModel raiinInf, List<RaiinKbnInfModel> raiinKbnInfs, List<KaikeiInfModel> kaikeiInfs,
            UserMstModel userMst)
        {
            PtId = ptId;
            SinDate = sinDate;
            RaiinNo = raiinNo;
            Misyu = misyu;
            NyukinDate = nyukinDate;
            Nyukin = nyukin;

            PtInf = ptInf;
            PtGrpInfs = ptGrpInfs;
            PtMemo = ptMemo;

            RaiinInf = raiinInf;
            RaiinKbnInfs = raiinKbnInfs;
            KaikeiInfs = kaikeiInfs;
            UserMst = userMst;
        }
        public long PtId { get; } = 0;
        public int SinDate { get; } = 0;
        public long RaiinNo { get; } = 0;

        private PtInfModel PtInf { get; } = null;
        public long PtNum
        {
            get { return PtInf != null ? PtInf.PtNum : 0; }
        }
        public string KanaName
        {
            get { return PtInf != null ? PtInf.KanaName : ""; }
        }
        public string KanaNameS
        {
            get { return PtInf != null ? PtInf.KanaNameS : ""; }
        }
        public string KanaNameM
        {
            get { return PtInf != null ? PtInf.KanaNameM : ""; }
        }
        public string Name
        {
            get { return PtInf != null ? PtInf.Name : ""; }
        }
        public string NameS
        {
            get { return PtInf != null ? PtInf.NameS : ""; }
        }
        public string NameM
        {
            get { return PtInf != null ? PtInf.NameM : ""; }
        }

        public string SexStr(string man, string female)
        {
            string ret = "";

            if (PtInf != null)
            {
                ret = PtInf.SexStr(man, female);
            }

            return ret;
        }
        public int Sex
        {
            get { return PtInf != null ? PtInf.Sex : 0; }
        }
        public int Birthday
        {
            get { return PtInf != null ? PtInf.Birthday : 0; }
        }
        /// <summary>
        /// 年齢（システム日付基準）
        /// </summary>
        public int Age
        {
            get
            {
                int ret = 0;

                if (Birthday > 0)
                {
                    ret = CIUtil.SDateToAge(Birthday, CIUtil.StrToIntDef(DateTime.Now.ToString("yyyyMMdd"), 0));
                }

                return ret;
            }
        }
        /// <summary>
        /// 年齢（診療日基準）
        /// </summary>
        public int SinDateAge
        {
            get
            {
                int ret = 0;

                if (Birthday > 0)
                {
                    ret = CIUtil.SDateToAge(Birthday, SinDate);
                }

                return ret;
            }
        }
        public string HomePost
        {
            get { return PtInf != null ? PtInf.HomePost : ""; }
        }
        public string HomePostHyphen
        {
            get
            {
                string ret = "";

                if (PtInf != null && string.IsNullOrEmpty(PtInf.HomePost) == false)
                {
                    ret = PtInf.HomePost;
                    if (ret.Length > 3)
                    {
                        ret = ret.Substring(0, 3) + "-" + ret.Substring(3);
                    }
                }

                return ret;
            }
        }
        public string HomeAddress
        {
            get { return PtInf != null ? PtInf.HomeAddress : ""; }
        }
        public string HomeAddress1
        {
            get { return PtInf != null ? PtInf.HomeAddress1 : ""; }
        }
        public string HomeAddress2
        {
            get { return PtInf != null ? PtInf.HomeAddress2 : ""; }
        }
        public string Tel
        {
            get { return PtInf != null ? PtInf.Tel : ""; }
        }
        public string Tel1
        {
            get { return PtInf != null ? PtInf.Tel1 : ""; }
        }
        public string Tel2
        {
            get { return PtInf != null ? PtInf.Tel2 : ""; }
        }
        public string RenrakuTel
        {
            get { return PtInf != null ? PtInf.RenrakuTel : ""; }
        }
        public string Setanusi
        {
            get { return PtInf != null ? PtInf.Setanusi : ""; }
        }
        public string OfficeName
        {
            get { return PtInf != null ? PtInf.OfficeName : ""; }
        }
        public string OfficeAddress
        {
            get { return PtInf != null ? PtInf.OfficeAddress : ""; }
        }
        public string Job
        {
            get { return PtInf != null ? PtInf.Job : ""; }
        }


        public List<PtGrpInfModel> PtGrpInfs { get; } = null;

        private PtMemoModel PtMemo { get; } = null;
        public string GetMemoLine(int index)
        {
            string ret = "";

            if (PtMemo != null)
            {
                ret = PtMemo.GetMemoLine(index);
            }

            return ret;
        }

        //private PtHokenModel PtHoken { get; } = null;
        private PtHokenPidModel PtHokenPid { get; } = null;

        public string HokensyaNo
        {
            //get { return KaikeiInfs != null && KaikeiInfs.Any() ? KaikeiInfs.First().HokensyaNo : ""; }
            get
            {
                string ret = "";
                if (KaikeiInfs != null && KaikeiInfs.Any())
                {
                    ret = KaikeiInfs.First().HokensyaNo;
                }
                else
                {
                    ret = PtHokenPid != null ? PtHokenPid.HokensyaNo : "";
                }
                return ret;
            }
        }
        public string HokensyaKigo
        {
            //get { return PtHoken != null ? PtHoken.HokensyaKigo : ""; }
            //get { return KaikeiInfs != null && KaikeiInfs.Any() ? KaikeiInfs.First().HokensyaKigo : ""; }
            get
            {
                string ret = "";
                if (KaikeiInfs != null && KaikeiInfs.Any())
                {
                    ret = KaikeiInfs.First().HokensyaKigo;
                }
                else
                {
                    ret = PtHokenPid != null ? PtHokenPid.HokensyaKigo : "";
                }
                return ret;
            }
        }
        public string HokensyaBango
        {
            //get { return PtHoken != null ? PtHoken.HokensyaBango : ""; }
            //get { return KaikeiInfs != null && KaikeiInfs.Any() ? KaikeiInfs.First().HokensyaBango : ""; }
            get
            {
                string ret = "";
                if (KaikeiInfs != null && KaikeiInfs.Any())
                {
                    ret = KaikeiInfs.First().HokensyaBango;
                }
                else
                {
                    ret = PtHokenPid != null ? PtHokenPid.HokensyaBango : "";
                }
                return ret;
            }
        }
        public string HokensyaEdaNo
        {
            //get { return KaikeiInfs != null && KaikeiInfs.Any() ? KaikeiInfs.First().HokensyaEdaNo : ""; }
            get
            {
                string ret = "";
                if (KaikeiInfs != null && KaikeiInfs.Any())
                {
                    ret = KaikeiInfs.First().HokensyaEdaNo;
                }
                else
                {
                    ret = PtHokenPid != null ? PtHokenPid.HokensyaEdaNo : "";
                }
                return ret;
            }
        }
        public int HokenStartDate
        {
            //get { return PtHoken != null ? PtHoken.HokenStartDate : 0; }
            //get { return KaikeiInfs != null && KaikeiInfs.Any() ? KaikeiInfs.First().HokenStartDate : 0; }
            get
            {
                int ret = 0;
                if (KaikeiInfs != null && KaikeiInfs.Any())
                {
                    ret = KaikeiInfs.First().HokenStartDate;
                }
                else
                {
                    ret = PtHokenPid != null ? PtHokenPid.HokenStartDate : 0;
                }
                return ret;
            }
        }
        public int HokenEndDate
        {
            //get { return PtHoken != null ? PtHoken.HokenEndDate : 0; }
            //get { return KaikeiInfs != null && KaikeiInfs.Any() ? KaikeiInfs.First().HokenEndDate : 0; }
            get
            {
                int ret = 0;
                if (KaikeiInfs != null && KaikeiInfs.Any())
                {
                    ret = KaikeiInfs.First().HokenEndDate;
                }
                else
                {
                    ret = PtHokenPid != null ? PtHokenPid.HokenEndDate : 0;
                }
                return ret;
            }
        }
        public int HonkeKbn
        {
            //get { return PtHoken != null ? PtHoken.HonkeKbn : 0; }
            //get { return KaikeiInfs != null && KaikeiInfs.Any() ? KaikeiInfs.First().HonkeKbn : 0; }
            get
            {
                int ret = 0;
                if (KaikeiInfs != null && KaikeiInfs.Any())
                {
                    ret = KaikeiInfs.First().HonkeKbn;
                }
                else
                {
                    ret = PtHokenPid != null ? PtHokenPid.HonkeKbn : 0;
                }
                return ret;
            }
        }
        public int HokenRate
        {
            //get { return PtHoken != null ? PtHoken.HokenRate : 0; }
            get { return KaikeiInfs != null && KaikeiInfs.Any() ? KaikeiInfs.First().HokenRate : 0; }
        }
        public int? FutanRate
        {
            //get { return PtHoken != null ? PtHoken.FutanRate : 0; }
            get { return KaikeiInfs != null && KaikeiInfs.Any() ? KaikeiInfs.First().FutanRate : 0; }
        }
        public string HokensyaName
        {
            //get { return PtHoken != null ? PtHoken.HokensyaName : ""; }
            //get { return KaikeiInfs != null && KaikeiInfs.Any() ? KaikeiInfs.First().HokensyaName : ""; }
            get
            {
                string ret = "";
                if (KaikeiInfs != null && KaikeiInfs.Any())
                {
                    ret = KaikeiInfs.First().HokensyaName;
                }
                else
                {
                    ret = PtHokenPid != null ? PtHokenPid.HokensyaName : "";
                }
                return ret;
            }
        }
        public string HokensyaAddress
        {
            //get { return PtHoken != null ? PtHoken.HokensyaAddress : ""; }
            //get { return KaikeiInfs != null && KaikeiInfs.Any() ? KaikeiInfs.First().HokensyaAddress : ""; }
            get
            {
                string ret = "";
                if (KaikeiInfs != null && KaikeiInfs.Any())
                {
                    ret = KaikeiInfs.First().HokensyaAddress;
                }
                else
                {
                    ret = PtHokenPid != null ? PtHokenPid.HokensyaAddress : "";
                }
                return ret;
            }
        }
        public string HokensyaTel
        {
            //get { return PtHoken != null ? PtHoken.HokensyaTel : ""; }
            //get { return KaikeiInfs != null && KaikeiInfs.Any() ? KaikeiInfs.First().HokensyaTel : ""; }
            get
            {
                string ret = "";
                if (KaikeiInfs != null && KaikeiInfs.Any())
                {
                    ret = KaikeiInfs.First().HokensyaTel;
                }
                else
                {
                    ret = PtHokenPid != null ? PtHokenPid.HokensyaTel : "";
                }
                return ret;
            }
        }
        public string KohiFutansyaNo(int index)
        {
            string ret = "";
            if (KaikeiInfs != null && KaikeiInfs.Any())
            {
                ret = KaikeiInfs.First().KohiFutansyaNo(index);
            }
            return ret;
        }
        public string KohiJyukyusyaNo(int index)
        {
            string ret = "";
            if (KaikeiInfs != null && KaikeiInfs.Any())
            {
                ret = KaikeiInfs.First().KohiJyukyusyaNo(index);
            }
            return ret;
        }
        public int KohiStartDate(int index)
        {
            int ret = 0;
            if (KaikeiInfs != null && KaikeiInfs.Any())
            {
                ret = KaikeiInfs.First().KohiStartDate(index);
            }
            return ret;
        }
        public int KohiEndDate(int index)
        {
            int ret = 0;
            if (KaikeiInfs != null && KaikeiInfs.Any())
            {
                ret = KaikeiInfs.First().KohiEndDate(index);
            }
            return ret;
        }
        //public string GetKohiFutansyaNo(int index)
        //{
        //    string ret = "";

        //    if(PtHoken != null)
        //    {
        //        ret = PtHoken.GetKohiFutansyaNo(index);
        //    }
        //    return ret;
        //}
        //public string GetKohiJyukyusyaNo(int index)
        //{
        //    string ret = "";

        //    if (PtHoken != null)
        //    {
        //        ret = PtHoken.GetKohiJyukyusyaNo(index);
        //    }
        //    return ret;
        //}
        //public int GetKohiStartDate(int index)
        //{
        //    int ret = 0;
        //    if(PtHoken != null)
        //    {
        //        ret = PtHoken.GetKohiStartDate(index);
        //    }
        //    return ret;
        //}
        //public int GetKohiEndDate(int index)
        //{
        //    int ret = 0;
        //    if (PtHoken != null)
        //    {
        //        ret = PtHoken.GetKohiEndDate(index);
        //    }
        //    return ret;
        //}
        private RaiinInfModel RaiinInf { get; } = null;
        public int TantoId
        {
            get { return RaiinInf != null ? RaiinInf.TantoId : 0; }
        }
        public string YoyakuTime
        {
            get { return RaiinInf != null ? RaiinInf.YoyakuTime : ""; }
        }
        public int UketukeNo
        {
            get { return RaiinInf != null ? RaiinInf.UketukeNo : 0; }
        }
        public int UketukeSbt
        {
            get { return RaiinInf != null ? RaiinInf.UketukeSbt : 0; }
        }
        public int Status
        {
            get { return RaiinInf != null ? RaiinInf.Status : 0; }
        }
        public int LastDate
        {
            get { return RaiinInf != null ? RaiinInf.LastDate : 0; }
        }
        public string KaName
        {
            get { return RaiinInf != null ? RaiinInf.KaName : ""; }
        }
        public string KaSname
        {
            get { return RaiinInf != null ? RaiinInf.KaSname : ""; }
        }
        public string RsvFrameName
        {
            get { return RaiinInf != null ? RaiinInf.RsvFrameName : ""; }
        }
        public string RaiinCmt
        {
            get { return RaiinInf != null ? RaiinInf.RaiinCmt : ""; }
        }
        public long OtherPtId
        {
            get { return RaiinInf != null ? RaiinInf.OtherPtId : -1; }
        }
        public long OtherSeqNo
        {
            get { return RaiinInf != null ? RaiinInf.OtherSeqNo : -1; }
        }
        public long OtherSeqNo2
        {
            get { return RaiinInf != null ? RaiinInf.OtherSeqNo2 : -1; }
        }
        public List<RaiinKbnInfModel> RaiinKbnInfs { get; } = null;
        private List<KaikeiInfModel> KaikeiInfs { get; } = null;
        public int HokenId
        {
            get { return KaikeiInfs != null && KaikeiInfs.Any() ? KaikeiInfs.First().HokenId : 0; }
        }
        public int SeikyuDate
        {
            get { return KaikeiInfs != null && KaikeiInfs.Any() ? KaikeiInfs.First().SinDate : 0; }
        }
        public int SumPtFutan
        {
            get
            {
                int ret = 0;

                if (KaikeiInfs != null && KaikeiInfs.Any())
                {
                    ret = KaikeiInfs.Sum(p => p.PtFutan + p.AdjustRound);
                }
                return ret;
            }
        }
        public int SumJihiFutanTotal
        {
            get
            {
                int ret = 0;

                if (KaikeiInfs != null && KaikeiInfs.Any())
                {
                    ret = KaikeiInfs.Sum(p => p.JihiFutanTotal);
                }
                return ret;
            }
        }
        public int HokenKbn
        {
            get { return KaikeiInfs != null && KaikeiInfs.Any() ? KaikeiInfs.First().HokenKbn : 0; }
        }
        private UserMstModel UserMst { get; } = null;
        public string LoginId
        {
            get { return UserMst != null ? UserMst.LoginId : ""; }
        }
        public int UserId
        {
            get { return UserMst != null ? UserMst.UserId : 0; }
        }

        public int? Seikyu { get; set; } = null;
        public int? SeikyuJihi { get; set; } = null;
        public int? SeikyuGokei { get; set; } = null;
        public int? Misyu { get; set; } = null;
        public int? Nyukin { get; set; } = null;
        public int NyukinDate { get; set; } = 0;
        /// <summary>
        /// 入金順番
        ///     同一来院に対して分割入金した場合の入金の順番
        /// </summary>
        public int NyukinSortNo { get; set; } = 0;
    }
}
