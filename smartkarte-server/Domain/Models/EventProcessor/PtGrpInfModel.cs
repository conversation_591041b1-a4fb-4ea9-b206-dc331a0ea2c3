﻿using Entity.Tenant;

namespace Domain.Models.EventProcessor
{
    public class PtGrpInfModel 
    {
        public PtGrpInf PtGrpInf { get; } = null;

        public PtGrpInfModel(PtGrpInf ptGrpInf)
        {
            PtGrpInf = ptGrpInf;
        }

        /// <summary>
        /// 患者分類情報
        /// </summary>
        /// <summary>
        /// 医療機関識別ID
        /// </summary>
        public int HpId
        {
            get { return PtGrpInf.HpId; }
        }

        /// <summary>
        /// 患者ID
        ///  患者を識別するためのシステム固有の番号
        /// </summary>
        public long PtId
        {
            get { return PtGrpInf.PtId; }
        }

        /// <summary>
        /// 分類番号
        /// </summary>
        public int GroupId
        {
            get { return PtGrpInf.GroupId; }
        }

        /// <summary>
        /// 連番
        /// </summary>
        public long SeqNo
        {
            get { return PtGrpInf.SeqNo; }
        }

        /// <summary>
        /// 並び順
        /// </summary>
        public int SortNo
        {
            get { return PtGrpInf.SortNo; }
        }

        /// <summary>
        /// 分類項目コード
        /// </summary>
        public string GroupCode
        {
            get { return PtGrpInf.GroupCode; }
        }

        /// <summary>
        /// 削除区分
        /// </summary>
        public int IsDeleted
        {
            get { return PtGrpInf.IsDeleted; }
        }

        /// <summary>
        /// 作成日時 
        /// </summary>
        public DateTime CreateDate
        {
            get { return PtGrpInf.CreateDate; }
        }

        /// <summary>
        /// 作成者  
        /// </summary>
        public int CreateId
        {
            get { return PtGrpInf.CreateId; }
        }

        /// <summary>
        /// 作成端末   
        /// </summary>
        public string CreateMachine
        {
            get { return PtGrpInf.CreateMachine; }
        }

        /// <summary>
        /// 更新日時   
        /// </summary>
        public DateTime UpdateDate
        {
            get { return PtGrpInf.UpdateDate; }
        }

        /// <summary>
        /// 更新者   
        /// </summary>
        public int UpdateId
        {
            get { return PtGrpInf.UpdateId; }
        }

        /// <summary>
        /// 更新端末   
        /// </summary>
        public string UpdateMachine
        {
            get { return PtGrpInf.UpdateMachine; }
        }
    }
}
