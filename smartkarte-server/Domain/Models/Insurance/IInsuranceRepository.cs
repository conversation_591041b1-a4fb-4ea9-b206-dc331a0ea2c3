﻿using Domain.Common;
using Domain.Models.Insurance.AIChat;
using Domain.Models.InsuranceInfor;
using Domain.Models.MaxMoney;
using Domain.Models.Online;
using Domain.Models.PatientInfor;
using Helper.Constants;

namespace Domain.Models.Insurance
{
    public interface IInsuranceRepository : IRepositoryBase
    {
        InsuranceDataModel GetInsuranceListById(int hpId, long ptId, int sinDate, bool flag = true, bool isDeletedPtHokenInf = false);

        List<(int HokenPid, int HokenKbn)> GetHokenKbnList(int hpId, List<int> hokenPidList, long ptId, int sinDate);

        HokenInfModel? GetPtHokenInfByHokenId(int hpId, long ptId, int hokenId, long seqNo);

        IEnumerable<InsuranceModel> GetListHokenPattern(int hpId, long ptId, int sinDate, bool allowDisplayDeleted, bool isAllHoken = true, bool isHoken = true, bool isJihi = true, bool isRosai = true, bool isJibai = true);

        bool CheckExistHokenPIdList(List<int> hokenPIds, List<int> hpIds, List<long> ptIds);

        bool CheckExistHokenId(int hpId, int hokenId);

        bool CheckExistHokenPids(int hpId, List<int> hokenPids);

        bool CheckExistHokenPid(int hpId, int hokenPid);

        List<HokenInfModel> GetCheckListHokenInf(int hpId, long ptId, List<int> hokenPids);

        List<(int, int)> GetListHistoryPid(int hpId, long ptId, int sinDate, List<int> historyPids, int selectedHokenPid);

        List<InsuranceModel> GetInsuranceList(int hpId, long ptId, int sinDate, bool isDeleted = false, bool isCache = false);

        bool DeleteInsuranceScan(int hpId, long seqNo, int userId);

        bool CheckHokenPatternUsed(int hpId, long ptId, int hokenPid);

        List<KohiPriorityModel> GetKohiPriorityList(int hpId);

        List<InsuranceScanModel> GetListInsuranceScanByPtId(int hpId, long ptId);

        int GetHokenKbnByHokenId(int hpId, int hokenId, long ptId);

        List<HokenInfModel> FindPtHokenList(int hpId, long ptId, int sinDay);

        InsuranceDataDto GetInsurancesDataById(int hpId, long ptId, int sinDate, bool flag = true, bool isDeletedPtHokenInf = false);

        List<HokenPatternDto> GetHokenPatternByPtId(int hpId, long ptId, int sinDate, bool flag = true, bool isDeletedPtHokenInf = false, bool isOdrInf = false);

        List<KohiInfDto> GetKohiInfByPtId(int hpId, long ptId, int sinDate, bool flag = true, bool isDeletedPtHokenInf = false);

        List<HokenInfDto> GetHokenInfByPtId(int hpId, long ptId, int sinDate, bool flag = true, bool isDeletedPtHokenInf = false);
        void ProcessKohiId(int hpId, long ptId, int kohiId, long kohiSeqNo, ref int secondNum, ref int maruchoCount, ref int hokenSbtKbn, ref string houbetu);

        List<HokenInfModel> FindHokenInfByPtId(int hpId, long ptId, int sinDate = 0, int isDeleted = DeleteTypes.None);

        void UpsertHokenInf(int hpId, long ptId, int userId, int hokenId, int kohi1Id, int kohi2Id, int kohi3Id, int kohi4Id);

        KohiInfModel? GetKohiDetail(int hpId, long ptId, int seqNo, int hokenId, int sinDate);

        (bool resultSave, int kohiId, long onlineConfirmHistoryId) SaveKohi(int hpId, int userId, long ptId, KohiInfModel kohiModel, List<LimitListModel> limitListModels, List<PatientInforConfirmOnlineDto> patientInfoFields, PtKyuseiModel? ptKyuseiModel, OnlineConfirmationHistoryModel onlineCmfHisModel, bool isConfirmOnline, EndDateModel? endDateModel);

        HokenInfModel GetHokenInf(int hpId, long ptId, int hokenId);

        bool DeleteHokenInf(int hpId, int userId, long ptId, int seqNo, int hokenId);

        bool DeleteKohi(int hpId, int userId, long ptId, int seqNo, int hokenId);

        int CheckDeleteHoken(int hpId, long ptId, int hokenId, int kohiId);

        bool IsValidPeriod(int hpId, long ptId, int sinDate, int hokenId);

        int HasElderHoken(int sinDate, int hpId, long ptId, int ptInfBirthday);

        (int, List<int>) GetPtHokenInfByHokenPtId(int hpId, long ptId, int hokenPid);

        (KohiInfModel? KohiInf, bool IsDifference) HasKohiInfoDifference(int hpId, long ptId, string futansyaNo, string jyukyusyaNo, int? startDate, int? endDate, int? selfPayAmount, int? birthDay);

        HokenInfModel? GetHokenInf(int hpId, long ptId, string hokensyaNo, string kigo, string bango, string? edaNo, int? honkeKbn, int kofuDate, int startDate, int? endDate, int age, int sinDate, string? kogakuValue, bool isApplicable);

        bool HasMaruchoDifference(long ptId, int hpId, int prefNo, int hokenNo, int hokenEdaNo, int? startDate, int? endDate);

        string GetKogakuValue(int age, string hokensyaNo, int sindate, string? kogakuKbn);

        List<InsuranceSummaryModel> GetInsuranceSummaryList(int hpId, long ptId, int sinDate, bool isDeleted = false, bool isCache = false);

        void DeleteKeyInsuranceList(int hpId, long ptId);
    }
}
