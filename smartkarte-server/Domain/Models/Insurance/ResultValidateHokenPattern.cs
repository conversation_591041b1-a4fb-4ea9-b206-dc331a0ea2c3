﻿using Domain.Constant;

namespace Domain.Models.Insurance
{
    public class ResultValidateHokenPattern<T>
    {
        public ResultValidateHokenPattern()
        {
            Title = string.Empty;
            MessageTitle = string.Empty;
            MessageContent = string.Empty;
            InsuranceTitle = string.Empty;
            InsuranceType = string.Empty;
            StartDate = 0;
            EndDate = 0;
            TypeMessage = 0;
            DisplayTextMst = string.Empty;
            FieldName = string.Empty;
        }

        public ResultValidateHokenPattern(T status, string messageTitle, int typeMessage)
        {
            Status = status;
            MessageTitle = messageTitle;
            TypeMessage = typeMessage;
            Title = string.Empty;
            MessageContent = string.Empty;
            InsuranceTitle = string.Empty;
            InsuranceType = string.Empty;
            StartDate = 0;
            EndDate = 0;
            DisplayTextMst = string.Empty;
            FieldName = string.Empty;
        }

        public ResultValidateHokenPattern(T status, string messageTitle, string fieldName, int typeMessage)
        {
            Status = status;
            MessageTitle = messageTitle;
            FieldName = fieldName;
            TypeMessage = typeMessage;
            Title = string.Empty;
            MessageContent = string.Empty;
            InsuranceTitle = string.Empty;
            InsuranceType = string.Empty;
            StartDate = 0;
            EndDate = 0;
            DisplayTextMst = string.Empty;
        }

        public ResultValidateHokenPattern(T status, string title, string messageTitle, string messageContent, int typeMessage)
        {
            Status = status;
            Title = title;
            MessageTitle = messageTitle;
            MessageContent = messageContent;
            TypeMessage = typeMessage;
            InsuranceTitle = string.Empty;
            InsuranceType = string.Empty;
            StartDate = 0;
            EndDate = 0;
            DisplayTextMst = string.Empty;
            FieldName = string.Empty;
        }

        public ResultValidateHokenPattern(T status, string title, string messageTitle, string messageContent, string insuranceTitle, string insuranceType, string displayTextMst, int typeMessage)
        {
            Status = status;
            Title = title;
            MessageTitle = messageTitle;
            MessageContent = messageContent;
            InsuranceTitle = insuranceTitle;
            InsuranceType = insuranceType;
            DisplayTextMst = displayTextMst;
            TypeMessage = typeMessage;
            FieldName = string.Empty;
        }

        public ResultValidateHokenPattern(T status, string title, string messageTitle, string messageContent, string insuranceTitle, string insuranceType, string displayTextMst,  int startDate, int endDate, int typeMessage)
        {
            Status = status;
            Title = title;
            MessageTitle = messageTitle;
            MessageContent = messageContent;
            InsuranceTitle = insuranceTitle;
            InsuranceType = insuranceType;
            StartDate = startDate;
            EndDate = endDate;
            TypeMessage = typeMessage;
            DisplayTextMst = displayTextMst;
            FieldName = string.Empty;
        }

        public T Status { get; private set; }
        public string Title { get; set; }
        public string MessageTitle { get; set; }
        public string MessageContent { get; set; }
        public string InsuranceTitle { get; set; }
        public string InsuranceType { get; set; }
        public string DisplayTextMst { get; set; }
        public int StartDate { get; private set; }
        public int EndDate { get; private set; }
        public string FieldName { get; private set; }
        public int TypeMessage { get; private set; }
    }
}
