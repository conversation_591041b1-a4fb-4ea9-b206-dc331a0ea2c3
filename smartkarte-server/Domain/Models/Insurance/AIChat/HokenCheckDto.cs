﻿using System.Globalization;
using System.Text.Json.Serialization;

namespace Domain.Models.Insurance.AIChat
{
    public class HokenCheckDto
    {
        public long PtId { get; private set; }

        public int HokenGrp { get; private set; }

        public int HokenId { get; private set; }

        public long SeqNo { get; private set; }

        public int CheckId { get; private set; }

        public string CheckName { get; private set; }

        public string CheckComment { get; private set; }

        public int CheckDate { get; private set; }

        public string CheckMachine { get; private set; }

        public int IsDeleted { get; private set; }

        public int OnlineConfirmationId { get; private set; }

        public HokenCheckDto(long ptId, long seqNo, int hokenGrp, int hokenId, int confirmDate, int checkId, string checkComment, string checkName)
        {
            PtId = ptId;
            SeqNo = seqNo;
            HokenGrp = hokenGrp;
            HokenId = hokenId;
            CheckDate = confirmDate;
            CheckId = checkId;
            CheckMachine = string.Empty;
            CheckName = checkName;
            CheckComment = checkComment;
        }

        public HokenCheckDto(long ptId, long seqNo, int hokenGrp, int hokenId, int confirmDate, int checkId, string checkComment, string checkName, int onlineConfirmationId)
        {
            PtId = ptId;
            SeqNo = seqNo;
            HokenGrp = hokenGrp;
            HokenId = hokenId;
            CheckDate = confirmDate;
            CheckId = checkId;
            CheckMachine = string.Empty;
            CheckName = checkName;
            CheckComment = checkComment;
            OnlineConfirmationId = onlineConfirmationId;
        }

    }
}
