﻿using Helper.Constants;
using System.Text.Json.Serialization;

namespace Domain.Models.Insurance.AIChat
{
    public class HokenInfDto
    {
        [JsonConstructor]
        public HokenInfDto(
            int hpId, long ptId, int hokenId, long seqNo, int hokenNo, int hokenEdaNo,
            int hokenKbn, string hokensyaNo, string kigo, string bango, string edaNo,
            int honkeKbn, int startDate, int endDate, int checkDate, int sinDate, int isDeleted,
            List<HokenCheckDto> hokenCheck, int? hokenSbtKbn, string? hokenSName, string houbetu,
            int hokenNoMaster, int hokenEdaNoMaster, string? rousaiKofuNo, string? jibaiHokenName,
            int sinDateRecentUse, string hokenMstHokenNameCd, string insuredName, int sikakuDate,
            int kofuDate, string hokensyaName, int kogakuKbn, int tasukaiYm, int tokureiYm1, int tokureiYm2,
            int genmenKbn, int genmenRate, int genmenGaku, int syokumuKbn, int keizokuKbn, string? tokki1,
            string? tokki2, string? tokki3, string? tokki4, string? tokki5, int onlineConfirmationCheckDate,
            string rousaiRoudouCd, int rousaiSaigaiKbn, string rousaiKantokuCd, int rousaiSyobyoDate,
            int ryoyoStartDate, int ryoyoEndDate, string rousaiSyobyoCd, string rousaiJigyosyoName,
            string rousaiPrefName, string rousaiCityName, int rousaiReceCount,
            string jibaiHokenTanto, string jibaiHokenTel, int jibaiJyusyouDate, List<RousaiTenkiModel> listRousaiTenki, 
            int futanRate, string roudouCd, string roudouName, int countKohi = 0)
        {
            HpId = hpId;
            PtId = ptId;
            HokenId = hokenId;
            SeqNo = seqNo;
            HokenNo = hokenNo;
            HokenEdaNo = hokenEdaNo;
            HokenKbn = hokenKbn;
            HokensyaNo = hokensyaNo;
            Kigo = kigo;
            Bango = bango;
            EdaNo = edaNo;
            HonkeKbn = honkeKbn;
            StartDate = startDate;
            EndDate = endDate;
            SinDate = sinDate;
            IsDeleted = isDeleted;
            CheckDate = checkDate;
            HokenCheck = hokenCheck;
            HokenSbtKbn = hokenSbtKbn;
            HokenSName = hokenSName;
            OnlineConfirmCheckDate = onlineConfirmationCheckDate;
            Houbetu = houbetu;
            HokenNoMaster = hokenNoMaster;
            HokenEdaNoMaster = hokenEdaNoMaster;
            RousaiKofuNo = rousaiKofuNo;
            JibaiHokenName = jibaiHokenName;
            SinDateRecentUse = sinDateRecentUse;
            HokenMstHokenNameCd = hokenMstHokenNameCd;
            InsuredName = insuredName;
            SikakuDate = sikakuDate;
            KofuDate = kofuDate;
            HokensyaName = hokensyaName;
            KogakuKbn = kogakuKbn;
            TasukaiYm = tasukaiYm;
            TokureiYm1 = tokureiYm1;
            TokureiYm2 = tokureiYm2;
            GenmenKbn = genmenKbn;
            GenmenRate = genmenRate;
            GenmenGaku = genmenGaku;
            SyokumuKbn = syokumuKbn;
            KeizokuKbn = keizokuKbn;
            Tokki1 = tokki1;
            Tokki2 = tokki2;
            Tokki3 = tokki3;
            Tokki4 = tokki4;
            Tokki5 = tokki5;
            RousaiRoudouCd = rousaiRoudouCd;
            RousaiSaigaiKbn = rousaiSaigaiKbn;
            RousaiKantokuCd = rousaiKantokuCd;
            RousaiSyobyoDate = rousaiSyobyoDate;
            RyoyoStartDate = ryoyoStartDate;
            RyoyoEndDate = ryoyoEndDate;
            RousaiSyobyoCd = rousaiSyobyoCd;
            RousaiJigyosyoName = rousaiJigyosyoName;
            RousaiPrefName = rousaiPrefName;
            RousaiCityName = rousaiCityName;
            RousaiReceCount = rousaiReceCount;
            JibaiHokenTanto = jibaiHokenTanto;
            JibaiHokenTel = jibaiHokenTel;
            JibaiJyusyouDate = jibaiJyusyouDate;
            ListRousaiTenki = listRousaiTenki;
            FutanRate = futanRate;
            RoudouCd = roudouCd;
            RoudouName = roudouName;
            CountKohi = countKohi;
        }

        public HokenInfDto(int hpId, long ptId, int hokenId, long seqNo, int hokenNo, int hokenEdaNo, int hokenKbn, string hokensyaNo, string kigo, string bango, string edaNo, int honkeKbn, int startDate, int endDate, int checkDate, int sinDate, int isDeleted, List<HokenCheckDto> hokenCheck, int? hokenSbtKbn, string? hokenSName, string houbetu, int onlineConfirmationCheckDate = 0)
        {
            HpId = hpId;
            PtId = ptId;
            HokenId = hokenId;
            SeqNo = seqNo;
            HokenNo = hokenNo;
            HokenEdaNo = hokenEdaNo;
            HokenKbn = hokenKbn;
            HokensyaNo = hokensyaNo;
            Kigo = kigo;
            Bango = bango;
            EdaNo = edaNo;
            HonkeKbn = honkeKbn;
            StartDate = startDate;
            EndDate = endDate;
            SinDate = sinDate;
            IsDeleted = isDeleted;
            CheckDate = checkDate;
            HokenCheck = hokenCheck;
            HokenSbtKbn = hokenSbtKbn;
            HokenSName = hokenSName;
            OnlineConfirmCheckDate = onlineConfirmationCheckDate;
            Houbetu = houbetu;
            HokenMstHokenNameCd = string.Empty;
            InsuredName = string.Empty;
            HokensyaName = string.Empty;
        }
        public HokenInfDto() { }

        public int HpId { get; private set; }

        public long PtId { get; private set; }

        public int HokenId { get; private set; }

        public long SeqNo { get; private set; }

        public int HokenNo { get; private set; }

        public int HokenEdaNo { get; private set; }

        public int HokenKbn { get; private set; }

        public string HokensyaNo { get; private set; } = string.Empty;

        public string Kigo { get; private set; } = string.Empty;

        public string Bango { get; private set; } = string.Empty;

        public string EdaNo { get; private set; } = string.Empty;

        public int HonkeKbn { get; private set; }

        public int StartDate { get; private set; }

        public int EndDate { get; private set; }

        public int KofuDate { get; private set; }

        public int SinDate { get; private set; }

        public int IsDeleted { get; private set; }

        public int? HokenMstId { get; private set; }

        public int? HokenSbtKbn { get; private set; }

        public string? HokenSName { get; private set; }

        public int CheckDate { get; private set; }

        public List<HokenCheckDto> HokenCheck { get; private set; }

        public int OnlineConfirmCheckDate { get; private set; }

        public string? RousaiKofuNo { get; private set; }

        public string? JibaiHokenName { get; private set; }

        public bool IsExpirated
        {
            get
            {
                return !(StartDate <= SinDate && EndDate >= SinDate);
            }
        }
        public string Houbetu { get; private set; } = string.Empty;

        public bool IsShaho
        {
            // not nashi
            get => HokenKbn == 1 && Houbetu != HokenConstant.HOUBETU_NASHI;
        }
        public bool IsKokuho
        {
            get => HokenKbn == 2;
        }
        public bool IsHoken => IsShaho || IsKokuho;

        public bool IsRousai => HokenKbn == 11 || HokenKbn == 12 || HokenKbn == 13;

        public bool IsJibai => HokenKbn == 14;

        public int HokenNoMaster { get; private set; }

        public int HokenEdaNoMaster { get; private set; }

        public string HokenMstHokenNameCd { get; private set; }

        public string InsuredName { get; private set; }

        public int SikakuDate { get; private set; }

        public string HokensyaName { get; private set; }

        public int KogakuKbn { get; private set; }

        public int TasukaiYm { get; private set; }

        public int TokureiYm1 { get; private set; }

        public int TokureiYm2 { get; private set; }

        public int GenmenKbn { get; private set; }

        public int GenmenRate { get; private set; }

        public int GenmenGaku { get; private set; }

        public int SyokumuKbn { get; private set; }

        public int KeizokuKbn { get; private set; }

        public string? Tokki1 { get; private set; }

        public string? Tokki2 { get; private set; }

        public string? Tokki3 { get; private set; }

        public string? Tokki4 { get; private set; }

        public string? Tokki5 { get; private set; }


        public string RousaiRoudouCd { get; private set; } = string.Empty;

        public int RousaiSaigaiKbn { get; private set; }

        public string RousaiKantokuCd { get; private set; } = string.Empty;

        public int RousaiSyobyoDate { get; private set; }

        public int RyoyoStartDate { get; private set; }

        public int RyoyoEndDate { get; private set; }

        public string RousaiSyobyoCd { get; private set; } = string.Empty;

        public string RousaiJigyosyoName { get; private set; } = string.Empty;

        public string RousaiPrefName { get; private set; } = string.Empty;

        public string RousaiCityName { get; private set; } = string.Empty;

        public int RousaiReceCount { get; private set; }

        public string JibaiHokenTanto { get; private set; } = string.Empty;

        public string JibaiHokenTel { get; private set; } = string.Empty;

        public int JibaiJyusyouDate { get; private set; }

        public List<RousaiTenkiModel> ListRousaiTenki { get; private set; } = new List<RousaiTenkiModel>();

        public int FutanRate { get; private set; }

        public string RoudouCd { get; private set; }

        public string RoudouName { get; private set; }

        public int CountKohi { get; private set; }

        public bool IsDefault { get; set; }

        private bool IsHaveHokenMst
        {
            get
            {
                return HokenNoMaster != 0 && HokenEdaNoMaster != 0;
            }
        }

        public bool IsJihi
        {
            get
            {
                if (IsHaveHokenMst)
                {
                    return HokenSbtKbn == 8;
                }
                return HokenKbn == 0 && (Houbetu == HokenConstant.HOUBETU_JIHI_108 || Houbetu == HokenConstant.HOUBETU_JIHI_109);
            }
        }
        public int SinDateRecentUse { get; private set; }

        public string HokenSentaku
        {
            get
            {
                var result = GetHokenName();

                if (!string.IsNullOrEmpty(HokensyaNo) || HokenKbn >= 10)
                {
                    result += " ";
                    result += HokensyaNo;
                }

                if (IsExpirated)
                {
                    result = "×" + result + "（有効期限切れ）";
                }
                return result;
            }
        }
        public bool IsNashi => Houbetu == HokenConstant.HOUBETU_NASHI;

        public string GetHokenName()
        {
            string result = string.Empty;
            result += HokenId.ToString().PadLeft(2, '0');
            result += " ";
            if (string.IsNullOrEmpty(HokensyaNo) && HokenKbn < 10)
            {
                // nashi
                if (HokenKbn == 1 && HokenSbtKbn == 0)
                {
                    result += HokenSName;
                    //GetHonkeKbnSuff(ref result);
                    return result;
                }
                
            }
            switch (HokenKbn)
            {
                case 0:
                    result += HokenSName;
                    GetHonkeKbnSuff(ref result);
                    break;
                case 1:
                    result += "社保";
                    GetHonkeKbnSuff(ref result);
                    break;
                case 2:
                    if (HokensyaNo.Length == 8 &&
                        HokensyaNo.StartsWith("39"))
                    {
                        result += "後期";
                    }
                    else if (HokensyaNo.Length == 8 &&
                        HokensyaNo.StartsWith("67"))
                    {
                        result += "退職";
                        GetHonkeKbnSuff(ref result);
                    }
                    else
                    {
                        result += "国保";
                        GetHonkeKbnSuff(ref result);
                    }

                    break;
                case 11:
                    result += "労災（短期給付）";
                    break;
                case 12:
                    result += "労災（傷病年金）";
                    break;
                case 13:
                    result += "労災（アフターケア）";
                    break;
                case 14:
                    result += "自賠責";
                    break;
            }

            return result;
        }

        private void GetHonkeKbnSuff(ref string result)
        {
            if (HonkeKbn != 0)
            {
                result += "(";
                if (HonkeKbn == 1)
                {
                    result += "本人";
                }
                else
                {
                    result += "家族";
                }
                result += ")";
            }
        }
    }
}
