﻿namespace Domain.Models.Insurance.AIChat
{
    public class InsuranceDataDto
    {
        public InsuranceDataDto(List<HokenPatternDto> listInsurance, List<HokenInfDto> listHokenInf, List<KohiInfDto> listKohi, int maxIdHokenInf, int maxIdHokenKohi, int maxPidHokenPattern)
        {
            ListHokenPattern = listInsurance;
            ListHokenInf = listHokenInf;
            ListKohi = listKohi;
            MaxIdHokenInf = maxIdHokenInf;
            MaxIdHokenKohi = maxIdHokenKohi;
            MaxPidHokenPattern = maxPidHokenPattern;
        }

        public InsuranceDataDto()
        {
            ListHokenPattern = new List<HokenPatternDto>();
            ListHokenInf = new List<HokenInfDto>();
            ListKohi = new List<KohiInfDto>();
        }

        public List<HokenPatternDto> ListHokenPattern { get; private set; }

        public List<HokenInfDto> ListHokenInf { get; private set; }

        public List<KohiInfDto> ListKohi { get; private set; }

        public int MaxIdHokenInf { get; private set; }

        public int MaxIdHokenKohi { get; private set; }

        public int MaxPidHokenPattern { get; private set; }
    }
}
