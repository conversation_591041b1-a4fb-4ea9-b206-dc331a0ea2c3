﻿using Newtonsoft.Json;

namespace Domain.Models.Insurance
{
    public class SaveHokenCheckDto
    {
        [JsonConstructor]
        public SaveHokenCheckDto(int hokenId, bool isHokenGroupKohi, int confirmDate)
        {
            HokenId = hokenId;
            IsHokenGroupKohi = isHokenGroupKohi;
            ConfirmDate = confirmDate;
        }

        public int HokenId { get; private set; }
        public bool IsHokenGroupKohi { get; private set; }
        public int ConfirmDate { get; private set; }

    }
}
