﻿using System.Text.Json.Serialization;

namespace Domain.Models.Insurance
{
    public class ConfirmDateModel
    {
        public long PtId { get; private set; }

        public int HokenGrp { get; private set; }

        public int HokenId { get; private set; }

        public long SeqNo { get; private set; }

        public int CheckId { get; private set; }

        public string CheckName { get; private set; }

        public string CheckComment { get; private set; }

        public int ConfirmDate { get; private set; }

        public string CheckMachine { get; private set; }

        public int IsDeleted { get; private set; }

        public int OnlineConfirmationId { get; private set; }

        public ConfirmDateModel(long ptId, int hokenGrp, int hokenId, DateTime confirmDate, int checkId, string checkComment)
        {
            PtId = ptId;
            HokenGrp = hokenGrp;
            HokenId = hokenId;
            ConfirmDate = int.Parse(confirmDate.ToString("yyyyMMdd"));
            CheckId = checkId;
            CheckMachine = string.Empty;
            CheckName = string.Empty;
            CheckComment = checkComment;
        }

        public ConfirmDateModel(int hokenGrp, int hokenId, DateTime confirmDate, int checkId, string checkMachine, string checkComment, int isDeleted)
        {
            HokenGrp = hokenGrp;
            HokenId = hokenId;
            ConfirmDate = int.Parse(confirmDate.ToString("yyyyMMdd"));
            CheckId = checkId;
            CheckMachine = checkMachine;
            IsDeleted = isDeleted;
            CheckName = string.Empty;
            CheckComment = string.Empty;
        }

        public ConfirmDateModel(int hokenGrp, int hokenId, long seqNo, int checkId, string checkName, string checkComment, DateTime confirmDate, int onlineConfirmationId = 0)
        {
            HokenGrp = hokenGrp;
            HokenId = hokenId;
            SeqNo = seqNo;
            CheckId = checkId;
            CheckName = checkName;
            CheckComment = checkComment;
            CheckMachine = string.Empty;
            ConfirmDate = int.Parse(confirmDate.ToString("yyyyMMdd"));
            OnlineConfirmationId = onlineConfirmationId;
        }

        public ConfirmDateModel(long ptId, int hokenGrp, int hokenId, DateTime confirmDate, int checkId, string checkComment, long seqNo)
        {
            PtId = ptId;
            HokenGrp = hokenGrp;
            HokenId = hokenId;
            ConfirmDate = int.Parse(confirmDate.ToString("yyyyMMdd"));
            CheckId = checkId;
            CheckMachine = string.Empty;
            CheckName = string.Empty;
            CheckComment = checkComment;
            SeqNo = seqNo;
        }

        public ConfirmDateModel(DateTime confirmDate)
        {
            ConfirmDate = int.Parse(confirmDate.ToString("yyyyMMdd"));
        }

        [JsonConstructor]
        public ConfirmDateModel(int hokenGrp, int hokenId, long seqNo, int checkId, string checkName, string checkComment, int confirmDate, int onlineConfirmationId = 0)
        {
            HokenGrp = hokenGrp;
            HokenId = hokenId;
            SeqNo = seqNo;
            CheckId = checkId;
            CheckName = checkName;
            CheckComment = checkComment;
            ConfirmDate = confirmDate;
            CheckMachine = string.Empty;
            OnlineConfirmationId = onlineConfirmationId;
        }

        public ConfirmDateModel(long ptId, int hokenGrp, int hokenId, long seqNo, int checkId, string checkComment, DateTime confirmDate, string checkMachine, int isDeleted)
        {
            PtId = ptId;
            HokenGrp = hokenGrp;
            HokenId = hokenId;
            SeqNo = seqNo;
            CheckId = checkId;
            CheckName = string.Empty;
            CheckComment = checkComment;
            ConfirmDate = int.Parse(confirmDate.ToString("yyyyMMdd"));
            CheckMachine = checkMachine;
            IsDeleted = isDeleted;
            OnlineConfirmationId = 0;
        }
    }
}
