﻿namespace Domain.Models.KarteInf;

public class FileInfModel
{
    public FileInfModel(long raiinNo, long seqNo, bool isSchema, string linkFile, bool isDelete, DateTime createDate, DateTime updateDate, string createName, string updateName, string dspFileName, string fileName)
    {
        RaiinNo = raiinNo;
        SeqNo = seqNo;
        IsSchema = isSchema;
        LinkFile = linkFile;
        IsDelete = isDelete;
        CreateDate = createDate;
        UpdateDate = updateDate;
        CreateName = createName;
        UpdateName = updateName;
        DspFileName = dspFileName;
        FileName = fileName;
    }

    public FileInfModel(long raiinNo, long seqNo, bool isSchema, string linkFile)
    {
        RaiinNo = raiinNo;
        SeqNo = seqNo;
        IsSchema = isSchema;
        LinkFile = linkFile;
        IsDelete = false;
        CreateName = string.Empty;
        UpdateName = string.Empty;
    }

    public FileInfModel(bool isSchema, string linkFile)
    {
        RaiinNo = 0;
        SeqNo = 0;
        IsSchema = isSchema;
        LinkFile = linkFile;
        IsDelete = false;
        CreateName = string.Empty;
        UpdateName = string.Empty;
    }

    public FileInfModel(int seqNo, bool isSchema, long raiinNo, string linkFile, DateTime createDate, DateTime updateDate, string createName, string updateName, string dspFileName, string fileName, string s3FileName)
    {
        SeqNo = seqNo;
        IsSchema = isSchema;
        RaiinNo = raiinNo;
        LinkFile = linkFile;
        CreateDate = createDate;
        UpdateDate = updateDate;
        CreateName = createName;
        UpdateName = updateName;
        DspFileName = dspFileName;
        FileName = fileName;
        S3FileName = s3FileName;
    }

    public FileInfModel(int seqNo, bool isSchema, long raiinNo, string linkFile, DateTime createDate, DateTime updateDate, string createName, string updateName, string dspFileName, string fileName, string s3FileName, bool isDelete, int categoryCd, string memo)
    {
        SeqNo = seqNo;
        IsSchema = isSchema;
        RaiinNo = raiinNo;
        LinkFile = linkFile;
        CreateDate = createDate;
        UpdateDate = updateDate;
        CreateName = createName;
        UpdateName = updateName;
        DspFileName = dspFileName;
        FileName = fileName;
        S3FileName = s3FileName;
        IsDelete = isDelete;
        CategoryCd = categoryCd;
        Memo = memo;
    }

    public void SetFilePath(string path)
    {
        FilePath = path;
    }

    public long RaiinNo { get; private set; }

    public long SeqNo { get; private set; }

    public bool IsSchema { get; private set; }

    public string LinkFile { get; private set; } = string.Empty;

    public bool IsDelete { get; private set; }

    public DateTime CreateDate { get; private set; }

    public DateTime UpdateDate { get; private set; }

    public string CreateName { get; private set; } = string.Empty;

    public string UpdateName { get; private set; } = string.Empty;

    public string DspFileName { get; private set; } = string.Empty;

    public string FileName { get; private set; } = string.Empty;

    public string S3FileName { get; private set; } = string.Empty;

    public string FilePath { get; private set; } = string.Empty;

    public string Memo { get; private set; } = string.Empty;

    public int CategoryCd { get; private set; }
}
