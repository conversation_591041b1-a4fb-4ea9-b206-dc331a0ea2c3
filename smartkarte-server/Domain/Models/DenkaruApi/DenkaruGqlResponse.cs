using Newtonsoft.Json;

namespace Domain.Models.DenkaruApi
{
    public class DenkaruGqlResponse<T>
    {
        public T? Data { get; set; }
        public List<GraphqlError> Errors { get; set; } = new List<GraphqlError>();
    }

    public class GraphqlError
    {
        public string? Message { get; set; }
        public Extensions? Extensions { get; set; }
    }

    public class Extensions
    {
        public string? Code { get; set; }
        public string? UserMessage { get; set; }
    }

    public class VerifyTokenResponse
    {
        [JsonProperty("verifyToken")]
        public VerifyTokenData? VerifyToken { get; set; }
    }

    public class VerifyTokenData
    {
        [JsonProperty("isValid")]
        public bool IsValid { get; set; }
    }
}