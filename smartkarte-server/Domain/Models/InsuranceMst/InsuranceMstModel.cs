﻿namespace Domain.Models.InsuranceMst
{
    public class InsuranceMstModel
    {
        public InsuranceMstModel()
        {
            ListTokkiMstModel = new List<TokkiMstModel>();
            HokenKogakuKbnDict = new Dictionary<int, string>();
            KantokuMstData = new List<KantokuMstModel>();
            ByomeiMstAftercareData = new List<ByomeiMstAftercareModel>();
            RoudouMst = new List<RoudouMstModel>();
            HokenMstAlLData = new List<HokenMstModel>();
            RouDouNameList = new List<string>();
        }

        public InsuranceMstModel(List<TokkiMstModel> listTokkiMstModel, Dictionary<int, string> hokenKogakuKbnDict, List<KantokuMstModel> kantokuMstData, List<ByomeiMstAftercareModel> byomeiMstAftercareData, List<RoudouMstModel> roudouMst, List<HokenMstModel> hokenMstAlLData, List<string> rouDouNameList)
        {
            ListTokkiMstModel = listTokkiMstModel;
            HokenKogakuKbnDict = hokenKogakuKbnDict;
            KantokuMstData = kantokuMstData;
            ByomeiMstAftercareData = byomeiMstAftercareData;
            RoudouMst = roudouMst;
            HokenMstAlLData = hokenMstAlLData;
            RouDouNameList = rouDouNameList;
        }

        public List<TokkiMstModel> ListTokkiMstModel { get; private set; }

        public Dictionary<int, string> HokenKogakuKbnDict { get; private set; }

        public List<KantokuMstModel> KantokuMstData { get; private set; }

        public List<ByomeiMstAftercareModel> ByomeiMstAftercareData { get; private set; }

        public List<RoudouMstModel> RoudouMst { get; private set; }

        public List<HokenMstModel> HokenMstAlLData { get; private set; }

        public List<string> RouDouNameList { get; private set; }
    }
}
