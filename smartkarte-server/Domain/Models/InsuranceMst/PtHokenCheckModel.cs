﻿using Entity.Tenant;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.Models.InsuranceMst
{
    public class PtHokenCheckModel
    {
        public PtHokenCheck PtHokenCheck { get; }

        public PtHokenCheckModel(PtHokenCheck ptHokenCheck)
        {
            PtHokenCheck = ptHokenCheck;
        }

        /// <summary>
        /// 病院コード
        /// </summary>
        public int HpId => PtHokenCheck.HpId;

        /// <summary>
        /// 患者ID
        /// </summary>
        public long PtId => PtHokenCheck.PtID;

        /// <summary>
        /// 保険グループ
        ///     1:主保険・労災・自賠
        ///     2:公費
        /// </summary>
        public int HokenGrp => PtHokenCheck.HokenGrp;

        /// <summary>
        /// 保険ID
        ///     患者別に保険情報を識別するための固有の番号
        /// </summary>
        public int HokenId => PtHokenCheck.HokenId;

        /// <summary>
        /// 連番
        /// </summary>
        public long SeqNo => PtHokenCheck.SeqNo;

        /// <summary>
        /// 確認日時
        /// </summary>
        public DateTime CheckDate => PtHokenCheck.CheckDate;

        /// <summary>
        /// 確認者コード
        /// </summary>
        public int CheckId => PtHokenCheck.CheckId;

        /// <summary>
        /// 確認端末
        /// </summary>
        public string CheckMachine => PtHokenCheck.CheckMachine ?? string.Empty;

        /// <summary>
        /// 確認コメント
        /// </summary>
        public string CheckCmt => PtHokenCheck.CheckCmt ?? string.Empty;

        /// <summary>
        /// 削除区分
        ///     1:削除
        /// </summary>
        public int IsDeleted => PtHokenCheck.IsDeleted;

        /// <summary>
        /// 作成日時
        /// </summary>
        public DateTime CreateDate => PtHokenCheck.CreateDate;

        /// <summary>
        /// 作成者
        /// </summary>
        public int CreateId => PtHokenCheck.CreateId;

        /// <summary>
        /// 作成端末
        /// </summary>
        public string CreateMachine => PtHokenCheck.CreateMachine ?? string.Empty;

        /// <summary>
        /// 更新日時
        /// </summary>
        public DateTime UpdateDate => PtHokenCheck.UpdateDate;

        /// <summary>
        /// 更新者
        /// </summary>
        public int UpdateId => PtHokenCheck.UpdateId;

        /// <summary>
        /// 更新端末
        /// </summary>
        public string UpdateMachine => PtHokenCheck.UpdateMachine ?? string.Empty;
    }
}
