﻿namespace Domain.Models.InsuranceMst
{
    public class GetInsuranceCommonComboboxModel
    {
        public GetInsuranceCommonComboboxModel() { }
        public GetInsuranceCommonComboboxModel(InsuranceCardComboboxModel insuranceCardComboboxModel, PublicExpenseComboboxModel publicExpenseComboboxModel, WorkRelatedInjuryComboboxModel workRelatedInjuryComboboxModel, AutomobileInsuranceComboboxModel automobileInsuranceComboboxModel)
        {
            InsuranceCardComboboxModel = insuranceCardComboboxModel;
            PublicExpenseComboboxModel = publicExpenseComboboxModel;
            WorkRelatedInjuryComboboxModel = workRelatedInjuryComboboxModel;
            AutomobileInsuranceComboboxModel = automobileInsuranceComboboxModel;
        }

        public InsuranceCardComboboxModel InsuranceCardComboboxModel { get; private set; }
        public PublicExpenseComboboxModel PublicExpenseComboboxModel { get; private set; }
        public WorkRelatedInjuryComboboxModel WorkRelatedInjuryComboboxModel { get; private set; }
        public AutomobileInsuranceComboboxModel AutomobileInsuranceComboboxModel { get; private set; }
    }
    public class InsuranceCardComboboxModel
    {
        /// <summary>
        /// 保険の種類
        /// </summary>
        public List<HokenMstModel> HokenMstAllData { get; private set; }

        /// <summary>
        /// 本人/家族
        /// </summary>
        public Dictionary<int, string> SelfFamily { get; private set; }

        /// <summary>
        /// 国保減免
        /// </summary>
        public Dictionary<int, string> InsuranceExemption { get; private set; }

        /// <summary>
        /// 職務上区分
        /// </summary>
        public Dictionary<int, string> JobClassification { get; private set; }

        /// <summary>
        /// 継続区分
        /// </summary>
        public Dictionary<int, string> ContinuationCategory { get; private set; }

        /// <summary>
        /// 特記事項
        /// </summary>
        public List<TokkiMstModel> ImportantNotes { get; private set; }

        public InsuranceCardComboboxModel(List<HokenMstModel> hokenMstModels, List<TokkiMstModel> tokkiMstModels)
        {
            HokenMstAllData = hokenMstModels;
            ImportantNotes = tokkiMstModels;
            SelfFamily = new Dictionary<int, string>
            {
                {1,"1 本人" },
                {2,"2 家族" }
            };
            InsuranceExemption = new Dictionary<int, string>
            {
                { 0, " " },
                { 1, "1 減額" },
                { 2, "2 免除" },
                { 3, "3 支払猶予" },
                { 4, "4 自立支援減免" }
            };
            JobClassification = new Dictionary<int, string>
            {
                { 0, "" },
                { 1, "1 職務上" },
                { 2, "2 下船後３月" },
                { 3, "3 通勤災害" }
            };
            ContinuationCategory = new Dictionary<int, string>
            {
                {0,"" },
                {1,"1 任意継続" }
            };
        }

    }

    public class PublicExpenseComboboxModel
    {
        public PublicExpenseComboboxModel(List<HokenMstModel> hokenMstModels)
        {
            HokenMstAllData = hokenMstModels;
        }
        /// <summary>
        /// 保険の種類
        /// </summary>
        public List<HokenMstModel> HokenMstAllData { get; private set; }

    }

    public class WorkRelatedInjuryComboboxModel
    {
        /// <summary>
        /// 制度種別
        /// </summary>
        public Dictionary<int, string> SystemType { get; private set; }

        /// <summary>
        /// 負担率
        /// </summary>
        public List<HokenMstModel> Contributionrate { get; private set; }

        /// <summary>
        /// 労働局
        /// </summary>
        public List<RoudouMstModel> RoudouMst { get; private set; }

        /// <summary>
        /// 
        /// </summary>
        public List<string> RoudouNameList { get; private set; }

        /// <summary>
        /// 災害区分
        /// </summary>
        public Dictionary<int, string> DisasterClassification { get; private set; }

        /// <summary>
        /// 監督署
        /// </summary>
        public List<KantokuMstModel> KantokuMstData { get; private set; }

        /// <summary>
        /// 傷病コード
        /// </summary>
        public List<ByomeiMstAftercareModel> InjuryCode { get; private set; }

        /// <summary>
        /// 都道府県
        /// </summary>
        public Dictionary<int, string> Prefectures { get; private set; }

        /// <summary>
        /// 新継再別
        /// </summary>
        public Dictionary<int, string> RepeatedClassification { get; private set; }

        /// <summary>
        /// 転帰事由
        /// </summary>
        public Dictionary<int, string> Reason { get; private set; }

        public WorkRelatedInjuryComboboxModel(List<HokenMstModel> hokenMstModels, List<RoudouMstModel> roudouMstModels, List<KantokuMstModel> kantokuMstModels, List<ByomeiMstAftercareModel> byomeiMsts, List<string> roudouNameList)
        {
            Contributionrate = hokenMstModels;
            RoudouMst = roudouMstModels;
            KantokuMstData = kantokuMstModels;
            InjuryCode = byomeiMsts;
            RoudouNameList = roudouNameList;
            SystemType = new Dictionary<int, string>
            {
                {11,"1 短期給付" },
                {12,"2 傷病年金" },
                {13,"3 アフターケア" }
            };
            DisasterClassification = new Dictionary<int, string>
            {
                {0,"" },
                {1,"1 業務中の災害" },
                {2,"2 通勤途上の災害" }
            };
            RepeatedClassification = new Dictionary<int, string>
            {
                {1, "1 初診"},
                {3, "3 転医始診"},
                {5, "5 継続"},
                {7, "7 再発"}
            };
            Prefectures = new Dictionary<int, string>
            {
            };
            Reason = new Dictionary<int, string>
            {
                {1, "1 治癒"},
                {3, "3 継続"},
                {5, "5 転医"},
                {7, "7 中止"},
                {9, "9 死亡"}
            };
        }
    }

    public class AutomobileInsuranceComboboxModel
    {
        /// <summary>
        /// 負担率
        /// </summary>
        public List<HokenMstModel> Contributionrate { get; private set; }

        /// <summary>
        /// 転帰事由
        /// </summary>
        public Dictionary<int, string> Reason { get; private set; }
        public AutomobileInsuranceComboboxModel(List<HokenMstModel> hokenMstModels)
        {
            Contributionrate = hokenMstModels;
            Reason = new Dictionary<int, string>
            {
                {1, "1 治癒"},
                {3, "3 継続"},
                {5, "5 転医"},
                {7, "7 中止"},
                {9, "9 死亡"}
            };
        }
    }
}
