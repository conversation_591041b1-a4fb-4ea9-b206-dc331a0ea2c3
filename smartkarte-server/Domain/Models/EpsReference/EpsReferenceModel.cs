﻿using System.ComponentModel.DataAnnotations;

namespace Domain.Models.EpsReference
{
    public class EpsReferenceModel
    {
        /// <summary>
        /// 医療機関識別ID
        /// </summary>
        public int HpId { get; set; }

        /// <summary>
        /// 患者ID
        /// </summary>
        public long PtId { get; set; }

        /// <summary>
        /// 来院番号
        /// </summary>
        public long RaiinNo { get; set; }

        /// <summary>
        /// 診療日
        /// </summary>
        public int SinDate { get; set; }

        /// <summary>
        /// 処方箋ID
        ///     EPS_PRESCRIPTION.PRESCRIPTION_ID
        /// </summary>
        public string PrescriptionId { get; set; } = string.Empty;

        /// <summary>
        /// 処方内容控え
        ///     base64エンコードされた処方内容控え(PDF)
        /// </summary>
        public string PrescriptionReferenceInformation { get; set; } = string.Empty;

        /// <summary>
        /// 作成日時
        /// </summary>
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// 作成ID
        /// </summary>
        public int CreateId { get; set; }

        /// <summary>
        /// 作成端末
        /// </summary>
        [MaxLength(60)]
        public string CreateMachine { get; set; } = string.Empty;
        public EpsReferenceModel(int hpId, long ptId, long raiinNo, int sinDate, string prescriptionId, string prescriptionReferenceInformation, DateTime createDate, int createId, string createMachine)
        {
            HpId = hpId;
            PtId = ptId;
            RaiinNo = raiinNo;
            SinDate = sinDate;
            PrescriptionId = prescriptionId;
            PrescriptionReferenceInformation = prescriptionReferenceInformation;
            CreateDate = createDate;
            CreateId = createId;
            CreateMachine = createMachine;
        }
    }
}