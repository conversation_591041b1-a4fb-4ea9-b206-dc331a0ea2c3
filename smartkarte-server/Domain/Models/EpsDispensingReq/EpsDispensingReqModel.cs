﻿using System.ComponentModel.DataAnnotations.Schema;

namespace Domain.Models.EpsDispensingReq;

public class EpsDispensingReqModel
{
    public int HpId { get; set; }
    public long Id { get; set; }
    public string DispensingResultId { get; set; } = string.Empty;
    public int Status { get; set; }
    public string ErrorMessage { get; set; } = string.Empty;
    public DateTime CreateDate { get; set; }
    public int CreateId { get; set; }
    public string CreateMachine { get; set; } = string.Empty;
}