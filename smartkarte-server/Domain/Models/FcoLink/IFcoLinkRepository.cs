﻿using Domain.Common;


namespace Domain.Models.FcoLink
{
    public interface IFcoLinkRepository : IRepositoryBase
    {
        PatientInfoModel SearchPtInfByPtNum(long ptNum, int hpId);
        List<SeikyuModel> GetSeikyuInfList(int hpId, long ptId, int? sinDate);
        List<OrdInfModel> GetOdrInfList(int hpId, long ptId, long raiinNo, int sinDate);
        List<SinKouiDetailModel> GetSinKouiList(int hpId, long ptId, long raiinNo, int sinDate);
        bool IsExistsFcoApiKeys(int hpId, string apiKey);
        List<SyunoSeikyuModel> GetListSeikyuInf(int hpId, long ptId, List<long> raiinNoList);
        SyunoSeikyuModel? FcoSaveNyukin(SyunoSeikyuModel syunoSeikyu, int depositMethod);
        int SaveChanges();
    }
}