﻿using Domain.Models.Accounting;
using System.Text.Json.Serialization;

namespace Domain.Models.FcoLink;

public class SyunoSeikyuModel
{
    public SyunoSeikyuModel(int hpId, long ptId, int sinDate, int status, long raiinNo, long oyaRaiinNo, int nyukinKbn, int seikyuTensu, int adjustFutan, int seikyuGaku, string seikyuDetail, int newSeikyuTensu, int newAdjustFutan, int newSeikyuGaku, int raiinUketukeSbt, int totalPtFutan, string newSeikyuDetail)
    {
        HpId = hpId;
        PtId = ptId;
        SinDate = sinDate;
        Status = status;
        RaiinNo = raiinNo;
        OyaRaiinNo = oyaRaiinNo;
        NyukinKbn = nyukinKbn;
        SeikyuTensu = seikyuTensu;
        AdjustFutan = adjustFutan;
        SeikyuGaku = seikyuGaku;
        SeikyuDetail = seikyuDetail;
        NewSeikyuTensu = newSeikyuTensu;
        NewAdjustFutan = newAdjustFutan;
        NewSeikyuGaku = newSeikyuGaku;
        RaiinUketukeSbt = raiinUketukeSbt;
        TotalPtFutan = totalPtFutan;
        NewSeikyuDetail = newSeikyuDetail;
    }

    public SyunoSeikyuModel()
    {
        SeikyuDetail = string.Empty;
        NewSeikyuDetail = string.Empty;
    }

    public int HpId { get; private set; }

    public long PtId { get; private set; }

    public int SinDate { get; private set; }
    
    public int Status { get; private set; }

    public long RaiinNo { get; private set; }

    public long OyaRaiinNo { get; private set; }

    public int NyukinKbn { get; private set; }

    public int SeikyuTensu { get; private set; }

    public int AdjustFutan { get; private set; }

    public int SeikyuGaku { get; private set; }

    public string SeikyuDetail { get; private set; }

    public int NewSeikyuTensu { get; private set; }

    public int NewAdjustFutan { get; private set; }

    public int NewSeikyuGaku { get; private set; }

    public string NewSeikyuDetail { get; private set; }

    public int RaiinUketukeSbt { get; private set; }

    public int TotalPtFutan { get; private set; }

    public int NyukinGaku { get; set; }

    public int NyukinDate { get; set; }

}
