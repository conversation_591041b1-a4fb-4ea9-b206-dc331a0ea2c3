﻿using System.Text.Json.Serialization;

namespace Domain.Models.FcoLink
{
    public class PatientInfoModel
    {
        public PatientInfoModel()
        {}

        public PatientInfoModel(int hpId, long ptId, long ptNum, string kanaName, string name, string officeName)
        {
            HpId = hpId;
            PtId = ptId;
            PtNum = ptNum;
            KanaName = kanaName;
            Name = name;
            OfficeName = officeName;
        }

        public int HpId { get; private set; }
        public long PtId { get; private set; }
        public long PtNum { get; private set; }
        public string KanaName { get; private set; }
        public string Name { get; private set; }
        public string OfficeName { get; private set; }
        public bool IsEmpty => PtId == 0; 
    }
}