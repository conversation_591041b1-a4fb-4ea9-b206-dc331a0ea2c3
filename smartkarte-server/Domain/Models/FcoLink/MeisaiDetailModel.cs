﻿using Helper.Constants;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.Models.FcoLink
{
    public class SinKouiDetailModel
    {
        // パラメータなしのデフォルトコンストラクタ
        public SinKouiDetailModel() { }

        // すべてのプロパティを初期化するコンストラクタ
        public SinKouiDetailModel(long raiinNo, int sinDate, string cdKbn, int hokenId, int rpNo, int seqNo,
                                int entenKbn, double tenKei, int count, string itemCd, string itemName, double suryo,
                                string unitName, string recId, int cmtSbt)
        {
            RaiinNo = raiinNo;
            SinDate = sinDate;
            CdKbn = cdKbn;
            HokenId = hokenId;
            RpNo = rpNo;
            SeqNo = seqNo;
            EntenKbn = entenKbn;
            TenKei = tenKei;
            Count = count;
            ItemCd = itemCd;
            ItemName = itemName;
            Suryo = suryo;
            UnitName = unitName;
            RecId = recId;
            CmtSbt = cmtSbt;
        }

        public long RaiinNo { get; set; }
        public int SinDate { get; set; }
        public string CdKbn { get; set; } = string.Empty;
        public int HokenId { get; set; }
        public int RpNo { get; set; }
        public int SeqNo { get; set; }
        public int EntenKbn { get; set; }
        public double TenKei { get; set; }
        public int Count { get; set; }
        public string ItemCd { get; set; } = string.Empty;
        public string ItemName { get; set; } = string.Empty;
        public double Suryo { get; set; }
        public string UnitName { get; set; } = string.Empty;
        public string RecId { get; set; } = string.Empty;
        public int CmtSbt { get; set; }
    }
}
