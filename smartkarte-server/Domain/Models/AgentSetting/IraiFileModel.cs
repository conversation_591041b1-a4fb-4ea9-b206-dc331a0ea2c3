﻿using Domain.Models.KensaIrai.GetIraiFileDataDummy;

namespace Domain.Models.AgentSetting
{
    public class IraiFileModel
    {
        public IraiFileModel()
        {
            ContentFileModel = new();
        }

        public IraiFileModel(List<ContentFileModel> contentFileModel)
        {
            ContentFileModel = contentFileModel;
        }

        public List<ContentFileModel> ContentFileModel { get; private set; }
    }

    public class ContentFileModel
    {
        public ContentFileModel(string outputPath, List<string> iraiFileData, bool outputFile)
        {
            OutputPath = outputPath;
            IraiFileData = iraiFileData;
            OutputFile = outputFile;
        }

        public string OutputPath { get; private set; }

        public List<string> IraiFileData { get; private set; }

        public bool OutputFile { get; private set; }
    }
}
