﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.Constant
{
    public static class ErrorMessage
    {
        public const string MessageType_mChk00020 = "{0}が有効期限切れです。" + "\r\n" + "有効期限を確認してください。";
        public const string MessageType_mChk00080 = "{0}" + "\r\n" + "{1}を確認してください。";
        public const string MessageType_mChk00030 = "{0}が未確認です。" + "\r\n" + "{1}を確認してください。";
        public const string MessageType_mNG01010 = "{0}が正しくありません。";
        public const string MessageType_mInp00011 = "{0}の{1}を入力してください。";
        public const string MessageType_mSel01010 = "{0}を選択してください。";
        public const string MessageType_mInp00041 = "{0}は {1}を入力してください。";
        public const string MessageType_mFree00030 = "{0}";
        public const string MessageType_mInp00150 = "{0}ため {1}は入力できません。";
        public const string MessageType_mInp00080 = "{0}は {1}以内を入力してください。";
        public const string MessageType_mUnq00010 = "{0}が重複しています。";
        public const string MessageType_mInp00040 = "{0}は {1}で入力してください。";
        public const string MessageType_mEnt01041 = "{0}ため、登録できません。";
        public const string MessageType_mInp00010 = "{0}を入力してください。";
        public const string MessageType_mEnt00020 = "{0}が既に登録されています。" + "\r\n" + "登録しますか？";
        public const string MessageType_mInp00140 = "{0}に {1}は入力できません。";
        public const string MessageType_mChk00040 = "同一期間内に複数保険が登録されています。" + "\r\n" + "有効期限を確認してください。";
        public const string MessageType_mDel01060 = "{0}ため、{1}は削除できません。";
        public const string MessageType_mInp00110 = "{0}は {1}以降を入力してください。";
        public const string MessageType_mInp00050 = "{0}は {1} ～ {2}の範囲で入力してください。";
        public const string MessageType_mInp00060 = "{0}は {1}以上を入力してください。";
        public const string MessageType_mEnt01020 = "既に登録されているため、{0}は登録できません。";
        public const string MessageType_mDo00012 = "{0}" + "\r\n" + "{1}を実行しますか？";
        public const string MessageType_mDo00010 = "{0}を実行しますか？";
        public const string MessageType_mDo00050 = "{0}ため、この処理には時間がかかる場合があります。" + "\r\n" + "実行しますか？";
        public const string MessageType_mEnt01030 = "問題が発生したため、{0}は登録できません。";
        public const string MessageType_mEnt02020 = "{0}を登録しました。";
        public const string MessageType_mUpd01030 = "問題が発生したため、{0}を更新できません。";
        public const string MessageType_Age = "{0}ため、登録できません。\r\n{1}";
        public const string MessageType_Expired = "{0}保険の有効期限切れ" + "\r\n" + "有効期限切れの{1}が選択されています" +"\r\n" + "{2}" + "\r\n" + "このまま受付しますか？";
        public const string MessageType_dupHoken = "有効期限の確認" + "\r\n" + "有効な保険が複数登録されています" + "\r\n" + "有効期限を確認してください。";
        public const string MessageType_InvalidLength = "100文字以内で入力してください。";

        //Title
        public const string Title_CheckConfirmDate = "{0}の確認";
        public const string Title_CheckExpired = "{0}の有効期限切れ";
        public const string Title_CheckAge = "保険証の確認";
        public const string Title_Check75 = "75歳到達月の確認";
        public const string Title_CheckHokenMst = "{0}の種類の確認";
        public const string Title_CheckAgeUseIns = "{0}の対象年齢の確認";
        public const string Title_IsValidPeriod = "有効期限の確認";
        public const string Title_ElderHoken = "保険の確認";
        public const string Title_DuplicateKohi = "公費情報の重複";


        //MessageTitle
        public const string MessageTitle_CheckConfirmDate = "{0}が未確認です";
        public const string MessageTitle_CheckExpired = "有効期限切れの{0}が選択されています";
        public const string MessageTitle_CheckAge = "{0}歳となりました";
        public const string MessageTitle_Check75 = "75歳到達月の自己負担限度額の特例対象です";
        public const string MessageTitle_CheckHokenMst = "有効期限外の{0}が選択されています。";
        public const string MessageTitle_CheckAgeUseIns = "適用年齢外の{0}が選択されています。";
        public const string MessageTitle_IsValidPeriod = "有効な保険が複数登録されています";
        public const string MessageTitle_ElderHoken75 = "75歳以上ですが、後期高齢者保険が選択されていません";
        public const string MessageTitle_ElderHoken65 = "65歳未満ですが、後期高齢者保険が選択されています";
        public const string MessageTitle_DeletedHoken = "対象の保険が見つかりません。";
        public const string MessageTitle_DuplicateHokenInf = "{0}が既に登録されています。";

        //MessageContent
        public const string MessageContent_CheckConfirmDate = "{0}を確認してください。\r\nこのまま受付しますか？";
        public const string MessageContent_CheckExpired = "保険証を確認してください。\r\nこのまま受付しますか？";
        public const string MessageContent_CheckExpiredKohi = "受給者証等を確認してください。\r\nこのまま受付しますか？";
        public const string MessageContent_CheckAge = "{0}を確認してください。\r\nこのまま受付しますか？";
        public const string MessageContent_Check75 = "高額療養費の自己負担限度額の特例を適用するには、対象年月を入力してください。\r\nこのまま受付しますか？";
        public const string MessageContent_CheckHokenMst = "この{0}は使用できません。\r\n有効な{0}の種類を選択してください。\r\nこのまま受付しますか？";
        public const string MessageContent_CheckAgeUseIns = "この公費は{0}歳から利用できます。\r\n{1}が有効か確認してください。\r\nこのまま受付しますか？";
        public const string MessageContent_IsValidPeriod = "有効期限を確認してください。\r\nこのまま受付しますか？";
        public const string MessageContent_Confirm = "このまま受付しますか？";
        public const string MessageContent_DuplicateHokenInf = "登録しますか？";

        public const string MessageType_DuplicatePtNum = "入力された患者番号は既に利用されています。";
        public const string MessageType_NameValid = "姓と名の間にスペースを入力してください。";

        //MessageHoken
        public const string Message_InvalidStartEndDate = "保険有効終了日は 保険有効開始日以降を入力してください。";
        public const string Message_HokensyaNoIsNull = "保険者番号を入力してください。";
        public const string Message_HokensyaNo = "保険番号を入力してください。";
        public const string Message_HokensyaNoEqualZero = "保険者番号を入力してください。";
        public const string Message_HokenNoEqualZero = "保険番号が正しくありません。";

        public const string Tokki1 = "tokki1";
        public const string Tokki2 = "tokki2";
        public const string Tokki3 = "tokki3";
        public const string Tokki4 = "tokki4";
        public const string Tokki5 = "tokki5";


    }

    public static class TypeMessage
    {
        public const int TypeMessageCustom = 1;
        public const int TypeMessageError = 2;
        public const int TypeMessageWarning = 3;
        public const int TypeMessageConfirmation = 4;
        public const int TypeMessageInformation = 5;
        public const int TypeMessageInformationEx = 6;
        public const int TypeMessageSuccess = 7;
        public const int TypeMessageInline = 8;

    }
}
