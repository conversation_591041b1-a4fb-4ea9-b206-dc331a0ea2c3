﻿using Domain.Models.EventProcessor;
using Domain.Models.Renkei;
using Helper.Common;
using Helper.Constants;

namespace Domain.Common
{
    public static class EventProcessorUtil
    {
        public static List<(string, string)> GetReplaceTimeParamString()
        {
            return new List<(string, string)> {
                ("sysdate", "yyyy/MM/dd HH:mm:ss"),
                ("sysdate_2", "yyyyMMddHHmmss"),
                ("sysdate_3", "yyyyMMddHHmm"),
                ("sysdate_4", "yyyyMMdd-HHmmssfff"),
                ("sysdate_hh", "HH"),
                ("sysdate_nn", "mm"),
                ("sysdate_ss", "ss"),
                ("sysdate_zzz", "fff")
                    };
        }
        public static List<string> GetSecondsParamString()
        {
            return new List<string> {
                ("sysdate"),
                ("sysdate_2"),
                ("sysdate_4"),
                ("sysdate_ss")
                    };
        }

        /// <summary>
        /// パラメータ置換処理
        /// </summary>
        /// <param name="param">置換したいパラメータ</param>
        /// <param name="renkeiModel">連携マスタ</param>
        /// <param name="common">共通データ</param>
        /// <param name="excludeTime">時間に関するパラメータの置換を行わない場合、true</param>
        /// <returns></returns>
        public static string ReplaceParam(string param, RenkeiModel renkeiModel, CommonDataModel common, bool excludeTime = false)
        {
            /// <summary>
            /// 時間系パラメータ置換用
            /// </summary>
            List<(string, string)> ReplaceTimeParamString = GetReplaceTimeParamString();

            #region local method
            // param置換処理
            void Replace(string oldValue, string newValue)
            {
                param = param.Replace($"\"{oldValue}\"", newValue);
            }

            // 指定のbaseStrで始まるパラメータがparamに存在する場合、その値を返す
            string GetFieldName(string baseStr)
            {
                int posStart = 0;
                int posEnd = 0;
                string repStr = "";
                posStart = param.IndexOf($"\"{baseStr}");
                posEnd = param.IndexOf("\"", posStart + 1);
                if (posStart >= 0 && posEnd >= 0)
                {
                    repStr = param.Substring(posStart, posEnd - posStart + 1);
                    repStr = repStr.Replace("\"", "");
                }

                return repStr;
            }

            // パラメータ名に長さ等を含むパラメータを取得し、パラメータ名とそこに含まれる数値を返す
            (string repStr, int len) GetLengthFieldData(string baseStr)
            {
                string repStr = GetFieldName(baseStr);
                int len = 0;
                if (string.IsNullOrEmpty(repStr) == false)
                {
                    len = CIUtil.StrToIntDef(repStr.Substring($"{baseStr}".Length, repStr.Length - $"{baseStr}".Length), 0);
                }

                return (repStr, len);
            }

            // パラメータ名に長さ等を含むパラメータを置換する
            void ReplaceLengthField(string baseStr, string value)
            {
                string repStr = "";
                int len = 0;

                do
                {
                    (repStr, len) = GetLengthFieldData(baseStr);
                    if (repStr != "")
                    {
                        if (len > 0)
                        {
                            Replace(repStr, CIUtil.Copy(value, 1, len));
                        }
                        else
                        {
                            Replace(repStr, "");
                        }
                    }

                }
                while (repStr != "");
            }

            // RAIIN_GRP_INF関連置換処理
            void ReplaceGrpField()
            {
                string baseStr = "bunrui_";
                string repStr = "";
                int grpId = 0;

                do
                {
                    (repStr, grpId) = GetLengthFieldData(baseStr);
                    if (repStr != "")
                    {
                        string value = "";

                        if (grpId > 0)
                        {
                            if (common.PtGrpInfs != null)
                            {
                                List<PtGrpInfModel> filterdPtGrpInfs = common.PtGrpInfs.FindAll(p => p.GroupId == grpId);
                                if (filterdPtGrpInfs.Any())
                                {
                                    value = filterdPtGrpInfs.First().GroupCode;
                                }
                            }
                        }

                        Replace(repStr, value);
                    }
                }
                while (repStr != "");
            }

            // RAIIN_KBN_INF関連置換処理
            void ReplaceRaiinKbnField()
            {
                string baseStr = "jiskbn_";
                string repStr = "";
                int raiinKbnId = 0;

                do
                {
                    (repStr, raiinKbnId) = GetLengthFieldData(baseStr);
                    if (repStr != "")
                    {
                        string value = "";

                        if (raiinKbnId > 0)
                        {
                            if (common.RaiinKbnInfs != null)
                            {
                                List<RaiinKbnInfModel> filterdRaiinKbnInfs = common.RaiinKbnInfs.FindAll(p => p.GrpId == raiinKbnId);
                                if (filterdRaiinKbnInfs.Any())
                                {
                                    value = $"{filterdRaiinKbnInfs.First().KbnCd}";
                                }
                            }
                        }
                        Replace(repStr, value);
                    }
                }
                while (repStr != "");
            }

            void ReplaceRaiinKbnNameField()
            {
                string baseStr = "rcvcategory_";
                string repStr = "";
                int raiinKbnId = 0;

                do
                {
                    (repStr, raiinKbnId) = GetLengthFieldData(baseStr);
                    if (repStr != "")
                    {
                        string value = "";
                        if (raiinKbnId > 0)
                        {
                            List<RaiinKbnInfModel> filterdRaiinKbnInfs = common.RaiinKbnInfs.FindAll(p => p.GrpId == raiinKbnId);
                            if (filterdRaiinKbnInfs.Any())
                            {
                                value = $"{filterdRaiinKbnInfs.First().KbnName}";
                            }
                        }
                        Replace(repStr, value);
                    }
                }
                while (repStr != "");
            }
            #endregion

            // 患者番号
            var ptNumStr = common.PtNum.ToString();
            var ptNumStr_z = ptNumStr.Length > 9 ? ptNumStr.Substring(ptNumStr.Length - 9) : ptNumStr;
            var ptNumStr_z10 = ptNumStr.Length > 10 ? ptNumStr.Substring(ptNumStr.Length - 10) : ptNumStr;
            Replace("patientid", $"{common.PtNum}");
            Replace("patientid_z", $"{ptNumStr_z.PadLeft(9, '0')}");
            Replace("patientid_z10", $"{ptNumStr_z10.PadLeft(10, '0')}");
            if (renkeiModel.PtNumLength > 0)
            {
                Replace("patientid_zn", ptNumStr.PadLeft(renkeiModel.PtNumLength, '0'));
            }
            else
            {
                Replace("patientid_zn", $"{common.PtNum}");
            }

            // 患者ID
            Replace("pt_id", $"{common.PtId}");

            // 患者氏名

            Replace("kananame", common.KanaName);
            Replace("kanjiname", common.Name);


            ReplaceLengthField("kananame_l", common.KanaName);
            ReplaceLengthField("kanjiname_l", common.Name);

            Replace("kananame_s", common.KanaNameS);
            Replace("kananame_m", common.KanaNameM);
            Replace("kanjiname_s", common.NameS);
            Replace("kanjiname_m", common.NameM);

            // 性別
            Replace("sex", common.SexStr("M", "F"));
            Replace("sex_2", $"{common.Sex}");
            Replace("sex_3", common.SexStr("男", "女"));

            string customSexFieldName = GetFieldName("sex_m");
            if (string.IsNullOrEmpty(customSexFieldName) == false && customSexFieldName.Length >= 9)
            {
                int strPos = customSexFieldName.IndexOf("_f");
                if (strPos >= 0)
                {
                    string customMan = customSexFieldName.Substring(5, strPos - 5);

                    strPos += 2;
                    string customFemale = customSexFieldName.Substring(strPos, customSexFieldName.Length - strPos);
                    Replace(customSexFieldName, common.SexStr(customMan, customFemale));
                }
            }

            // 生年月日
            Replace("birthday", $"{common.Birthday}");
            Replace("birthday_2", $"{CIUtil.SDateToShowSDate(common.Birthday)}");
            Replace("birthday_3", $"{CIUtil.SDateToShowSDate(common.Birthday).Replace('/', '-')}");
            Replace("birthday_4", $"{CIUtil.SDateToShowWDate2(common.Birthday)}");

            // 年齢
            Replace("age", $"{common.Age}");
            Replace("sindate_age", $"{common.SinDateAge}");

            // 郵便番号
            Replace("zipcode", common.HomePostHyphen);
            ReplaceLengthField("zipcode_l", common.HomePostHyphen);
            Replace("zipno", common.HomePost);
            ReplaceLengthField("zipno_l", common.HomePost);

            // 住所
            Replace("address", common.HomeAddress);

            ReplaceLengthField("address_l", common.HomeAddress);

            Replace("address_1", common.HomeAddress1);
            Replace("address_2", common.HomeAddress2);

            // 電話番号
            Replace("tel", common.Tel);
            Replace("tel_1", common.Tel1);
            Replace("tel_2", common.Tel2);
            Replace("tel_3", common.RenrakuTel);

            // 世帯主
            Replace("setainushi", common.Setanusi);

            // 勤務先名
            Replace("kinmu", common.OfficeName);
            Replace("kinmuaddress", common.OfficeAddress);

            // 職業
            Replace("job", common.Job);

            // 分類
            //if (common.PtGrpInfs != null)
            //{
            ReplaceGrpField();
            //}

            // メモ
            //if (common.PtMemo != null)
            //{
            for (int i = 1; i <= 5; i++)
            {
                Replace($"memo_{i}", common.GetMemoLine(i - 1));
            }
            //}

            // 保険
            Replace("hokensyano", common.HokensyaNo);
            Replace("hokensyakigo", common.HokensyaKigo);
            Replace("hokensyabango", common.HokensyaBango);
            Replace("hokensyaedaban", common.HokensyaEdaNo);
            Replace("hokenfrom", $"{common.HokenStartDate}");
            Replace("hokento", $"{common.HokenEndDate}");
            Replace("zokugara", $"{common.HonkeKbn}");
            Replace("hokenrate1", $"{common.HokenRate}");
            Replace("hokenrate2", $"{common.FutanRate ?? 0}");
            Replace("hokensyaname", common.HokensyaName);
            Replace("hokensyaaddress", common.HokensyaAddress);
            Replace("hokensyatel", common.HokensyaTel);

            for (int i = 1; i <= 4; i++)
            {
                Replace($"ko{i}futanno", common.KohiFutansyaNo(i));
                Replace($"ko{i}jyukyuno", common.KohiJyukyusyaNo(i));
                Replace($"ko{i}from", $"{common.KohiStartDate(i)}");
                Replace($"ko{i}to", $"{common.KohiEndDate(i)}");
            }

            // ユーザー情報
            Replace("loginid", common.LoginId);
            Replace("userid", $"{common.UserId}");

            // 来院情報

            Replace("rsvno", $"{common.RaiinNo}");

            Replace("tantoid", $"{common.TantoId}");
            Replace("rcvday", $"{common.SinDate}");
            Replace("rcvday_2", CIUtil.SDateToShowSDate(common.SinDate));
            Replace("rcvtime", $"{common.YoyakuTime}");
            Replace("rcvtime_2", CIUtil.TimeToShowTime(CIUtil.StrToIntDef(common.YoyakuTime, 0)));
            Replace("rcvdate", CIUtil.SDateToShowSDate(common.SinDate) + " " + CIUtil.TimeToShowTime(CIUtil.StrToIntDef(common.YoyakuTime, 0)));
            Replace("uketukeno", $"{common.UketukeNo}");
            Replace("uketukesbt", $"{common.UketukeSbt}");

            int iStatus = 0;

            switch (common.Status)
            {

                case 0:
                    iStatus = 0;
                    break;
                case 1:
                    iStatus = 1;
                    break;
                case 3:
                    iStatus = 3;
                    break;
                case 5:
                    iStatus = 5;
                    break;
                case 7:
                    iStatus = 4;
                    break;
                case 9:
                    iStatus = 6;
                    break;
            }
            Replace("raiin_status", $"{iStatus}");

            // 最終来院日
            Replace("lastsinday", $"{common.LastDate}");
            Replace("lastsinday_2", CIUtil.SDateToShowSDate(common.LastDate));

            // 診療科
            Replace("skanm", common.KaName);
            Replace("skarnm", common.KaSname);

            // 予約枠
            Replace("rcvframe", common.RsvFrameName);

            // 来院区分
            ReplaceRaiinKbnField();
            ReplaceRaiinKbnNameField();

            // 来院コメント
            Replace("rcvcmt", common.RaiinCmt);

            // 連携情報
            if (common.OtherPtId >= 0)
            {
                Replace("ren_pid", $"{common.OtherPtId}");
            }
            else
            {
                Replace("ren_pid", "");
            }
            if (common.OtherSeqNo >= 0)
            {
                Replace("ren_seqno", $"{common.OtherSeqNo}");
            }
            else
            {
                Replace("ren_seqno", "");
            }
            if (common.OtherSeqNo2 >= 0)
            {
                Replace("ren_seqno2", $"{common.OtherSeqNo2}");
            }
            else
            {
                Replace("ren_seqno2", "");
            }

            Replace("hokenno", $"{common.HokenId}");
            Replace("seikyudate", $"{common.SeikyuDate}");
            int ptFutan = common.SumPtFutan;
            //Replace("seikyugaku", $"{ptFutan}");
            Replace("seikyugaku", $"{common.Seikyu}");
            int jihiFutan = common.SumJihiFutanTotal;
            //Replace("seikyujihi", $"{jihiFutan}");
            Replace("seikyujihi", $"{common.SeikyuJihi}");

            int syahoKokuho = common.HokenKbn;
            if (!new int[] { 1, 2 }.Contains(syahoKokuho))
            {
                syahoKokuho = 3;
            }
            Replace("syahokokuho", $"{syahoKokuho}");

            syahoKokuho = common.HokenKbn;
            if (syahoKokuho == 1)
            {
                syahoKokuho = 2;
            }
            else if (syahoKokuho == 2)
            {
                syahoKokuho = 1;
            }
            else
            {
                syahoKokuho = 3;
            }
            Replace("syahokokuho_2", $"{syahoKokuho}");
            Replace("seikyumisyu", $"{common.Misyu ?? 0}");
            //Replace("seikyugokei", $"{ptFutan + jihiFutan + (common.Misyu ?? 0)}");
            Replace("seikyugokei", $"{common.SeikyuGokei}");
            Replace("nyukingaku", $"{common.Nyukin ?? 0}");
            Replace("nyukindate", $"{common.NyukinDate}");
            if (common.NyukinSortNo > 0)
            {
                Replace("nyukinrenban", $"{common.NyukinSortNo}");
            }
            else
            {
                Replace("nyukinrenban", "");
            }

            // システム日付
            DateTime dt = DateTime.Now;

            if (excludeTime == false)
            {
                foreach ((string oldVal, string newVal) in ReplaceTimeParamString)
                {
                    Replace(oldVal, dt.ToString(newVal));
                }
            }
            Replace("sysdate_yyyy", dt.ToString("yyyy"));
            Replace("sysdate_yy", dt.ToString("yyyy").Substring(2, 2));
            Replace("sysdate_mm", dt.ToString("MM"));
            Replace("sysdate_dd", dt.ToString("dd"));

            // カレンダー日付
            Replace("calendate", $"{common.SinDate}");
            // 改行コード
            Replace("crlf", "\r\n");

            // 文字コード
            Replace("charcd", $"{renkeiModel.CharCd}");
            Replace("charcdtype", $"{(renkeiModel.CharCd == 1 ? "UTF-8" : "Shift-JIS")}");


            return param;
        }

        /// <summary>
        /// 共通カスタムファイル出力
        /// </summary>
        /// <param name="renkeiModel"></param>
        /// <param name="common"></param>
        public static OuputCommonFileModel OutputCommonFile(RenkeiModel renkeiModel, CommonDataModel common)
        {
            bool isSaveData = true;
            if (new int[] { 90, 91 }.Contains(renkeiModel.RenkeiId) &&
                (common.Sex == 0 || common.Birthday == 0) &&
                renkeiModel.IsNotEmpty)
            {
                // 属性ファイル連携, 受付ファイル連携で、性別 or 生年月日が未指定の場合、ファイルを作成しない
                isSaveData = false;
                return new OuputCommonFileModel(isSaveData, new OuputCommonFile(), renkeiModel);
            }

            /// <summary>
            /// 時間系パラメータ置換用
            /// </summary>
            List<(string, string)> ReplaceTimeParamString = GetReplaceTimeParamString();
            // 秒を含むパラメータ
            List<string> SecondsParamString = GetSecondsParamString();

            #region local method
            // 時間系パラメータ置換処理
            string Replace(string repStr, string oldValue, string newValue)
            {
                return repStr.Replace($"\"{oldValue}\"", newValue);
            }
            string ReplaceTimeParam(string replaceStr)
            {
                DateTime dt = DateTime.Now;

                string repStr = replaceStr;

                foreach ((string oldVal, string newVal) in ReplaceTimeParamString)
                {
                    repStr = Replace(repStr, oldVal, dt.ToString(newVal));
                }

                return repStr;
            }
            #endregion

            // パラメータ変換
            string param = EventProcessorUtil.ReplaceParam(renkeiModel.Param, renkeiModel, common);
            string path = EventProcessorUtil.ReplaceParam(renkeiModel.Path, renkeiModel, common, true);

            bool includeTimeParam = ReplaceTimeParamString.Select(p => p.Item1).Any(c => path.Contains(c));
            bool includeSecondsParam = SecondsParamString.Select(p => p).Any(c => path.Contains(c));

            string outputPath = ReplaceTimeParam(path);
            string tmpPath = outputPath + ".temp";

            if (renkeiModel.Append)
            {
                // 追記の場合は、一時ファイルを作成しない
                tmpPath = outputPath;
            }
            
            return new OuputCommonFileModel(isSaveData, new OuputCommonFile(path, param, includeTimeParam, includeSecondsParam, outputPath, tmpPath), renkeiModel);
        }
    }
}
