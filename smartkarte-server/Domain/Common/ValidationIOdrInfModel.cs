﻿using Domain.Models.MstItem;
using Domain.Types;
using Helper.Common;
using Helper.Constants;
using Helper.Extension;
using System.Collections.Generic;
using static Helper.Constants.OrderInfConst;

namespace Domain.Common
{
    public static class ValidationIOdrInfModel<TOdrInf, TOdrInfDetailModel> where TOdrInf : class, IOdrInfModel<TOdrInfDetailModel> where TOdrInfDetailModel : class, IOdrInfDetailModel
    {
        private const string odrValidateCode = "-1";

        public static List<KeyValuePair<string, OrdInfValidationStatus>> Validation(TOdrInf odrInf, int flag, int sinDate = 0, int refillSetting = 999, List<RecedenCmtSelectModel>? recedenCmtSelectModels = null)
        {
            List<KeyValuePair<string, OrdInfValidationStatus>> validates = new();

            var validateCommons = ValidateCommon(odrInf, flag);
            if (validateCommons.Count > 0)
            {
                foreach (var validateCommon in validateCommons)
                {
                    validates.Add(new(odrValidateCode, validateCommon));
                }
            }

            #region Validate business
            //Check has not drug 
            if (odrInf.IsDrug)
            {
                // 用法
                var checkDrugOfDetail = odrInf.OrdInfDetails.Any(o => o.IsDrug);
                if (!checkDrugOfDetail)
                {
                    validates.Add(new(odrValidateCode, OrdInfValidationStatus.InvalidHasUsageButNotInjectionOrDrug));
                }
            }

            if (odrInf.SpecialToyaku)
            {
                if (odrInf.OrdInfDetails.Any(od => od.ItemCd == ItemCdConst.TouyakuChozaiGai) && odrInf.OrdInfDetails.Any(od => od.ItemCd == ItemCdConst.TouyakuChozaiNaiTon) || odrInf.OrdInfDetails.Any(od => od.MasterSbt == "S" && od.SinKouiKbn == 20 && od.DrugKbn == 0 && od.ItemCd != ItemCdConst.Con_Refill && od.ItemCd != ItemCdConst.Con_TouyakuOrSiBunkatu))
                {
                    validates.Add(new(odrValidateCode, OrdInfValidationStatus.InvalidException));
                }
            }

            //Check Refill
            if (odrInf.OdrKouiKbn == 20 || (odrInf.OrdInfDetails.Any(od => od.ItemCd == ItemCdConst.Con_Refill || od.ItemCd == ItemCdConst.Con_TouyakuOrSiBunkatu) && odrInf.OrdInfDetails.Any(od => od.ItemCd == ItemCdConst.TouyakuChozaiNaiTon || od.ItemCd == ItemCdConst.TouyakuChozaiGai)))
            {
                // 用法
                var checkRefill = odrInf.OrdInfDetails.Any(o => o.ItemCd == ItemCdConst.Con_Refill);
                var checkOther = odrInf.OrdInfDetails.Any(o => o.SinKouiKbn == 20 && o.MasterSbt == "S" && o.DrugKbn == 0 && o.ItemCd !=  ItemCdConst.TouyakuChozaiGai && o.ItemCd != ItemCdConst.TouyakuChozaiNaiTon && o.ItemCd != ItemCdConst.Con_Refill && o.ItemCd != ItemCdConst.Con_TouyakuOrSiBunkatu);
                var checkDrugOfDetail = odrInf.OrdInfDetails.Any(o => o.IsDrug);
                var checkUsageOfDetail = odrInf.OrdInfDetails.Any(o => o.IsDrugUsage);
                var checkBunkatu = odrInf.OrdInfDetails.Any(o => o.ItemCd == ItemCdConst.Con_TouyakuOrSiBunkatu);
                var checkZan = odrInf.OrdInfDetails.Any(o => o.ItemCd.Contains("@ZAN"));
                var checkRelateOther = odrInf.OrdInfDetails.Any(o => o.SinKouiKbn != 99 && o.ItemCd != ItemCdConst.Con_Refill && !(o.SinKouiKbn == 20 && o.MasterSbt == "S" && o.DrugKbn == 0));
                if (checkRefill && !checkOther && !checkZan && !checkDrugOfDetail)
                {
                    validates.Add(new(odrValidateCode, OrdInfValidationStatus.InvalidHasUsageButNotInjectionOrDrug));
                }
                if (checkOther && checkRelateOther && !checkBunkatu && odrInf.OdrKouiKbn == 20)
                {
                    validates.Add(new(odrValidateCode, OrdInfValidationStatus.InvalidException));
                }
                //if (checkOther && checkUsageOfDetail && !checkRefill && !checkBunkatu)
                //{
                //    return new(odrValidateCode, OrdInfValidationStatus.InvalidHasUsage);
                //}
                if (!checkOther && checkDrugOfDetail && !checkUsageOfDetail && !checkRefill && !checkBunkatu && odrInf.OdrKouiKbn == 20)
                {
                    validates.Add(new(odrValidateCode, OrdInfValidationStatus.InvalidHasDrugButNotUsage));
                }
                if (!checkOther && !checkDrugOfDetail && checkUsageOfDetail && !checkRefill && !checkBunkatu && odrInf.OdrKouiKbn == 20)
                {
                    validates.Add(new(odrValidateCode, OrdInfValidationStatus.InvalidHasUsageButNotDrug));
                }

                if (!checkUsageOfDetail && checkBunkatu && !checkDrugOfDetail)
                {
                    validates.Add(new(odrValidateCode, OrdInfValidationStatus.InvalidHasUsageButNotDrug));
                }
            }

            //Check has not Injection 
            if (odrInf.IsInjection)
            {
                // 用法
                var checkDrugOfDetail = odrInf.OrdInfDetails.Any(o => o.IsInjection || o.IsDrug);
                if (!checkDrugOfDetail)
                {
                    validates.Add(new(odrValidateCode, OrdInfValidationStatus.InvalidHasUsageButNotInjectionOrDrug));
                }
            }

            if (odrInf.OrdInfDetails.Any(o => o.IsSpecialItem))
            {
                var validateSpecialItem = ValidateSpecialItem(odrInf);
                if (validateSpecialItem.Count > 0)
                {
                    validates.AddRange(validateSpecialItem);
                }
            }

            var validateUsageOfSelfInjection = ValidateUsageOfSelfInjection(odrInf);
            if (validateUsageOfSelfInjection.Value != OrdInfValidationStatus.Valid)
            {
                validates.Add(validateUsageOfSelfInjection);
            }

            var validateHasUsageButNotHasDrugOrInjection = ValidateHasUsageButNotHasDrugOrInjection(odrInf);
            if (validateHasUsageButNotHasDrugOrInjection.Value != OrdInfValidationStatus.Valid)
            {
                validates.Add(validateHasUsageButNotHasDrugOrInjection);
            }

            var validateHasNotUsageOfDrug = ValidateHasNotUsageOfDrug(odrInf, flag, sinDate, refillSetting);
            if (validateHasNotUsageOfDrug.Count > 0)
            {
                validates.AddRange(validateHasNotUsageOfDrug);
            }

            var validateHasNotUsageOfInjection = ValidateHasNotUsageOfInjection(odrInf, flag, sinDate, refillSetting);
            if (validateHasNotUsageOfInjection.Count > 0)
            {
                validates.AddRange(validateHasNotUsageOfInjection);
            }

            if (odrInf.IsDrug || odrInf.IsInjection)
            {
                // 用法
                var usageCount = odrInf.OrdInfDetails?.Count(o => o.IsStandardUsage);
                if (usageCount > 1)
                {
                    validates.Add(new(odrValidateCode, OrdInfValidationStatus.InvalidStandardUsageOfDrugOrInjection));
                }

                // 補助用法
                //var usage2Count = odrInf.OrdInfDetails?.Count(item => item.IsSuppUsage);
                //if (usage2Count > 1)
                //{
                //    validates.Add(new(odrValidateCode, OrdInfValidationStatus.InvalidSuppUsageOfDrugOrInjection));
                //}
            }

            var validateBunkatu = ValidateBunkatu(odrInf);
            if (validateBunkatu.Count > 0)
            {
                validates.AddRange(validateBunkatu);
            }

            if (odrInf.OrdInfDetails?.Count > 0)
            {
                var count = 0;
                foreach (var ordInfDetail in odrInf.OrdInfDetails)
                {
                    var statuses = ValidationDetail(ordInfDetail, flag, sinDate, refillSetting);
                    if (statuses.Count > 0)
                    {
                        foreach (var status in statuses)
                        {
                            if (status == OrdInfValidationStatus.InvalidSuryoOfReffill)
                                validates.Add(new(count.ToString() + "_" + refillSetting, status));
                            validates.Add(new(count.ToString(), status));
                        }
                    }
                    count++;
                }
            }

            if (flag == 1)
            {
                var validateInputItemBussiness = ValidateInputItemBussiness(odrInf);
                if (validateInputItemBussiness.Count > 0)
                {
                    validates.AddRange(validateInputItemBussiness);
                }
            }
            #endregion

            //validates.Add(new(odrValidateCode, OrdInfValidationStatus.Valid));

            return validates;
        }

        private static List<OrdInfValidationStatus> ValidationDetail(TOdrInfDetailModel odrInfDetail, int flag, int sinDate, int refillSetting)
        {
            List<OrdInfValidationStatus> validates = new();
            #region Validate common
            var validateCommonDetails = ValidateCommonDetail(odrInfDetail);
            if (validateCommonDetails.Count > 0)
            {
                validates.AddRange(validateCommonDetails);
            }

            if (flag == 0)
            {
                var validateCommonDetailInputItems = ValidateCommonDetailInputItem(odrInfDetail);
                if (validateCommonDetailInputItems.Count > 0)
                {
                    validates.AddRange(validateCommonDetailInputItems);
                }
            }
            #endregion

            #region Validate business

            if ((string.IsNullOrEmpty(odrInfDetail.UnitName.Trim()) && ((odrInfDetail.Suryo > 0 && odrInfDetail.ItemCd != ItemCdConst.Con_TouyakuOrSiBunkatu) || (odrInfDetail.Suryo != 0 && odrInfDetail.ItemCd.StartsWith("J")))))
            {
                validates.Add(OrdInfValidationStatus.InvalidSuryo);
            }

            if (!string.IsNullOrEmpty(odrInfDetail.UnitName.Trim()) && odrInfDetail.Suryo == 0)
            {
                if (flag != 1 && flag != 0)
                    validates.Add(OrdInfValidationStatus.InvalidSuryo);
                else
                {
                    validates.Add(OrdInfValidationStatus.NoFillSuryo);
                }
            }

            if (!KohatuKbns.ContainsValue(odrInfDetail.KohatuKbn))
            {
                validates.Add(OrdInfValidationStatus.InvalidKohatuKbn);
            }

            if (!DrugKbns.ContainsValue(odrInfDetail.DrugKbn))
            {
                validates.Add(OrdInfValidationStatus.InvalidDrugKbn);
            }

            if (!string.IsNullOrWhiteSpace(odrInfDetail.DisplayedUnit))
            {
                if (string.IsNullOrEmpty(odrInfDetail.DisplayedQuantity))
                {
                    validates.Add(OrdInfValidationStatus.InvalidQuantityUnit);
                }
                else if (odrInfDetail.Suryo > 0 && odrInfDetail.Price > 0 && odrInfDetail.Suryo * odrInfDetail.Price > 999999999)
                {
                    validates.Add(OrdInfValidationStatus.InvalidPrice);
                }
            }

            if (!string.IsNullOrWhiteSpace(odrInfDetail.DisplayedUnit) && odrInfDetail.YohoKbn == 1 && odrInfDetail.Suryo > 999)
            {
                validates.Add(OrdInfValidationStatus.InvalidSuryoAndYohoKbnWhenDisplayedUnitNotNull);
            }

            if (odrInfDetail.ItemCd == ItemCdConst.Con_TouyakuOrSiBunkatu && odrInfDetail.Suryo == 0 && string.IsNullOrWhiteSpace(odrInfDetail.Bunkatu))
            {
                validates.Add(OrdInfValidationStatus.InvalidSuryoBunkatuWhenIsCon_TouyakuOrSiBunkatu);
            }

            if (odrInfDetail.ItemCd == ItemCdConst.Con_Refill && odrInfDetail.Suryo > refillSetting)
            {
                validates.Add(OrdInfValidationStatus.InvalidSuryoOfReffill);
            }

            var validateCmtDetails = ValidateCmtDetail(sinDate, odrInfDetail);
            if (validateCmtDetails.Count > 0)
            {
                validates.AddRange(validateCmtDetails);
            }
            #endregion

            return validates;
        }

        #region Seperate Validate OdrInf
        private static List<OrdInfValidationStatus> ValidateCommon(TOdrInf odrInf, int flag)
        {
            List<OrdInfValidationStatus> validates = new();

            if (odrInf.InoutKbn != 0 && odrInf.InoutKbn != 1)
            {
                validates.Add(OrdInfValidationStatus.InvalidInoutKbn);
            }
            if (odrInf.DaysCnt < 0)
            {
                validates.Add(OrdInfValidationStatus.InvalidDaysCnt);
            }
            if (!OdrKouiKbns.ContainsValue(odrInf.OdrKouiKbn))
            {
                validates.Add(OrdInfValidationStatus.InvalidOdrKouiKbn);
            }

            if (flag == 0)
            {
                if (odrInf.Id < 0)
                {
                    validates.Add(OrdInfValidationStatus.InvalidId);
                }
                if (odrInf.HpId <= 0)
                {
                    validates.Add(OrdInfValidationStatus.InvalidHpId);
                }
                if (odrInf.Id > 0 && odrInf.RpNo <= 0 || odrInf.Id == 0 && odrInf.RpNo != 0)
                {
                    validates.Add(OrdInfValidationStatus.InvalidRpNo);
                }
                if (odrInf.Id > 0 && odrInf.RpEdaNo <= 0 || odrInf.Id == 0 && odrInf.RpEdaNo != 0)
                {
                    validates.Add(OrdInfValidationStatus.InvalidRpEdaNo);
                }
                if (odrInf.RpName.Length > 240)
                {
                    validates.Add(OrdInfValidationStatus.InvalidRpName);
                }
                if (odrInf.SikyuKbn != 0 && odrInf.SikyuKbn != 1)
                {
                    validates.Add(OrdInfValidationStatus.InvalidSikyuKbn);
                }
                if (odrInf.SyohoSbt != 0 && odrInf.SyohoSbt != 1 && odrInf.SyohoSbt != 2)
                {
                    validates.Add(OrdInfValidationStatus.InvalidSyohoSbt);
                }
                if (odrInf.SanteiKbn != 0 && odrInf.SanteiKbn != 1 && odrInf.SanteiKbn != 2)
                {
                    validates.Add(OrdInfValidationStatus.InvalidSanteiKbn);
                }
                if (odrInf.TosekiKbn != 0 && odrInf.TosekiKbn != 1 && odrInf.TosekiKbn != 2)
                {
                    validates.Add(OrdInfValidationStatus.InvalidTosekiKbn);
                }
                if (odrInf.SortNo <= 0)
                {
                    validates.Add(OrdInfValidationStatus.InvalidSortNo);
                }
                if (odrInf.IsDeleted != 0 && odrInf.IsDeleted != 1 && odrInf.IsDeleted != 2)
                {
                    validates.Add(OrdInfValidationStatus.InvalidIsDeleted);
                }
            }

            return validates;
        }

        private static List<KeyValuePair<string, OrdInfValidationStatus>> ValidateSpecialItem(TOdrInf odrInf)
        {
            List<KeyValuePair<string, OrdInfValidationStatus>> validates = new();
            var countItem = odrInf.OrdInfDetails.FirstOrDefault(item => (item.IsDrug || item.IsInjection) && !item.IsSpecialItem);
            if (odrInf.OrdInfDetails.Any(x => x.IsSpecialItem && x.ItemCd == ItemCdConst.ZanGigi || x.ItemCd == ItemCdConst.ZanTeiKyo) && countItem != null && countItem.ItemCd == ItemCdConst.Con_Refill)
            {
                countItem = odrInf.OrdInfDetails.FirstOrDefault(item => (item.IsDrug || item.IsInjection) && !item.IsSpecialItem && item.ItemCd != ItemCdConst.Con_Refill);
            }
            // Check main usage of drug and injection
            var countUsage = odrInf.OrdInfDetails.FirstOrDefault(item => item.IsStandardUsage || item.IsInjectionUsage);

            // check supp usage of drug
            var countUsage2 = odrInf.OrdInfDetails.FirstOrDefault(item => item.IsSuppUsage);

            if (countItem != null)
            {
                var countIndex = odrInf.OrdInfDetails.FindIndex(od => od == countItem);

                validates.Add(new(countIndex.ToString(), OrdInfValidationStatus.InvalidSpecialItem));
            }
            else if (countUsage != null)
            {
                var countUsage1Index = odrInf.OrdInfDetails.FindIndex(od => od == countUsage);

                validates.Add(new(countUsage1Index.ToString(), OrdInfValidationStatus.InvalidSpecialStadardUsage));
            }
            else if (countUsage2 != null)
            {
                var countUsage2Index = odrInf.OrdInfDetails.FindIndex(od => od == countUsage2);

                validates.Add(new(countUsage2Index.ToString(), OrdInfValidationStatus.InvalidSpecialSuppUsage));
            }

            return validates;
        }

        private static List<KeyValuePair<string, OrdInfValidationStatus>> ValidateInputItemBussiness(TOdrInf odrInf)
        {
            //var checkGazoDensibaitaiHozon = odrInf.OrdInfDetails?.Any(item => item.ItemCd == ItemCdConst.GazoDensibaitaiHozon);
            //var checkOtherCategory = odrInf.OrdInfDetails?.Any(item => item.ItemCd != ItemCdConst.GazoDensibaitaiHozon);
            //if (checkGazoDensibaitaiHozon == true && checkOtherCategory != true)
            //{
            //    return new(odrValidateCode, OrdInfValidationStatus.InvalidGazoDensibaitaiHozon);
            //}
            List<KeyValuePair<string, OrdInfValidationStatus>> validates = new();

            var specialItems = odrInf.OrdInfDetails?.Where(item => item.IsSpecialItem && item.ItemCd != ItemCdConst.Con_TouyakuOrSiBunkatu);

            var countItems = odrInf.OrdInfDetails?.Where(item => (item.SinKouiKbn == 20 || item.SinKouiKbn == 30) && !item.IsSpecialItem);

            if (specialItems?.Any(x => x.ItemCd == ItemCdConst.ZanGigi || x.ItemCd == ItemCdConst.ZanTeiKyo) == true && countItems?.Any(x => x.ItemCd == ItemCdConst.Con_Refill) == true && countItems?.Any(x => x.ItemCd == ItemCdConst.Con_Refill) == true)
            {
                countItems = odrInf.OrdInfDetails?.Where(item => (item.SinKouiKbn == 20 || item.SinKouiKbn == 30) && !item.IsSpecialItem && item.ItemCd != ItemCdConst.Con_Refill);
            }

            var countUsage = odrInf.OrdInfDetails?.Count(item => item.SinKouiKbn == 28 || item.IsStandardUsage || item.IsInjectionUsage) ?? 0;

            if (countItems?.Count() > 0 && countUsage == 0 && (odrInf.IsDrug || odrInf.OdrKouiKbn == 20))
            {
                validates.Add(new(odrValidateCode, OrdInfValidationStatus.InvalidHasDrugButNotUsage));
            }

            if (countItems?.Count() > 0 && countUsage == 0 && (odrInf.IsInjection || odrInf.OdrKouiKbn == 28))
            {
                validates.Add(new(odrValidateCode, OrdInfValidationStatus.InvalidHasInjectionButNotUsage));
            }

            if (countUsage == 1 && countItems?.Count() == 0)
            {
                var tokuzaiCount = odrInf.OrdInfDetails?.Count(item => item.MasterSbt.AsString().ToUpper() == "T") ?? 0;

                if (tokuzaiCount == 0)
                {
                    validates.Add(new(odrValidateCode, OrdInfValidationStatus.InvalidTokuzai));
                }
            }

            var tokuzaiFirstCount = odrInf.OrdInfDetails?.Count(item => item.MasterSbt.AsString().ToUpper() == "T") ?? 0;

            if (tokuzaiFirstCount > 0)
            {
                if (odrInf.OdrKouiKbn == 0)
                {
                    validates.Add(new(odrValidateCode, OrdInfValidationStatus.InvalidTokuzaiKouiKbn));
                }

                if (countItems?.Count() == 0 && (odrInf.IsDrug || odrInf.OdrKouiKbn == 20 || odrInf.IsInjection))
                {
                    validates.Add(new(odrValidateCode, OrdInfValidationStatus.InvalidTokuzaiDrugOrInjection));
                }
            }

            return validates;
        }

        private static List<KeyValuePair<string, OrdInfValidationStatus>> ValidateBunkatu(TOdrInf odrInf)
        {
            List<KeyValuePair<string, OrdInfValidationStatus>> validates = new();

            if (odrInf.IsDrug)
            {
                int bunkatuItemCount = odrInf.OrdInfDetails?.Count(i => i.ItemCd == ItemCdConst.Con_TouyakuOrSiBunkatu) ?? 0;

                if (bunkatuItemCount > 1)
                {
                    validates.Add(new(odrValidateCode, OrdInfValidationStatus.InvalidBunkatu));
                }

                var bunkatuItem = odrInf.OrdInfDetails?.FirstOrDefault(i => i.ItemCd == ItemCdConst.Con_TouyakuOrSiBunkatu);
                if (bunkatuItem != null)
                {
                    if (string.IsNullOrEmpty(bunkatuItem?.Bunkatu))
                    {
                        var bunkatuIndex = odrInf.OrdInfDetails?.FindIndex(od => od == bunkatuItem) ?? 0;
                        validates.Add(new(bunkatuIndex.ToString(), OrdInfValidationStatus.InvalidBunkatuNoInput));
                    }

                    var usageItem = odrInf.OrdInfDetails?.FirstOrDefault(item => item.IsStandardUsage);

                    if (usageItem == null)
                    {
                        var usageIndex = odrInf.OrdInfDetails?.FindIndex(od => od == bunkatuItem) ?? 0;

                        validates.Add(new(usageIndex.ToString(), OrdInfValidationStatus.InvalidUsageWhenBuntakuNull));
                    }

                    var sumBukatu = odrInf.SumBunkatu(bunkatuItem?.Bunkatu ?? string.Empty);

                    if ((decimal)usageItem.Suryo != sumBukatu)
                    {
                        var bunkatuIndex = odrInf.OrdInfDetails?.FindIndex(od => od == bunkatuItem) ?? 0;

                        validates.Add(new(bunkatuIndex.ToString(), OrdInfValidationStatus.InvalidSumBunkatuDifferentSuryo));
                    }
                }
            }

            return validates;
        }

        private static KeyValuePair<string, OrdInfValidationStatus> ValidateUsageOfSelfInjection(TOdrInf odrInf)
        {
            if (odrInf.OdrKouiKbn == 28)
            {
                var seflInjection = odrInf.OrdInfDetails?.FirstOrDefault(d => d.ItemCd == ItemCdConst.ChusyaJikocyu);
                var usageCount = odrInf.OrdInfDetails?.Count(d => d.IsInjectionUsage);
                if (seflInjection == null && usageCount == 0)
                {
                    return new(odrValidateCode, OrdInfValidationStatus.InvalidHasNotBothInjectionAndUsageOf28);
                }
            }

            return new(odrValidateCode, OrdInfValidationStatus.Valid);
        }

        private static KeyValuePair<string, OrdInfValidationStatus> ValidateHasUsageButNotHasDrugOrInjection(TOdrInf odrInf)
        {
            if (odrInf.OrdInfDetails?.Count(d => d.IsDrugUsage) > 0 && odrInf.OrdInfDetails?.Count(d => d.IsDrug || d.SinKouiKbn == 20 && d.ItemCd.StartsWith("Z")) == 0)
            {
                return new(odrValidateCode, OrdInfValidationStatus.InvalidHasUsageButNotDrug);
            }

            if (odrInf.OrdInfDetails?.Count(d => d.IsInjectionUsage) > 0
              && odrInf.OrdInfDetails?.Count(d => d.IsInjection || d.IsDrug) == 0)
            {
                return new(odrValidateCode, OrdInfValidationStatus.InvalidHasUsageButNotInjectionOrDrug);
            }

            return new(odrValidateCode, OrdInfValidationStatus.Valid);
        }

        private static List<KeyValuePair<string, OrdInfValidationStatus>> ValidateHasNotUsageOfDrug(TOdrInf odrInf, int flag, int sinDate, int refillSetting)
        {
            List<KeyValuePair<string, OrdInfValidationStatus>> validates = new();
            if (odrInf.IsDrug)
            {
                var drugUsage = odrInf.OrdInfDetails?.LastOrDefault(d => d.IsDrugUsage);
                if (drugUsage != null)
                {
                    var drugAfterDrugUsage = odrInf.OrdInfDetails?.Where(d => d.RowNo > drugUsage.RowNo && d.IsDrug && !d.IsEmpty).ToList();
                    if (drugAfterDrugUsage?.Any() == true)
                    {
                        var count = 0;
                        foreach (var detail in drugAfterDrugUsage)
                        {
                            var validateResults = ValidationDetail(detail, flag, sinDate, refillSetting);
                            if (validateResults.Count > 0)
                            {
                                foreach (var validateResult in validateResults)
                                {
                                    validates.Add(new(count.ToString(), validateResult));
                                }
                            }
                            count++;
                        }
                    }
                }
                else
                {
                    validates.Add(new(odrValidateCode, OrdInfValidationStatus.InvalidHasDrugButNotUsage));
                }
            }

            return validates;
        }

        private static List<KeyValuePair<string, OrdInfValidationStatus>> ValidateHasNotUsageOfInjection(TOdrInf odrInf, int flag, int sinDate, int refillSetting)
        {
            List<KeyValuePair<string, OrdInfValidationStatus>> validates = new();
            if (odrInf.IsInjection)
            {
                var injectionUsage = odrInf.OrdInfDetails?.FirstOrDefault(d => d.IsInjectionUsage);
                if (injectionUsage != null)
                {
                    var injectionBeforeInjectionUsage = odrInf.OrdInfDetails?.Where(d => d.RowNo < injectionUsage.RowNo && (d.IsInjection || d.IsDrug) && !d.IsEmpty).ToList();
                    injectionBeforeInjectionUsage?.Reverse();
                    if (injectionBeforeInjectionUsage?.Any() == true)
                    {
                        var count = 0;
                        foreach (var detail in injectionBeforeInjectionUsage)
                        {
                            var validateResults = ValidationDetail(detail, flag, sinDate, refillSetting);
                            if (validateResults.Count > 0)
                            {
                                foreach (var validateResult in validateResults)
                                {
                                    validates.Add(new(count.ToString(), validateResult));
                                }
                            }
                            count++;
                        }
                    }
                }
                else
                {
                    validates.Add(new(odrValidateCode, OrdInfValidationStatus.InvalidHasInjectionButNotUsage));
                }
            }

            return validates;
        }

        #endregion

        #region Seperate Validate OdrInfDetail
        private static List<OrdInfValidationStatus> ValidateCommonDetail(TOdrInfDetailModel odrInfDetail)
        {
            List<OrdInfValidationStatus> validates = new();
            if (odrInfDetail.RowNo <= 0)
            {
                validates.Add(OrdInfValidationStatus.InvalidRowNo);
            }
            if (odrInfDetail.SinKouiKbn < 0)
            {
                validates.Add(OrdInfValidationStatus.InvalidSinKouiKbn);
            }
            if (odrInfDetail.ItemCd.Length > 10)
            {
                validates.Add(OrdInfValidationStatus.InvalidItemCd);
            }
            if (odrInfDetail.CmtOpt.Length > 240 && !(odrInfDetail.ItemCd.StartsWith("CO") || string.IsNullOrEmpty(odrInfDetail.ItemCd) || odrInfDetail.Is842Cmt || odrInfDetail.Is830Cmt))
            {
                validates.Add(OrdInfValidationStatus.InvalidCmtOpt);
            }
            if (odrInfDetail.ItemName.Length > 240)
            {
                validates.Add(OrdInfValidationStatus.InvalidItemName);
            }
            if (!odrInfDetail.ItemCd.StartsWith("J") && !string.IsNullOrEmpty(odrInfDetail.UnitName.Trim()) && odrInfDetail.Suryo == -1)
            {
                validates.Add(OrdInfValidationStatus.InvalidPrice);
            }
            if ((odrInfDetail.Suryo < 0 && !odrInfDetail.ItemCd.StartsWith("J")) || odrInfDetail.ItemCd == ItemCdConst.JikanKihon && !(odrInfDetail.Suryo >= 0 && odrInfDetail.Suryo <= 7) || odrInfDetail.ItemCd == ItemCdConst.SyosaiKihon && !(odrInfDetail.Suryo >= 0 && odrInfDetail.Suryo <= 8))
            {
                validates.Add(OrdInfValidationStatus.InvalidSuryo);
            }
            if (odrInfDetail.UnitName.Trim().Length > 24)
            {
                validates.Add(OrdInfValidationStatus.InvalidUnitName);
            }
            if (!(odrInfDetail.SyohoKbn >= 0 && odrInfDetail.SyohoKbn <= 4))
            {
                validates.Add(OrdInfValidationStatus.InvalidSyohoKbn);
            }
            if (!(odrInfDetail.YohoKbn >= 0 && odrInfDetail.YohoKbn <= 2))
            {
                validates.Add(OrdInfValidationStatus.InvalidYohoKbn);
            }
            if (odrInfDetail.Bunkatu.Length > 10)
            {
                validates.Add(OrdInfValidationStatus.InvalidBunkatuLength);
            }
            if (odrInfDetail.CmtName.Length > 240)
            {
                validates.Add(OrdInfValidationStatus.InvalidCmtName);
            }

            return validates;
        }

        private static List<OrdInfValidationStatus> ValidateCommonDetailInputItem(TOdrInfDetailModel odrInfDetail)
        {
            List<OrdInfValidationStatus> validates = new();
            if (odrInfDetail.HpId <= 0)
            {
                validates.Add(OrdInfValidationStatus.InvalidHpId);
            }
            if (odrInfDetail.RpNo < 0)
            {
                validates.Add(OrdInfValidationStatus.InvalidRpNo);
            }
            if (odrInfDetail.RpEdaNo < 0)
            {
                validates.Add(OrdInfValidationStatus.InvalidRpEdaNo);
            }
            if (odrInfDetail.RowNo <= 0)
            {
                validates.Add(OrdInfValidationStatus.InvalidRowNo);
            }
            if (odrInfDetail.SinKouiKbn < 0)
            {
                validates.Add(OrdInfValidationStatus.InvalidSinKouiKbn);
            }
            if (odrInfDetail.UnitSbt != 0 && odrInfDetail.UnitSbt != 1 && odrInfDetail.UnitSbt != 2)
            {
                validates.Add(OrdInfValidationStatus.InvalidUnitSbt);
            }
            if (odrInfDetail.TermVal < 0)
            {
                validates.Add(OrdInfValidationStatus.InvalidTermVal);
            }
            if (!(odrInfDetail.SyohoLimitKbn >= 0 && odrInfDetail.SyohoLimitKbn <= 3))
            {
                validates.Add(OrdInfValidationStatus.InvalidSyohoLimitKbn);
            }
            if (!(odrInfDetail.IsNodspRece >= 0 && odrInfDetail.IsNodspRece <= 1))
            {
                validates.Add(OrdInfValidationStatus.InvalidIsNodspRece);
            }
            if (odrInfDetail.IpnCd.Length > 12)
            {
                validates.Add(OrdInfValidationStatus.InvalidIpnCd);
            }
            if (odrInfDetail.IpnName.Length > 120)
            {
                validates.Add(OrdInfValidationStatus.InvalidIpnName);
            }
            if (odrInfDetail.FontColor.Length > 8)
            {
                validates.Add(OrdInfValidationStatus.InvalidFontColor);
            }
            if (!(odrInfDetail.CommentNewline >= 0 && odrInfDetail.CommentNewline <= 1))
            {
                validates.Add(OrdInfValidationStatus.InvalidCommentNewline);
            }

            return validates;
        }

        private static List<OrdInfValidationStatus> ValidateCmtDetail(int sinDate, TOdrInfDetailModel odrInfDetail)
        {
            List<OrdInfValidationStatus> validates = new();
            if (odrInfDetail.Is840Cmt)
            {
                string cmtOpt = OdrUtil.GetCmtOpt840(odrInfDetail.CmtOpt);
                if (odrInfDetail.CmtCol1 > 0 && (string.IsNullOrEmpty(cmtOpt) || string.IsNullOrEmpty(odrInfDetail.CmtName)))
                {
                    validates.Add(OrdInfValidationStatus.InvalidCmt840);
                }
            }

            if (odrInfDetail.Is842Cmt)
            {
                if (string.IsNullOrEmpty(odrInfDetail.CmtOpt) || string.IsNullOrEmpty(odrInfDetail.CmtName))
                {
                    validates.Add(OrdInfValidationStatus.InvalidCmt842);
                }
                else if (!string.IsNullOrEmpty(odrInfDetail.CmtOpt) && odrInfDetail.CmtOpt.Length > 38)
                {
                    validates.Add(OrdInfValidationStatus.InvalidCmt842CmtOptMoreThan38);
                }
            }

            if (odrInfDetail.Is830Cmt)
            {
                string fullSpace = @"　";

                if (string.IsNullOrEmpty(odrInfDetail.CmtOpt) && odrInfDetail.CmtOpt != fullSpace)
                {
                    validates.Add(OrdInfValidationStatus.InvalidCmt830CmtOpt);
                }
                else if (!string.IsNullOrEmpty(odrInfDetail.CmtOpt) && odrInfDetail.CmtOpt.Length > 38)
                {
                    validates.Add(OrdInfValidationStatus.InvalidCmt830CmtOptMoreThan38);
                }
            }

            if (odrInfDetail.Is831Cmt && (string.IsNullOrEmpty(odrInfDetail.CmtOpt) || string.IsNullOrEmpty(odrInfDetail.CmtName)))
            {
                validates.Add(OrdInfValidationStatus.InvalidCmt831);
            }

            if (odrInfDetail.Is850Cmt)
            {
                string cmtOpt = OdrUtil.GetCmtOpt850(odrInfDetail.CmtOpt, odrInfDetail.ItemName);
                if (string.IsNullOrEmpty(cmtOpt) || string.IsNullOrEmpty(odrInfDetail.CmtName))
                {
                    if (odrInfDetail.CmtName.Contains('日'))
                    {
                        validates.Add(OrdInfValidationStatus.InvalidCmt850Date);
                    }
                    else
                    {
                        validates.Add(OrdInfValidationStatus.InvalidCmt850OtherDate);
                    }
                }
            }

            if (odrInfDetail.Is851Cmt)
            {
                string cmtOpt = OdrUtil.GetCmtOpt851(odrInfDetail.CmtOpt);
                if (string.IsNullOrEmpty(cmtOpt) || string.IsNullOrEmpty(odrInfDetail.CmtName))
                {
                    validates.Add(OrdInfValidationStatus.InvalidCmt851);
                }
            }

            if (odrInfDetail.Is852Cmt)
            {
                string cmtOpt = OdrUtil.GetCmtOpt852(odrInfDetail.CmtOpt);
                if (string.IsNullOrEmpty(cmtOpt) || string.IsNullOrEmpty(odrInfDetail.CmtName))
                {
                    validates.Add(OrdInfValidationStatus.InvalidCmt852);
                }
            }

            if (odrInfDetail.Is853Cmt)
            {
                string cmtOpt = OdrUtil.GetCmtOpt853(odrInfDetail.CmtOpt, sinDate);
                if (string.IsNullOrEmpty(cmtOpt) || string.IsNullOrEmpty(odrInfDetail.CmtName))
                {
                    validates.Add(OrdInfValidationStatus.InvalidCmt853);
                }
            }

            if (odrInfDetail.Is880Cmt && (string.IsNullOrEmpty(odrInfDetail.CmtOpt) || string.IsNullOrEmpty(odrInfDetail.CmtName)))
            {
                validates.Add(OrdInfValidationStatus.InvalidCmt880);
            }

            if (odrInfDetail.ItemCd.StartsWith("CO") || string.IsNullOrEmpty(odrInfDetail.ItemCd))
            {
                if (!string.IsNullOrEmpty(odrInfDetail.CmtOpt) && odrInfDetail.CmtOpt.Length > 240)
                {
                    validates.Add(OrdInfValidationStatus.InvalidCmtCOFreeOptMoreThan240);
                }
            }

            return validates;
        }
        #endregion
    }
}
