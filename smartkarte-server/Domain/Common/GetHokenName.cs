﻿using Domain.Models.Insurance;
using Helper.Constants;

namespace Domain.Common
{
    public static class GetHokenName
    {
        public static string CreateHokenName(string hokenHoubetu, int hokenSbtCd, List<KohiInfModel> kohi, int hokenKbn)
        {
            string hokenName = string.Empty;
            string kohiName = string.Empty;

            if (!string.IsNullOrWhiteSpace(hokenHoubetu) && hokenSbtCd >= 0 && hokenKbn >= 0)
            {
                switch (hokenKbn)
                {
                    case 0:

                        if (hokenHoubetu == HokenConstant.HOUBETU_JIHI_108)
                        {
                            hokenName += "自費 ";
                            return hokenName;
                        }

                        if (hokenHoubetu == HokenConstant.HOUBETU_JIHI_109 || hokenHoubetu == HokenConstant.HOUBETU_68)
                        {
                            hokenName += "自費レセ ";
                            return hokenName;
                        }
                        break;
                    case 11:
                        hokenName += "労災（短期給付）";
                        return hokenName;
                    case 12:
                        hokenName += "労災（傷病年金）";
                        return hokenName;
                    case 13:
                        hokenName += "労災（アフターケア）";
                        return hokenName;
                    case 14:
                        hokenName += "自賠責";
                        return hokenName;
                    default:
                        break;
                }

                foreach (var item in kohi)
                {
                    if (item != null && item?.HokenId != 0 && item?.SeqNo != 0)
                    {
                        if (!string.IsNullOrEmpty(kohiName))
                        {
                            kohiName += "+";
                        }
                        if (item?.Houbetu != "102")
                        {
                            kohiName += item?.Houbetu ?? string.Empty;
                        }
                        else
                        {
                            kohiName += "マル長";
                        }
                    }
                }

                int classifyHoken = hokenSbtCd / 100;
                int classifyKohi = hokenSbtCd % 100;

                switch (classifyHoken)
                {
                    case 1:
                        if (classifyKohi == 1)
                        {
                            hokenName = "社保";
                            return hokenName;
                        }

                        if (classifyKohi >= 2)
                        {
                            hokenName = "社保 " + kohiName;
                            return hokenName;
                        }
                        break;
                    case 2:
                        if (classifyKohi == 1)
                        {
                            hokenName = "国保";
                            return hokenName;
                        }

                        if (classifyKohi >= 2)
                        {
                            hokenName = "国保 " + kohiName;
                            return hokenName;
                        }
                        break;
                    case 3:
                        if (classifyKohi == 1)
                        {
                            hokenName = "後期";
                            return hokenName;
                        }

                        if (classifyKohi >= 2)
                        {
                            hokenName = "後期 " + kohiName;
                            return hokenName;
                        }
                        break;
                    case 4:
                        if (classifyKohi == 1)
                        {
                            hokenName = "退職単独";
                            return hokenName;
                        }

                        if (classifyKohi >= 2)
                        {
                            hokenName = "退職 " + kohiName;
                            return hokenName;
                        }
                        break;
                    case 5:
                        if (classifyKohi == 1 || classifyKohi == 0)
                        {
                            hokenName = "公費";
                            return hokenName;
                        }

                        if (classifyKohi >= 2)
                        {
                            hokenName = "公費 " + kohiName;
                            return hokenName;
                        }
                        break;
                }

            }
            return hokenName;
        }
    }
}
