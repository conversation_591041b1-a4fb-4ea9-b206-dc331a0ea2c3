{"TenantDbWriter": "host=develop-smartkarte-postgres.ckthopedhq8w.ap-northeast-1.rds.amazonaws.com;port=5432;database=smartkarte_new;user id=postgres;password=************", "TenantDbReader": "host=develop-smartkarte-postgres.ckthopedhq8w.ap-northeast-1.rds.amazonaws.com;port=5432;database=smartkarte_new;user id=postgres;password=************", "SuperAdminDb": "host=develop-smartkarte-logging.ckthopedhq8w.ap-northeast-1.rds.amazonaws.com;port=5432;database=SuperAdmin;user id=postgres;password=************", "DomainList": {"smartkarte.sotatek.works": "host=develop-smartkarte-postgres.ckthopedhq8w.ap-northeast-1.rds.amazonaws.com;port=5432;database=smartkarte;user id=postgres;password=************", "uat-tenant.smartkarte.org": "host=develop-smartkarte-postgres.ckthopedhq8w.ap-northeast-1.rds.amazonaws.com;port=5432;database=smartkarte_new;user id=postgres;password=************", "gmoho.smartkarte.org": "host=develop-smartkarte-postgres.ckthopedhq8w.ap-northeast-1.rds.amazonaws.com;port=5432;database=gmoho_smartkarte;user id=postgres;password=************"}, "AmazonS3": {"AwsAccessKeyId": "********************", "AwsSecretAccessKey": "WBD7T0ThzBfd87iLyZG7l7DCmUIBmuPixDczPmmO", "Region": "ap-southeast-1", "BucketName": "develop-smartkarte-images-bucket", "BucketNameReplication": "develop-smartkarte-images-bucket-replication", "BaseAccessUrl": "https://develop-smartkarte-images-bucket.s3.ap-southeast-1.amazonaws.com"}, "DefaultImageDrugEmpty": "https://develop-smartkarte-images-bucket.s3.ap-southeast-1.amazonaws.com/image/defaultDrug/ImageDrugEmpty.png", "Karte2TemplateDefault": "https://develop-smartkarte-images-bucket.s3.ap-southeast-1.amazonaws.com/image/karte2Template/index.html", "DrugInfoTemplateDefault": "https://develop-smartkarte-images-bucket.s3.ap-southeast-1.amazonaws.com/image/drugInforTemplate/index.html", "PathImageDrugServer": "https://develop-smartkarte-images-bucket.s3.ap-southeast-1.amazonaws.com", "RenderPdf": {"BasePath": "https://smartkarte-report.sotatek.works/api/", "TimeoutSeconds": 4000}, "CalculateApi": {"BasePath": "https://smartkarte-calculate.sotatek.works/api/", "TimeoutSeconds": 4000, "WssPath": "wss://smartkarte-calculate.sotatek.works/CommonHub?tenantId="}, "RenderKarte2ReportApi": {"BasePath": "https://smartkarte-report2.sotatek.works/forms/chromium/convert/html"}, "Redis": {"RedisHost": "develop-smartkarte-redis.jyjoo4.ng.0001.apne1.cache.amazonaws.com", "RedisPort": "6379"}, "Pepper": "Sotatek", "Auth": {"Path": "http://*************:8080/auth/check", "AgentPath": "http://*************:8080/auth/agent/check"}}