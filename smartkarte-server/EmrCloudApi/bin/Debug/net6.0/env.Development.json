{"TenantDbWriter": "host=localhost;port=5433;database=local_gmoht;user id=local_gmoht;password=local_gmoht;SearchPath=local_gmoht;", "TenantDbReader": "host=localhost;port=5430;database=local_gmoht;user id=local_gmoht;password=local_gmoht;SearchPath=local_gmoht;", "SuperAdminDb": "host=localhost;port=5433;database=local_gmoht;user id=local_gmoht;password=local_gmoht", "DenkaruServer": {"Path": "http://localhost:8080", "InternalApiKey": "internalapikey"}, "AdminDatabase": "host=localhost;port=5433;database=local_gmoht;user id=local_gmoht;password=local_gmoht", "AmazonS3": {"AwsAccessKeyId": "Please enter manually", "AwsSecretAccessKey": "Please enter manually", "Region": "ap-northeast-1", "BucketName": "tf-smacli-dev2-image-bucket", "BucketNameReplication": "tf-smacli-dev2-image-bucket-replication", "BaseAccessUrl": "https://tf-smacli-dev2-image-bucket.s3.ap-northeast-1.amazonaws.com"}, "Karte2TemplateDefault": "https://develop-smartkarte-images-bucket.s3.ap-southeast-1.amazonaws.com/image/karte2Template/index.html", "DrugInfoTemplateDefault": "https://develop-smartkarte-images-bucket.s3.ap-southeast-1.amazonaws.com/image/drugInforTemplate/index.html", "RenderPdf": {"BasePath": "http://localhost:8070/api/", "TimeoutSeconds": 4000}, "CalculateApi": {"BasePath": "http://localhost:5146/api/", "TimeoutSeconds": 4000, "WssPath": "ws://localhost:5146/CommonHub?tenantId="}, "RenderKarte2ReportApi": {"BasePath": "http://localhost:3001/forms/chromium/convert/html"}, "Redis": {"RedisHost": "localhost", "RedisPort": "6379"}, "Auth": {"Path": "http://localhost:8080/auth/check", "BaseUrl": "http://localhost:8080/auth"}}