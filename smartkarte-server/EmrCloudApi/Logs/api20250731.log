2025-07-31T10:43:35.3987890+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/User/GetList?SinDate=********&IsDoctorOnly=true - 1387
2025-07-31T10:43:35.3987890+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/JsonSetting/Get?key=RECEPTION_FILTER_STATE application/json 379
2025-07-31T10:43:35.3988020+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/LabelMst/GetList application/json 346
2025-07-31T10:43:35.4501250+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/JsonSetting/Get?key=RECEPTION_FILTER_STATE application/json 379 - 401 - application/json;+charset=utf-8 51.7801ms
2025-07-31T10:43:35.4501930+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/User/GetList?SinDate=********&IsDoctorOnly=true - 1387 - 401 - application/json;+charset=utf-8 51.8258ms
2025-07-31T10:43:35.4523700+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/LabelMst/GetList application/json 346 - 401 - application/json;+charset=utf-8 54.1820ms
2025-07-31T10:43:37.5431390+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 POST http://host.docker.internal:5286/api/Lock/RemoveAllLockMedicalTab application/json 51
2025-07-31T10:43:37.5796380+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 POST http://host.docker.internal:5286/api/Lock/RemoveAllLockMedicalTab application/json 51 - 401 - application/json;+charset=utf-8 37.1364ms
2025-07-31T10:44:28.5927040+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/Reception/GetHpInf application/json 572
2025-07-31T10:44:28.6081760+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.ReceptionController.GetHpInf (EmrCloudApi)"'
2025-07-31T10:44:28.6252340+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetHpInf\", controller = \"Reception\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.Reception.GetHpInfResponse]] GetHpInf(EmrCloudApi.Requests.Reception.GetHpInfRequest)" on controller "EmrCloudApi.Controller.ReceptionController" ("EmrCloudApi").
2025-07-31T10:44:28.6523310+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.ReceptionController.GetHpInf (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.ReceptionController.GetHpInf(GetHpInfRequest request) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/ReceptionController.cs:line 517
   at lambda_method1984(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-31T10:44:28.6654640+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-31T10:44:28.6657120+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.ReceptionController.GetHpInf (EmrCloudApi)" in 40.347ms
2025-07-31T10:44:28.6659240+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.ReceptionController.GetHpInf (EmrCloudApi)"'
2025-07-31T10:44:28.6660290+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/Reception/GetHpInf" responded 400 in 58.0523 ms
2025-07-31T10:44:28.6672810+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/Reception/GetHpInf application/json 572 - 400 - application/json;+charset=utf-8 74.7723ms
2025-07-31T10:44:28.7433860+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 POST http://host.docker.internal:5286/api/KensaCenterPartnership/UpdateKensaCenterPartnershipMstUpdateDate application/json 374
2025-07-31T10:44:28.7619410+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/LabelMst/GetList application/json 346
2025-07-31T10:44:28.7629610+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/JsonSetting/Get?key=RECEPTION_FILTER_STATE application/json 379
2025-07-31T10:44:28.8149330+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)"'
2025-07-31T10:44:28.8174350+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"Get\", controller = \"JsonSetting\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.JsonSetting.GetJsonSettingResponse]] Get(EmrCloudApi.Requests.JsonSetting.GetJsonSettingRequest)" on controller "EmrCloudApi.Controller.JsonSettingController" ("EmrCloudApi").
2025-07-31T10:44:28.8245080+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/User/GetList?SinDate=********&IsDoctorOnly=true - 1387
2025-07-31T10:44:28.8245390+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.KensaCenterPartnershipController.UpdateKensaCenterPartnershipMstUpdateDate (EmrCloudApi)"'
2025-07-31T10:44:28.8247170+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.JsonSettingController.Get(GetJsonSettingRequest req) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/JsonSettingController.cs:line 31
   at lambda_method14(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-31T10:44:28.8257240+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)"'
2025-07-31T10:44:28.8260720+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetList\", controller = \"LabelMst\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.LabelMst.GetListLabelMstResponse]] GetList()" on controller "EmrCloudApi.Controller.LabelMstController" ("EmrCloudApi").
2025-07-31T10:44:28.8260910+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"UpdateKensaCenterPartnershipMstUpdateDate\", controller = \"KensaCenterPartnership\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.KensaCenterPartnership.KensaCenterPartnershipResponse]] UpdateKensaCenterPartnershipMstUpdateDate()" on controller "EmrCloudApi.Controller.KensaCenterPartnershipController" ("EmrCloudApi").
2025-07-31T10:44:28.8573160+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.LabelMstController.GetList() in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/LabelMstController.cs:line 27
   at lambda_method13(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-31T10:44:28.8742310+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-31T10:44:28.8751410+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)" in 56.8447ms
2025-07-31T10:44:28.8752940+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)"'
2025-07-31T10:44:28.8753580+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/JsonSetting/Get" responded 400 in 60.4432 ms
2025-07-31T10:44:28.8774840+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-31T10:44:28.8780240+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)" in 51.8024ms
2025-07-31T10:44:28.8781560+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)"'
2025-07-31T10:44:28.8782170+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/LabelMst/GetList" responded 400 in 52.5035 ms
2025-07-31T10:44:28.8783310+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/JsonSetting/Get?key=RECEPTION_FILTER_STATE application/json 379 - 400 - application/json;+charset=utf-8 116.2571ms
2025-07-31T10:44:28.8797270+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.KensaCenterPartnershipController.UpdateKensaCenterPartnershipMstUpdateDate (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.KensaCenterPartnershipController.UpdateKensaCenterPartnershipMstUpdateDate() in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/KensaCenterPartnershipController.cs:line 107
   at lambda_method2024(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-31T10:44:28.8811410+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/LabelMst/GetList application/json 346 - 400 - application/json;+charset=utf-8 119.1856ms
2025-07-31T10:44:28.8832600+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)"'
2025-07-31T10:44:28.8836450+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetList\", controller = \"User\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.User.GetUserListResponse]] GetList(EmrCloudApi.Requests.User.GetUserListRequest)" on controller "EmrCloudApi.Controller.UserController" ("EmrCloudApi").
2025-07-31T10:44:28.8971950+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-31T10:44:28.8974820+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.KensaCenterPartnershipController.UpdateKensaCenterPartnershipMstUpdateDate (EmrCloudApi)" in 71.1708ms
2025-07-31T10:44:28.8985740+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.KensaCenterPartnershipController.UpdateKensaCenterPartnershipMstUpdateDate (EmrCloudApi)"'
2025-07-31T10:44:28.8986800+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "POST" "/api/KensaCenterPartnership/UpdateKensaCenterPartnershipMstUpdateDate" responded 400 in 74.1458 ms
2025-07-31T10:44:28.8999930+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 POST http://host.docker.internal:5286/api/KensaCenterPartnership/UpdateKensaCenterPartnershipMstUpdateDate application/json 374 - 400 - application/json;+charset=utf-8 156.6079ms
2025-07-31T10:44:28.9004650+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.UserController.GetList (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.UserController.GetList(GetUserListRequest req) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/UserController.cs:line 59
   at lambda_method15(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-31T10:44:28.9094630+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-31T10:44:28.9104920+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)" in 26.594ms
2025-07-31T10:44:28.9116190+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)"'
2025-07-31T10:44:28.9117390+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/User/GetList" responded 400 in 28.4908 ms
2025-07-31T10:44:28.9139610+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/User/GetList?SinDate=********&IsDoctorOnly=true - 1387 - 400 - application/json;+charset=utf-8 89.4720ms
2025-07-31T10:44:36.5800220+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/JsonSetting/Get?key=RECEPTION_FILTER_STATE application/json 379
2025-07-31T10:44:36.5804360+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/LabelMst/GetList application/json 346
2025-07-31T10:44:36.5860200+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/User/GetList?SinDate=********&IsDoctorOnly=true - 1387
2025-07-31T10:44:36.6354140+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)"'
2025-07-31T10:44:36.6359230+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetList\", controller = \"User\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.User.GetUserListResponse]] GetList(EmrCloudApi.Requests.User.GetUserListRequest)" on controller "EmrCloudApi.Controller.UserController" ("EmrCloudApi").
2025-07-31T10:44:36.6425800+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)"'
2025-07-31T10:44:36.6431410+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"Get\", controller = \"JsonSetting\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.JsonSetting.GetJsonSettingResponse]] Get(EmrCloudApi.Requests.JsonSetting.GetJsonSettingRequest)" on controller "EmrCloudApi.Controller.JsonSettingController" ("EmrCloudApi").
2025-07-31T10:44:36.6443030+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.UserController.GetList (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.UserController.GetList(GetUserListRequest req) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/UserController.cs:line 59
   at lambda_method15(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-31T10:44:36.6455540+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.JsonSettingController.Get(GetJsonSettingRequest req) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/JsonSettingController.cs:line 31
   at lambda_method14(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-31T10:44:36.6484540+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)"'
2025-07-31T10:44:36.6486610+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetList\", controller = \"LabelMst\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.LabelMst.GetListLabelMstResponse]] GetList()" on controller "EmrCloudApi.Controller.LabelMstController" ("EmrCloudApi").
2025-07-31T10:44:36.6505460+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-31T10:44:36.6507410+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-31T10:44:36.6507820+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.LabelMstController.GetList() in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/LabelMstController.cs:line 27
   at lambda_method13(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-31T10:44:36.6509580+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)" in 7.7151ms
2025-07-31T10:44:36.6509580+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)" in 14.8065ms
2025-07-31T10:44:36.6510700+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)"'
2025-07-31T10:44:36.6511060+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/JsonSetting/Get" responded 400 in 8.5340 ms
2025-07-31T10:44:36.6513500+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)"'
2025-07-31T10:44:36.6513850+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/User/GetList" responded 400 in 16.0520 ms
2025-07-31T10:44:36.6542580+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/JsonSetting/Get?key=RECEPTION_FILTER_STATE application/json 379 - 400 - application/json;+charset=utf-8 74.3527ms
2025-07-31T10:44:36.6543900+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/User/GetList?SinDate=********&IsDoctorOnly=true - 1387 - 400 - application/json;+charset=utf-8 68.3639ms
2025-07-31T10:44:36.6556670+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-31T10:44:36.6561530+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)" in 7.4074ms
2025-07-31T10:44:36.6563020+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)"'
2025-07-31T10:44:36.6564390+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/LabelMst/GetList" responded 400 in 7.9854 ms
2025-07-31T10:44:36.6584000+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/LabelMst/GetList application/json 346 - 400 - application/json;+charset=utf-8 77.9769ms
2025-07-31T10:44:48.3157080+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/JsonSetting/Get?key=RECEPTION_FILTER_STATE application/json 379
2025-07-31T10:44:48.3183500+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/LabelMst/GetList application/json 346
2025-07-31T10:44:48.3213260+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/User/GetList?SinDate=********&IsDoctorOnly=true - 1387
2025-07-31T10:44:48.3353140+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)"'
2025-07-31T10:44:48.3355070+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)"'
2025-07-31T10:44:48.3355290+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetList\", controller = \"LabelMst\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.LabelMst.GetListLabelMstResponse]] GetList()" on controller "EmrCloudApi.Controller.LabelMstController" ("EmrCloudApi").
2025-07-31T10:44:48.3356380+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetList\", controller = \"User\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.User.GetUserListResponse]] GetList(EmrCloudApi.Requests.User.GetUserListRequest)" on controller "EmrCloudApi.Controller.UserController" ("EmrCloudApi").
2025-07-31T10:44:48.3362920+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)"'
2025-07-31T10:44:48.3365620+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"Get\", controller = \"JsonSetting\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.JsonSetting.GetJsonSettingResponse]] Get(EmrCloudApi.Requests.JsonSetting.GetJsonSettingRequest)" on controller "EmrCloudApi.Controller.JsonSettingController" ("EmrCloudApi").
2025-07-31T10:44:48.3374100+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.LabelMstController.GetList() in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/LabelMstController.cs:line 27
   at lambda_method13(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-31T10:44:48.3381000+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.JsonSettingController.Get(GetJsonSettingRequest req) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/JsonSettingController.cs:line 31
   at lambda_method14(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-31T10:44:48.3403640+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.UserController.GetList (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.UserController.GetList(GetUserListRequest req) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/UserController.cs:line 59
   at lambda_method15(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-31T10:44:48.3429070+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-31T10:44:48.3430200+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-31T10:44:48.3431660+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)" in 7.4888ms
2025-07-31T10:44:48.3431660+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)" in 6.3621ms
2025-07-31T10:44:48.3432630+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)"'
2025-07-31T10:44:48.3433290+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)"'
2025-07-31T10:44:48.3433640+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/LabelMst/GetList" responded 400 in 8.0564 ms
2025-07-31T10:44:48.3433890+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/JsonSetting/Get" responded 400 in 7.0979 ms
2025-07-31T10:44:48.3446870+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/JsonSetting/Get?key=RECEPTION_FILTER_STATE application/json 379 - 400 - application/json;+charset=utf-8 28.9931ms
2025-07-31T10:44:48.3447350+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/LabelMst/GetList application/json 346 - 400 - application/json;+charset=utf-8 26.4043ms
2025-07-31T10:44:48.3466550+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-31T10:44:48.3470990+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)" in 11.3037ms
2025-07-31T10:44:48.3471880+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)"'
2025-07-31T10:44:48.3472210+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/User/GetList" responded 400 in 11.7104 ms
2025-07-31T10:44:48.3486700+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/User/GetList?SinDate=********&IsDoctorOnly=true - 1387 - 400 - application/json;+charset=utf-8 27.3741ms
2025-07-31T10:48:26.8809510+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/LabelMst/GetList application/json 346
2025-07-31T10:48:26.8830150+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/JsonSetting/Get?key=RECEPTION_FILTER_STATE application/json 379
2025-07-31T10:48:26.8841170+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/User/GetList?SinDate=********&IsDoctorOnly=true - 1387
2025-07-31T10:48:26.9152740+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)"'
2025-07-31T10:48:26.9160700+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"Get\", controller = \"JsonSetting\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.JsonSetting.GetJsonSettingResponse]] Get(EmrCloudApi.Requests.JsonSetting.GetJsonSettingRequest)" on controller "EmrCloudApi.Controller.JsonSettingController" ("EmrCloudApi").
2025-07-31T10:48:26.9194160+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)"'
2025-07-31T10:48:26.9197240+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetList\", controller = \"LabelMst\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.LabelMst.GetListLabelMstResponse]] GetList()" on controller "EmrCloudApi.Controller.LabelMstController" ("EmrCloudApi").
2025-07-31T10:48:26.9222540+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.LabelMstController.GetList() in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/LabelMstController.cs:line 27
   at lambda_method13(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-31T10:48:26.9222580+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.JsonSettingController.Get(GetJsonSettingRequest req) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/JsonSettingController.cs:line 31
   at lambda_method14(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-31T10:48:26.9240180+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)"'
2025-07-31T10:48:26.9244410+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetList\", controller = \"User\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.User.GetUserListResponse]] GetList(EmrCloudApi.Requests.User.GetUserListRequest)" on controller "EmrCloudApi.Controller.UserController" ("EmrCloudApi").
2025-07-31T10:48:26.9692690+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-31T10:48:26.9697700+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)" in 49.9613ms
2025-07-31T10:48:26.9698830+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)"'
2025-07-31T10:48:26.9700110+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/LabelMst/GetList" responded 400 in 50.5684 ms
2025-07-31T10:48:26.9808570+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-31T10:48:26.9813260+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)" in 65.1423ms
2025-07-31T10:48:26.9920070+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)"'
2025-07-31T10:48:27.0010770+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/JsonSetting/Get" responded 400 in 85.8652 ms
2025-07-31T10:48:27.0025190+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.UserController.GetList (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.UserController.GetList(GetUserListRequest req) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/UserController.cs:line 59
   at lambda_method15(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-31T10:48:27.0037860+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/JsonSetting/Get?key=RECEPTION_FILTER_STATE application/json 379 - 400 - application/json;+charset=utf-8 120.7669ms
2025-07-31T10:48:27.0037860+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/LabelMst/GetList application/json 346 - 400 - application/json;+charset=utf-8 123.1134ms
2025-07-31T10:48:27.0148320+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-31T10:48:27.0153720+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)" in 90.2661ms
2025-07-31T10:48:27.0155430+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)"'
2025-07-31T10:48:27.0156660+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/User/GetList" responded 400 in 91.6499 ms
2025-07-31T10:48:27.0224560+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/User/GetList?SinDate=********&IsDoctorOnly=true - 1387 - 400 - application/json;+charset=utf-8 138.2968ms
2025-07-31T10:48:58.1131300+09:00 [ERR] (Microsoft.AspNetCore.SignalR.StackExchangeRedis.RedisHubLifetimeManager) Connection to Redis failed.
StackExchange.Redis.RedisConnectionException: SocketClosed (ReadEndOfStream, 0-read, last-recv: 0) on localhost:6379/Subscription, Flushed/MarkProcessed, last: SUBSCRIBE, origin: ReadFromPipe, outstanding: 5, last-read: 0s ago, last-write: 0s ago, unanswered-write: 0s ago, keep-alive: 60s, state: ConnectedEstablishing, mgr: 4 of 10 available, last-heartbeat: never, last-mbeat: 0s ago, global: 0s ago, v: 2.6.111.64013
2025-07-31T10:49:58.6866520+09:00 [INF] (Microsoft.AspNetCore.SignalR.StackExchangeRedis.RedisHubLifetimeManager) Connection to Redis restored.
2025-07-31T11:10:04.5471640+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/User/GetList?SinDate=********&IsDoctorOnly=true - 1387
2025-07-31T11:10:04.5471390+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/JsonSetting/Get?key=RECEPTION_FILTER_STATE application/json 379
2025-07-31T11:10:04.5471390+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/LabelMst/GetList application/json 346
2025-07-31T11:10:04.9286560+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/LabelMst/GetList application/json 346 - 401 - application/json;+charset=utf-8 390.1580ms
2025-07-31T11:10:04.9287790+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/JsonSetting/Get?key=RECEPTION_FILTER_STATE application/json 379 - 401 - application/json;+charset=utf-8 390.3138ms
2025-07-31T11:10:04.9297400+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/User/GetList?SinDate=********&IsDoctorOnly=true - 1387 - 401 - application/json;+charset=utf-8 391.1654ms
2025-07-31T11:10:05.3921450+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 POST http://host.docker.internal:5286/api/Lock/RemoveAllLockMedicalTab application/json 51
2025-07-31T11:10:05.3985180+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 POST http://host.docker.internal:5286/api/Lock/RemoveAllLockMedicalTab application/json 51 - 401 - application/json;+charset=utf-8 6.3673ms
2025-07-31T11:10:07.6999350+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/Reception/GetHpInf application/json 572
2025-07-31T11:10:07.7505330+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.ReceptionController.GetHpInf (EmrCloudApi)"'
2025-07-31T11:10:07.7512520+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetHpInf\", controller = \"Reception\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.Reception.GetHpInfResponse]] GetHpInf(EmrCloudApi.Requests.Reception.GetHpInfRequest)" on controller "EmrCloudApi.Controller.ReceptionController" ("EmrCloudApi").
2025-07-31T11:10:07.7600600+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.ReceptionController.GetHpInf (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.ReceptionController.GetHpInf(GetHpInfRequest request) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/ReceptionController.cs:line 517
   at lambda_method1984(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-31T11:10:07.7972120+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-31T11:10:07.7975080+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.ReceptionController.GetHpInf (EmrCloudApi)" in 46.1363ms
2025-07-31T11:10:07.7975670+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.ReceptionController.GetHpInf (EmrCloudApi)"'
2025-07-31T11:10:07.7976160+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/Reception/GetHpInf" responded 400 in 47.0843 ms
2025-07-31T11:10:07.7985520+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/Reception/GetHpInf application/json 572 - 400 - application/json;+charset=utf-8 98.6205ms
2025-07-31T11:10:07.8089320+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 POST http://host.docker.internal:5286/api/KensaCenterPartnership/UpdateKensaCenterPartnershipMstUpdateDate application/json 374
2025-07-31T11:10:07.8329300+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.KensaCenterPartnershipController.UpdateKensaCenterPartnershipMstUpdateDate (EmrCloudApi)"'
2025-07-31T11:10:07.8333970+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"UpdateKensaCenterPartnershipMstUpdateDate\", controller = \"KensaCenterPartnership\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.KensaCenterPartnership.KensaCenterPartnershipResponse]] UpdateKensaCenterPartnershipMstUpdateDate()" on controller "EmrCloudApi.Controller.KensaCenterPartnershipController" ("EmrCloudApi").
2025-07-31T11:10:07.8388710+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.KensaCenterPartnershipController.UpdateKensaCenterPartnershipMstUpdateDate (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.KensaCenterPartnershipController.UpdateKensaCenterPartnershipMstUpdateDate() in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/KensaCenterPartnershipController.cs:line 107
   at lambda_method2024(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-31T11:10:07.8502850+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-31T11:10:07.8507390+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.KensaCenterPartnershipController.UpdateKensaCenterPartnershipMstUpdateDate (EmrCloudApi)" in 17.244ms
2025-07-31T11:10:07.8508710+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.KensaCenterPartnershipController.UpdateKensaCenterPartnershipMstUpdateDate (EmrCloudApi)"'
2025-07-31T11:10:07.8509190+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "POST" "/api/KensaCenterPartnership/UpdateKensaCenterPartnershipMstUpdateDate" responded 400 in 18.0041 ms
2025-07-31T11:10:07.8546050+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/JsonSetting/Get?key=RECEPTION_FILTER_STATE application/json 379
2025-07-31T11:10:07.8733340+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/LabelMst/GetList application/json 346
2025-07-31T11:10:07.8745220+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 POST http://host.docker.internal:5286/api/KensaCenterPartnership/UpdateKensaCenterPartnershipMstUpdateDate application/json 374 - 400 - application/json;+charset=utf-8 65.5480ms
2025-07-31T11:10:07.8911280+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/User/GetList?SinDate=********&IsDoctorOnly=true - 1387
2025-07-31T11:10:07.9138820+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)"'
2025-07-31T11:10:07.9143850+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"Get\", controller = \"JsonSetting\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.JsonSetting.GetJsonSettingResponse]] Get(EmrCloudApi.Requests.JsonSetting.GetJsonSettingRequest)" on controller "EmrCloudApi.Controller.JsonSettingController" ("EmrCloudApi").
2025-07-31T11:10:07.9147140+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)"'
2025-07-31T11:10:07.9148300+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetList\", controller = \"LabelMst\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.LabelMst.GetListLabelMstResponse]] GetList()" on controller "EmrCloudApi.Controller.LabelMstController" ("EmrCloudApi").
2025-07-31T11:10:07.9190120+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.LabelMstController.GetList() in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/LabelMstController.cs:line 27
   at lambda_method13(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-31T11:10:07.9191580+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.JsonSettingController.Get(GetJsonSettingRequest req) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/JsonSettingController.cs:line 31
   at lambda_method14(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-31T11:10:07.9270410+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-31T11:10:07.9271370+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)"'
2025-07-31T11:10:07.9272860+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-31T11:10:07.9273770+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)" in 12.4944ms
2025-07-31T11:10:07.9275530+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)"'
2025-07-31T11:10:07.9276110+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)" in 13.0192ms
2025-07-31T11:10:07.9276110+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/LabelMst/GetList" responded 400 in 12.8987 ms
2025-07-31T11:10:07.9276230+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetList\", controller = \"User\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.User.GetUserListResponse]] GetList(EmrCloudApi.Requests.User.GetUserListRequest)" on controller "EmrCloudApi.Controller.UserController" ("EmrCloudApi").
2025-07-31T11:10:07.9277520+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)"'
2025-07-31T11:10:07.9278470+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/JsonSetting/Get" responded 400 in 13.9796 ms
2025-07-31T11:10:07.9317840+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/LabelMst/GetList application/json 346 - 400 - application/json;+charset=utf-8 58.4107ms
2025-07-31T11:10:07.9319050+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/JsonSetting/Get?key=RECEPTION_FILTER_STATE application/json 379 - 400 - application/json;+charset=utf-8 77.3547ms
2025-07-31T11:10:07.9554120+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.UserController.GetList (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at Npgsql.NpgsqlConnection.Open(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenDbConnection(Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternal(Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.Open(Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass28_0`2.<Execute>b__0(DbContext context, TState state)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementation[TState,TResult](Func`3 operation, Func`3 verifySucceeded, TState state)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Infrastructure.Services.UserInfoService.Reload() in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Infrastructure/Services/UserInfoService.cs:line 80
   at Infrastructure.Services.UserInfoService..ctor(ITenantProvider tenantProvider, IConfiguration configuration) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Infrastructure/Services/UserInfoService.cs:line 28
   at ResolveService(ILEmitResolverBuilderRuntimeContext , ServiceProviderEngineScope )
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.UserController.GetList(GetUserListRequest req) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/UserController.cs:line 59
   at lambda_method15(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. Npgsql. 
. 
 
System.Threading.Tasks.Task Open(Boolean, System.Threading.CancellationToken)
2025-07-31T11:10:07.9687600+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-31T11:10:07.9689490+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)" in 40.7143ms
2025-07-31T11:10:07.9690040+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)"'
2025-07-31T11:10:07.9690920+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/User/GetList" responded 400 in 41.9536 ms
2025-07-31T11:10:07.9706410+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/User/GetList?SinDate=********&IsDoctorOnly=true - 1387 - 400 - application/json;+charset=utf-8 79.5530ms
2025-07-31T11:10:17.0755940+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/JsonSetting/Get?key=RECEPTION_FILTER_STATE application/json 379
2025-07-31T11:10:17.0782020+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/LabelMst/GetList application/json 346
2025-07-31T11:10:17.0805480+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/User/GetList?SinDate=********&IsDoctorOnly=true - 1387
2025-07-31T11:10:17.1013770+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)"'
2025-07-31T11:10:17.1017360+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"Get\", controller = \"JsonSetting\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.JsonSetting.GetJsonSettingResponse]] Get(EmrCloudApi.Requests.JsonSetting.GetJsonSettingRequest)" on controller "EmrCloudApi.Controller.JsonSettingController" ("EmrCloudApi").
2025-07-31T11:10:17.1032950+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)"'
2025-07-31T11:10:17.1038070+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetList\", controller = \"LabelMst\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.LabelMst.GetListLabelMstResponse]] GetList()" on controller "EmrCloudApi.Controller.LabelMstController" ("EmrCloudApi").
2025-07-31T11:10:17.1050480+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.JsonSettingController.Get(GetJsonSettingRequest req) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/JsonSettingController.cs:line 31
   at lambda_method14(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-31T11:10:17.1056890+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)"'
2025-07-31T11:10:17.1062220+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetList\", controller = \"User\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.User.GetUserListResponse]] GetList(EmrCloudApi.Requests.User.GetUserListRequest)" on controller "EmrCloudApi.Controller.UserController" ("EmrCloudApi").
2025-07-31T11:10:17.1077690+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.LabelMstController.GetList() in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/LabelMstController.cs:line 27
   at lambda_method13(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-31T11:10:17.1106110+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.UserController.GetList (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at Npgsql.NpgsqlConnection.Open(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenDbConnection(Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternal(Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.Open(Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass28_0`2.<Execute>b__0(DbContext context, TState state)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementation[TState,TResult](Func`3 operation, Func`3 verifySucceeded, TState state)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Infrastructure.Services.UserInfoService.Reload() in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Infrastructure/Services/UserInfoService.cs:line 80
   at Infrastructure.Services.UserInfoService..ctor(ITenantProvider tenantProvider, IConfiguration configuration) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Infrastructure/Services/UserInfoService.cs:line 28
   at ResolveService(ILEmitResolverBuilderRuntimeContext , ServiceProviderEngineScope )
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.UserController.GetList(GetUserListRequest req) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/UserController.cs:line 59
   at lambda_method15(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. Npgsql. 
. 
 
System.Threading.Tasks.Task Open(Boolean, System.Threading.CancellationToken)
2025-07-31T11:10:17.1131940+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-31T11:10:17.1133450+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-31T11:10:17.1135730+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)" in 9.5502ms
2025-07-31T11:10:17.1135820+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)" in 11.7697ms
2025-07-31T11:10:17.1136750+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)"'
2025-07-31T11:10:17.1137180+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)"'
2025-07-31T11:10:17.1137550+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/JsonSetting/Get" responded 400 in 12.4030 ms
2025-07-31T11:10:17.1138950+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/LabelMst/GetList" responded 400 in 10.5979 ms
2025-07-31T11:10:17.1156050+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/JsonSetting/Get?key=RECEPTION_FILTER_STATE application/json 379 - 400 - application/json;+charset=utf-8 40.0659ms
2025-07-31T11:10:17.1156150+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/LabelMst/GetList application/json 346 - 400 - application/json;+charset=utf-8 37.4227ms
2025-07-31T11:10:17.1211370+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-31T11:10:17.1213710+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)" in 15.03ms
2025-07-31T11:10:17.1215490+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)"'
2025-07-31T11:10:17.1216860+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/User/GetList" responded 400 in 16.0085 ms
2025-07-31T11:10:17.1236620+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/User/GetList?SinDate=********&IsDoctorOnly=true - 1387 - 400 - application/json;+charset=utf-8 43.1473ms
2025-07-31T11:15:41.9316230+09:00 [INF] (Microsoft.AspNetCore.SignalR.StackExchangeRedis.RedisHubLifetimeManager) Connected to Redis.
2025-07-31T11:15:41.9311360+09:00 [INF] (Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager) User profile is available. Using '"/Users/<USER>/.aspnet/DataProtection-Keys"' as key repository; keys will not be encrypted at rest.
2025-07-31T11:15:42.0699060+09:00 [INF] (Microsoft.Hosting.Lifetime) Now listening on: "http://localhost:5286"
