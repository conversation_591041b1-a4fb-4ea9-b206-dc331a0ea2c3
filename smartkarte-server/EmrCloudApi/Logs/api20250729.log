2025-07-29T18:14:33.3222120+09:00 [INF] (Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager) User profile is available. Using '"/Users/<USER>/.aspnet/DataProtection-Keys"' as key repository; keys will not be encrypted at rest.
2025-07-29T18:14:33.3299320+09:00 [INF] (Microsoft.AspNetCore.SignalR.StackExchangeRedis.RedisHubLifetimeManager) Connected to Redis.
2025-07-29T18:14:33.3864800+09:00 [INF] (Microsoft.Hosting.Lifetime) Now listening on: "http://localhost:5286"
2025-07-29T18:14:33.6634590+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/SmartKartePort/GetList?onlyCommon=false - 523
2025-07-29T18:14:33.6634720+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/KensaMst/GetInHospitalKensaMst?isExceptVital=false - 518
2025-07-29T18:14:33.6634760+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/OrdInf/GetHeaderInf?SinDate=********&RaiinNo=68&PtId=46 - 1455
2025-07-29T18:14:33.6634950+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/TodayOrd/GetInsuranceComboList?sinDate=********&ptId=46 - 790
2025-07-29T18:14:33.6634600+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 POST http://host.docker.internal:5286/api/History/GetList application/json 193
2025-07-29T18:14:33.7214580+09:00 [WRN] (Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware) Failed to determine the https port for redirect.
2025-07-29T18:14:33.7474180+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/KarteAllergy/GetKarteAllergyList?PtId=46 - 899
2025-07-29T18:14:34.0439230+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.HistoryController.GetList (EmrCloudApi)"'
2025-07-29T18:14:34.0439250+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.KensaMstController.GetInHospitalKensaMst (EmrCloudApi)"'
2025-07-29T18:14:34.0439250+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.KarteAllergyController.GetKarteAllergyList (EmrCloudApi)"'
2025-07-29T18:14:34.0439270+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.TodayOrdController.GetInsuranceComboList (EmrCloudApi)"'
2025-07-29T18:14:34.0439260+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.OrdInfController.GetHeaderInf (EmrCloudApi)"'
2025-07-29T18:14:34.0439320+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.SmartKartePortController.GetPortList (EmrCloudApi)"'
2025-07-29T18:14:34.0637830+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetInsuranceComboList\", controller = \"TodayOrd\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.InsuranceList.GetInsuranceComboListResponse]] GetInsuranceComboList(EmrCloudApi.Requests.Insurance.GetInsuranceComboListRequest)" on controller "EmrCloudApi.Controller.TodayOrdController" ("EmrCloudApi").
2025-07-29T18:14:34.0638060+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetList\", controller = \"History\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.MedicalExamination.GetMedicalExaminationHistoryResponse]] GetList(EmrCloudApi.Requests.MedicalExamination.GetMedicalExaminationHistoryRequest)" on controller "EmrCloudApi.Controller.HistoryController" ("EmrCloudApi").
2025-07-29T18:14:34.0637830+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetInHospitalKensaMst\", controller = \"KensaMst\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.KensaMst.KensaMstListResponse]] GetInHospitalKensaMst(EmrCloudApi.Requests.KensaMst.GetKensaMstRequest)" on controller "EmrCloudApi.Controller.KensaMstController" ("EmrCloudApi").
2025-07-29T18:14:34.0638100+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetKarteAllergyList\", controller = \"KarteAllergy\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.KarteAllergy.GetKarteAllergyResponse]] GetKarteAllergyList(EmrCloudApi.Requests.KarteAllergy.KarteAllergyRequest)" on controller "EmrCloudApi.Controller.KarteAllergyController" ("EmrCloudApi").
2025-07-29T18:14:34.0637830+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetHeaderInf\", controller = \"OrdInf\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.OrdInfs.GetHeaderInfResponse]] GetHeaderInf(EmrCloudApi.Requests.OrdInfs.GetMaxRpNoRequest)" on controller "EmrCloudApi.Controller.OrdInfController" ("EmrCloudApi").
2025-07-29T18:14:34.0637800+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetPortList\", controller = \"SmartKartePort\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.SmartKartePort.GetPortListResponse]] GetPortList(EmrCloudApi.Requests.SmartKartePort.GetPortListRequest)" on controller "EmrCloudApi.Controller.SmartKartePortController" ("EmrCloudApi").
2025-07-29T18:14:34.1011760+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"EmrCloudApi.Responses.Response`1[[EmrCloudApi.Responses.KarteAllergy.GetKarteAllergyResponse, EmrCloudApi, Version=*******, Culture=neutral, PublicKeyToken=null]]"'.
2025-07-29T18:14:34.1215840+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.KarteAllergyController.GetKarteAllergyList (EmrCloudApi)" in 55.2175ms
2025-07-29T18:14:34.1223330+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.KarteAllergyController.GetKarteAllergyList (EmrCloudApi)"'
2025-07-29T18:14:34.1228940+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/KarteAllergy/GetKarteAllergyList" responded 200 in 80.0064 ms
2025-07-29T18:14:34.1295690+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/KarteAllergy/GetKarteAllergyList?PtId=46 - 899 - 200 - application/json;+charset=utf-8 381.8368ms
2025-07-29T18:14:34.1332560+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/User/GetAllPermission application/json 424
2025-07-29T18:14:34.1491450+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.UserController.GetAllPermission (EmrCloudApi)"'
2025-07-29T18:14:34.1499920+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetAllPermission\", controller = \"User\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.User.GetAllPermissionResponse]] GetAllPermission()" on controller "EmrCloudApi.Controller.UserController" ("EmrCloudApi").
2025-07-29T18:14:39.6961830+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.UserController.GetAllPermission (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.UserController.GetAllPermission() in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/UserController.cs:line 152
   at lambda_method855(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:14:39.6961830+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.SmartKartePortController.GetPortList (EmrCloudApi). Interactor.SmartKartePort. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.SmartKartePortController.GetPortList(GetPortListRequest request) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/SmartKartePortController.cs:line 73
   at lambda_method22(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
The ConnectionString property has not been initialized.. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:14:39.7459210+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:14:39.7459210+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:14:39.7466970+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.UserController.GetAllPermission (EmrCloudApi)" in 5596.551ms
2025-07-29T18:14:39.7467690+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.SmartKartePortController.GetPortList (EmrCloudApi)" in 5681.886ms
2025-07-29T18:14:39.7467830+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.UserController.GetAllPermission (EmrCloudApi)"'
2025-07-29T18:14:39.7468420+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.SmartKartePortController.GetPortList (EmrCloudApi)"'
2025-07-29T18:14:39.7468850+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/User/GetAllPermission" responded 400 in 5597.7052 ms
2025-07-29T18:14:39.7469340+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/SmartKartePort/GetList" responded 400 in 5704.5078 ms
2025-07-29T18:14:39.7482610+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/SmartKartePort/GetList?onlyCommon=false - 523 - 400 - application/json;+charset=utf-8 6087.8507ms
2025-07-29T18:14:39.7482630+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/User/GetAllPermission application/json 424 - 400 - application/json;+charset=utf-8 5614.9573ms
2025-07-29T18:14:39.7529600+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/SystemConf/GetList application/json 395
2025-07-29T18:14:39.7616810+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.SystemConfController.GetSystemConfList (EmrCloudApi)"'
2025-07-29T18:14:39.7625750+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetSystemConfList\", controller = \"SystemConf\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.SystemConf.GetSystemConfListResponse]] GetSystemConfList()" on controller "EmrCloudApi.Controller.SystemConfController" ("EmrCloudApi").
2025-07-29T18:14:39.7711730+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"EmrCloudApi.Responses.Response`1[[EmrCloudApi.Responses.SystemConf.GetSystemConfListResponse, EmrCloudApi, Version=*******, Culture=neutral, PublicKeyToken=null]]"'.
2025-07-29T18:14:39.7725250+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 POST http://host.docker.internal:5286/api/AuditLog/Save application/json 195
2025-07-29T18:14:39.7732250+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.SystemConfController.GetSystemConfList (EmrCloudApi)" in 10.5305ms
2025-07-29T18:14:39.7733080+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.SystemConfController.GetSystemConfList (EmrCloudApi)"'
2025-07-29T18:14:39.7733540+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/SystemConf/GetList" responded 200 in 11.6882 ms
2025-07-29T18:14:39.7745700+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/SystemConf/GetList application/json 395 - 200 - application/json;+charset=utf-8 21.5634ms
2025-07-29T18:14:39.7791740+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.AuditLogController.Save (EmrCloudApi)"'
2025-07-29T18:14:39.7801640+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"Save\", controller = \"AuditLog\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.AuditLog.SaveAuditLogResponse]] Save(EmrCloudApi.Requests.AuditLog.SaveAuditLogRequest)" on controller "EmrCloudApi.Controller.AuditLogController" ("EmrCloudApi").
2025-07-29T18:14:39.8195880+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/PatientInfor/GetBasicPatientInfo?ptId=46 application/json 2497
2025-07-29T18:14:39.8368370+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.PatientInforController.GetBasicPatientInfo (EmrCloudApi)"'
2025-07-29T18:14:39.8398620+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetBasicPatientInfo\", controller = \"PatientInfor\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.PatientInfor.GetBasicPatientInfoResponse]] GetBasicPatientInfo(EmrCloudApi.Requests.PatientInfor.BasicPatientInfo.GetBasicPatientInfoRequest)" on controller "EmrCloudApi.Controller.PatientInforController" ("EmrCloudApi").
2025-07-29T18:14:39.9173560+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.PatientInforController.GetBasicPatientInfo (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.PatientInforController.GetBasicPatientInfo(GetBasicPatientInfoRequest request) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/PatientInforController.cs:line 2106
   at lambda_method2360(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:14:39.9217480+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:14:39.9219790+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.PatientInforController.GetBasicPatientInfo (EmrCloudApi)" in 81.9712ms
2025-07-29T18:14:39.9220510+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.PatientInforController.GetBasicPatientInfo (EmrCloudApi)"'
2025-07-29T18:14:39.9221100+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/PatientInfor/GetBasicPatientInfo" responded 400 in 85.2865 ms
2025-07-29T18:14:39.9237030+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/PatientInfor/GetBasicPatientInfo?ptId=46 application/json 2497 - 400 - application/json;+charset=utf-8 104.1413ms
2025-07-29T18:14:39.9267600+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/CustomButtonConf/ListAllCustomButtonConf - 656
2025-07-29T18:14:39.9323330+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.CustomButtonConfController.ListAllCustomButtonConfList (EmrCloudApi)"'
2025-07-29T18:14:39.9332880+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"ListAllCustomButtonConfList\", controller = \"CustomButtonConf\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.CustomButtonConf.ListAllCustomButtonConfResponse]] ListAllCustomButtonConfList(Int32, Int64)" on controller "EmrCloudApi.Controller.CustomButtonConfController" ("EmrCloudApi").
2025-07-29T18:14:39.9416180+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.CustomButtonConfController.ListAllCustomButtonConfList (EmrCloudApi). One or more errors occurred. (The ConnectionString property has not been initialized.). Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.CustomButtonConfController.ListAllCustomButtonConfList(Int32 ptId, Int64 raiinNo) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/CustomButtonConfController.cs:line 26
   at lambda_method2414(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
The ConnectionString property has not been initialized.. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:14:39.9510760+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:14:39.9513040+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.CustomButtonConfController.ListAllCustomButtonConfList (EmrCloudApi)" in 17.9239ms
2025-07-29T18:14:39.9513750+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.CustomButtonConfController.ListAllCustomButtonConfList (EmrCloudApi)"'
2025-07-29T18:14:39.9514170+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/CustomButtonConf/ListAllCustomButtonConf" responded 400 in 19.0935 ms
2025-07-29T18:14:39.9526900+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/CustomButtonConf/ListAllCustomButtonConf - 656 - 400 - application/json;+charset=utf-8 25.9235ms
2025-07-29T18:14:39.9556940+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/CustomButtonParamMst/ListCustomButtonParamMsts - 471
2025-07-29T18:14:39.9611690+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.CustomButtonParamMstController.GetCustomButtonParamMstList (EmrCloudApi)"'
2025-07-29T18:14:39.9620280+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetCustomButtonParamMstList\", controller = \"CustomButtonParamMst\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.CustomButtonParamMst.GetCustomButtonParamMstResponse]] GetCustomButtonParamMstList()" on controller "EmrCloudApi.Controller.CustomButtonParamMstController" ("EmrCloudApi").
2025-07-29T18:14:39.9654790+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"EmrCloudApi.Responses.Response`1[[EmrCloudApi.Responses.CustomButtonParamMst.GetCustomButtonParamMstResponse, EmrCloudApi, Version=*******, Culture=neutral, PublicKeyToken=null]]"'.
2025-07-29T18:14:39.9663600+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.CustomButtonParamMstController.GetCustomButtonParamMstList (EmrCloudApi)" in 4.2341ms
2025-07-29T18:14:39.9664480+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.CustomButtonParamMstController.GetCustomButtonParamMstList (EmrCloudApi)"'
2025-07-29T18:14:39.9664930+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/CustomButtonParamMst/ListCustomButtonParamMsts" responded 200 in 5.3353 ms
2025-07-29T18:14:39.9674100+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/CustomButtonParamMst/ListCustomButtonParamMsts - 471 - 200 - application/json;+charset=utf-8 11.7211ms
2025-07-29T18:14:39.9814720+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/Reception/GetLastRaiinInfs?sinDate=********&isLastVisit=true&ptId=46 - 1179
2025-07-29T18:14:39.9871260+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.ReceptionController.GetLastRaiinInfs (EmrCloudApi)"'
2025-07-29T18:14:39.9883110+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetLastRaiinInfs\", controller = \"Reception\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.Reception.GetLastRaiinInfsResponse]] GetLastRaiinInfs(EmrCloudApi.Requests.Reception.GetLastRaiinInfsRequest)" on controller "EmrCloudApi.Controller.ReceptionController" ("EmrCloudApi").
2025-07-29T18:14:40.0265740+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.ReceptionController.GetLastRaiinInfs (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.ReceptionController.GetLastRaiinInfs(GetLastRaiinInfsRequest request) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/ReceptionController.cs:line 126
   at lambda_method2470(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:14:40.0309770+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:14:40.0312190+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.ReceptionController.GetLastRaiinInfs (EmrCloudApi)" in 42.8181ms
2025-07-29T18:14:40.0312960+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.ReceptionController.GetLastRaiinInfs (EmrCloudApi)"'
2025-07-29T18:14:40.0314400+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/Reception/GetLastRaiinInfs" responded 400 in 44.3238 ms
2025-07-29T18:14:40.0325650+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/Reception/GetLastRaiinInfs?sinDate=********&isLastVisit=true&ptId=46 - 1179 - 400 - application/json;+charset=utf-8 51.0950ms
2025-07-29T18:14:40.0390250+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/Reception/Get?raiinNo=68 application/json 2049
2025-07-29T18:14:40.0497470+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.ReceptionController.Get (EmrCloudApi)"'
2025-07-29T18:14:40.0508720+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"Get\", controller = \"Reception\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.Reception.GetReceptionResponse]] Get(EmrCloudApi.Requests.Reception.GetReceptionRequest)" on controller "EmrCloudApi.Controller.ReceptionController" ("EmrCloudApi").
2025-07-29T18:14:40.0678280+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.ReceptionController.Get (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.ReceptionController.Get(GetReceptionRequest request) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/ReceptionController.cs:line 107
   at lambda_method2522(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:14:40.0725620+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:14:40.0727630+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.ReceptionController.Get (EmrCloudApi)" in 21.795ms
2025-07-29T18:14:40.0728310+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.ReceptionController.Get (EmrCloudApi)"'
2025-07-29T18:14:40.0728720+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/Reception/Get" responded 400 in 23.1431 ms
2025-07-29T18:14:40.0739060+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/Reception/Get?raiinNo=68 application/json 2049 - 400 - application/json;+charset=utf-8 34.8884ms
2025-07-29T18:14:40.0783120+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/Diseases/GetList?sinDate=********&ptId=46 - 1646
2025-07-29T18:14:40.0839240+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.DiseasesController.GetDiseaseListMedicalExamination (EmrCloudApi)"'
2025-07-29T18:14:40.0849480+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetDiseaseListMedicalExamination\", controller = \"Diseases\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.Diseases.GetPtDiseaseListResponse]] GetDiseaseListMedicalExamination(EmrCloudApi.Requests.Diseases.GetPtDiseaseListRequest)" on controller "EmrCloudApi.Controller.DiseasesController" ("EmrCloudApi").
2025-07-29T18:14:40.0995850+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.DiseasesController.GetDiseaseListMedicalExamination (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.DiseasesController.GetDiseaseListMedicalExamination(GetPtDiseaseListRequest request) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/DiseasesController.cs:line 39
   at lambda_method2573(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:14:40.1033720+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:14:40.1036670+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.DiseasesController.GetDiseaseListMedicalExamination (EmrCloudApi)" in 18.6205ms
2025-07-29T18:14:40.1037550+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.DiseasesController.GetDiseaseListMedicalExamination (EmrCloudApi)"'
2025-07-29T18:14:40.1038010+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/Diseases/GetList" responded 400 in 19.8812 ms
2025-07-29T18:14:40.1050800+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/Diseases/GetList?sinDate=********&ptId=46 - 1646 - 400 - application/json;+charset=utf-8 26.7679ms
2025-07-29T18:14:40.1084780+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/SystemGenerationConf/GetList application/json 535
2025-07-29T18:14:40.1145800+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.SystemGenerationConfController.GetList (EmrCloudApi)"'
2025-07-29T18:14:40.1154310+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetList\", controller = \"SystemGenerationConf\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.SystemGenerationConf.GetSystemGenerationConfListResponse]] GetList()" on controller "EmrCloudApi.Controller.SystemGenerationConfController" ("EmrCloudApi").
2025-07-29T18:14:40.1201340+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"EmrCloudApi.Responses.Response`1[[EmrCloudApi.Responses.SystemGenerationConf.GetSystemGenerationConfListResponse, EmrCloudApi, Version=*******, Culture=neutral, PublicKeyToken=null]]"'.
2025-07-29T18:14:40.1264400+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.SystemGenerationConfController.GetList (EmrCloudApi)" in 10.8927ms
2025-07-29T18:14:40.1265440+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.SystemGenerationConfController.GetList (EmrCloudApi)"'
2025-07-29T18:14:40.1265910+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/SystemGenerationConf/GetList" responded 200 in 12.0178 ms
2025-07-29T18:14:40.1276760+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/SystemGenerationConf/GetList application/json 535 - 200 - application/json;+charset=utf-8 19.1972ms
2025-07-29T18:14:40.1327380+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/UserConf/GetListForModel application/json 433
2025-07-29T18:14:40.1396030+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.UserConfController.GetListForModel (EmrCloudApi)"'
2025-07-29T18:14:40.1404360+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetListForModel\", controller = \"UserConf\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.UserConf.GetUserConfModelListResponse]] GetListForModel()" on controller "EmrCloudApi.Controller.UserConfController" ("EmrCloudApi").
2025-07-29T18:14:40.1439660+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"EmrCloudApi.Responses.Response`1[[EmrCloudApi.Responses.UserConf.GetUserConfModelListResponse, EmrCloudApi, Version=*******, Culture=neutral, PublicKeyToken=null]]"'.
2025-07-29T18:14:40.1447940+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.UserConfController.GetListForModel (EmrCloudApi)" in 4.2564ms
2025-07-29T18:14:40.1448750+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.UserConfController.GetListForModel (EmrCloudApi)"'
2025-07-29T18:14:40.1449180+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/UserConf/GetListForModel" responded 200 in 5.3254 ms
2025-07-29T18:14:40.1464230+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/UserConf/GetListForModel application/json 433 - 200 - application/json;+charset=utf-8 13.6806ms
2025-07-29T18:14:40.1506020+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/Diseases/GetList?sinDate=********&ptId=46&requestFrom=0 - 1643
2025-07-29T18:14:40.1577570+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.DiseasesController.GetDiseaseListMedicalExamination (EmrCloudApi)"'
2025-07-29T18:14:40.1580010+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetDiseaseListMedicalExamination\", controller = \"Diseases\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.Diseases.GetPtDiseaseListResponse]] GetDiseaseListMedicalExamination(EmrCloudApi.Requests.Diseases.GetPtDiseaseListRequest)" on controller "EmrCloudApi.Controller.DiseasesController" ("EmrCloudApi").
2025-07-29T18:14:40.1610930+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.DiseasesController.GetDiseaseListMedicalExamination (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.DiseasesController.GetDiseaseListMedicalExamination(GetPtDiseaseListRequest request) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/DiseasesController.cs:line 39
   at lambda_method2573(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:14:40.1646630+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:14:40.1648610+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.DiseasesController.GetDiseaseListMedicalExamination (EmrCloudApi)" in 6.7717ms
2025-07-29T18:14:40.1649350+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.DiseasesController.GetDiseaseListMedicalExamination (EmrCloudApi)"'
2025-07-29T18:14:40.1649780+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/Diseases/GetList" responded 400 in 7.2275 ms
2025-07-29T18:14:40.1662930+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/Diseases/GetList?sinDate=********&ptId=46&requestFrom=0 - 1643 - 400 - application/json;+charset=utf-8 15.6846ms
2025-07-29T18:14:40.1700980+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/Reception/GetLastRaiinInfs?sinDate=********&isLastVisit=true&ptId=46 - 1076
2025-07-29T18:14:40.2105280+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.ReceptionController.GetLastRaiinInfs (EmrCloudApi)"'
2025-07-29T18:14:40.2109810+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetLastRaiinInfs\", controller = \"Reception\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.Reception.GetLastRaiinInfsResponse]] GetLastRaiinInfs(EmrCloudApi.Requests.Reception.GetLastRaiinInfsRequest)" on controller "EmrCloudApi.Controller.ReceptionController" ("EmrCloudApi").
2025-07-29T18:14:40.2188880+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.ReceptionController.GetLastRaiinInfs (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.ReceptionController.GetLastRaiinInfs(GetLastRaiinInfsRequest request) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/ReceptionController.cs:line 126
   at lambda_method2470(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:14:40.2223810+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:14:40.2225660+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.ReceptionController.GetLastRaiinInfs (EmrCloudApi)" in 11.2419ms
2025-07-29T18:14:40.2227980+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.ReceptionController.GetLastRaiinInfs (EmrCloudApi)"'
2025-07-29T18:14:40.2228980+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/Reception/GetLastRaiinInfs" responded 400 in 12.3812 ms
2025-07-29T18:14:40.2239480+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/Reception/GetLastRaiinInfs?sinDate=********&isLastVisit=true&ptId=46 - 1076 - 400 - application/json;+charset=utf-8 53.8555ms
2025-07-29T18:14:40.2275030+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/Diseases/GetList?sinDate=********&hokenId=0&ptId=46&requestFrom=2 - 1640
2025-07-29T18:14:40.2334820+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.DiseasesController.GetDiseaseListMedicalExamination (EmrCloudApi)"'
2025-07-29T18:14:40.2337060+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetDiseaseListMedicalExamination\", controller = \"Diseases\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.Diseases.GetPtDiseaseListResponse]] GetDiseaseListMedicalExamination(EmrCloudApi.Requests.Diseases.GetPtDiseaseListRequest)" on controller "EmrCloudApi.Controller.DiseasesController" ("EmrCloudApi").
2025-07-29T18:14:40.2404210+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.DiseasesController.GetDiseaseListMedicalExamination (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.DiseasesController.GetDiseaseListMedicalExamination(GetPtDiseaseListRequest request) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/DiseasesController.cs:line 39
   at lambda_method2573(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:14:40.2439350+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:14:40.2441240+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.DiseasesController.GetDiseaseListMedicalExamination (EmrCloudApi)" in 10.2696ms
2025-07-29T18:14:40.2441950+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.DiseasesController.GetDiseaseListMedicalExamination (EmrCloudApi)"'
2025-07-29T18:14:40.2442370+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/Diseases/GetList" responded 400 in 10.7645 ms
2025-07-29T18:14:40.2450730+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/Diseases/GetList?sinDate=********&hokenId=0&ptId=46&requestFrom=2 - 1640 - 400 - application/json;+charset=utf-8 17.5755ms
2025-07-29T18:14:40.2471640+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/NextOrder/GetNextOrderNextCheck?ptId=46 - 470
2025-07-29T18:14:40.2525900+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.NextOrderController.GetNextOrderNextCheck (EmrCloudApi)"'
2025-07-29T18:14:40.2539990+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetNextOrderNextCheck\", controller = \"NextOrder\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.NextOrder.GetNextOrderNextCheckResponse]] GetNextOrderNextCheck(EmrCloudApi.Requests.NextOrder.GetNextOrderNextCheckRequest)" on controller "EmrCloudApi.Controller.NextOrderController" ("EmrCloudApi").
2025-07-29T18:14:40.2609450+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"EmrCloudApi.Responses.Response`1[[EmrCloudApi.Responses.NextOrder.GetNextOrderNextCheckResponse, EmrCloudApi, Version=*******, Culture=neutral, PublicKeyToken=null]]"'.
2025-07-29T18:14:40.2620510+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.NextOrderController.GetNextOrderNextCheck (EmrCloudApi)" in 7.9447ms
2025-07-29T18:14:40.2622300+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.NextOrderController.GetNextOrderNextCheck (EmrCloudApi)"'
2025-07-29T18:14:40.2622790+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/NextOrder/GetNextOrderNextCheck" responded 200 in 9.6997 ms
2025-07-29T18:14:40.2631740+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/NextOrder/GetNextOrderNextCheck?ptId=46 - 470 - 200 - application/json;+charset=utf-8 16.0114ms
2025-07-29T18:14:44.4663620+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "BadRequestObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:14:44.4669630+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.OrdInfController.GetHeaderInf (EmrCloudApi)" in 10402.6742ms
2025-07-29T18:14:44.4670520+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.OrdInfController.GetHeaderInf (EmrCloudApi)"'
2025-07-29T18:14:44.4671020+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/OrdInf/GetHeaderInf" responded 400 in 10424.6190 ms
2025-07-29T18:14:44.4675660+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"EmrCloudApi.Responses.Response`1[[EmrCloudApi.Responses.InsuranceList.GetInsuranceComboListResponse, EmrCloudApi, Version=*******, Culture=neutral, PublicKeyToken=null]]"'.
2025-07-29T18:14:44.4684900+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "BadRequestObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:14:44.4686150+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.HistoryController.GetList (EmrCloudApi)" in 10404.3565ms
2025-07-29T18:14:44.4686680+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.HistoryController.GetList (EmrCloudApi)"'
2025-07-29T18:14:44.4687010+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "POST" "/api/History/GetList" responded 400 in 10426.2293 ms
2025-07-29T18:14:44.4695630+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/OrdInf/GetHeaderInf?SinDate=********&RaiinNo=68&PtId=46 - 1455 - 400 - application/json;+charset=utf-8 10809.2859ms
2025-07-29T18:14:44.4699410+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 POST http://host.docker.internal:5286/api/History/GetList application/json 193 - 400 - application/json;+charset=utf-8 10809.5225ms
2025-07-29T18:14:44.4703350+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.TodayOrdController.GetInsuranceComboList (EmrCloudApi)" in 10406.1953ms
2025-07-29T18:14:44.4704010+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.TodayOrdController.GetInsuranceComboList (EmrCloudApi)"'
2025-07-29T18:14:44.4704400+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/TodayOrd/GetInsuranceComboList" responded 200 in 10427.9665 ms
2025-07-29T18:14:44.4719860+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.KensaMstController.GetInHospitalKensaMst (EmrCloudApi). Interactor.KensaMst. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.KensaMstController.GetInHospitalKensaMst(GetKensaMstRequest request) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/KensaMstController.cs:line 50
   at lambda_method19(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
The ConnectionString property has not been initialized.. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:14:44.4724230+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/TodayOrd/GetInsuranceComboList?sinDate=********&ptId=46 - 790 - 200 - application/json;+charset=utf-8 10810.4740ms
2025-07-29T18:14:44.4821340+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:14:44.4823200+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.KensaMstController.GetInHospitalKensaMst (EmrCloudApi)" in 10417.6483ms
2025-07-29T18:14:44.4823860+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.KensaMstController.GetInHospitalKensaMst (EmrCloudApi)"'
2025-07-29T18:14:44.4824210+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/KensaMst/GetInHospitalKensaMst" responded 400 in 10439.9503 ms
2025-07-29T18:14:44.4837720+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/KensaMst/GetInHospitalKensaMst?isExceptVital=false - 518 - 400 - application/json;+charset=utf-8 10824.3708ms
2025-07-29T18:14:44.5411790+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.AuditLogController.Save (EmrCloudApi). Interactor.AuditTrailLog. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.AuditLogController.Save(SaveAuditLogRequest req) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/AuditLogController.cs:line 35
   at lambda_method2357(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
The ConnectionString property has not been initialized.. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:14:44.5505190+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:14:44.5509930+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.AuditLogController.Save (EmrCloudApi)" in 4770.6967ms
2025-07-29T18:14:44.5511630+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.AuditLogController.Save (EmrCloudApi)"'
2025-07-29T18:14:44.5512190+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "POST" "/api/AuditLog/Save" responded 400 in 4772.0180 ms
2025-07-29T18:14:44.5534820+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 POST http://host.docker.internal:5286/api/AuditLog/Save application/json 195 - 400 - application/json;+charset=utf-8 4780.9077ms
2025-07-29T18:14:46.8745920+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/KensaMst/GetInHospitalKensaMst?isExceptVital=false - 518
2025-07-29T18:14:46.8925360+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/SmartKartePort/GetList?onlyCommon=false - 523
2025-07-29T18:14:46.8938880+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.KensaMstController.GetInHospitalKensaMst (EmrCloudApi)"'
2025-07-29T18:14:46.8947910+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetInHospitalKensaMst\", controller = \"KensaMst\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.KensaMst.KensaMstListResponse]] GetInHospitalKensaMst(EmrCloudApi.Requests.KensaMst.GetKensaMstRequest)" on controller "EmrCloudApi.Controller.KensaMstController" ("EmrCloudApi").
2025-07-29T18:14:46.9029550+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.KensaMstController.GetInHospitalKensaMst (EmrCloudApi). Interactor.KensaMst. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.KensaMstController.GetInHospitalKensaMst(GetKensaMstRequest request) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/KensaMstController.cs:line 50
   at lambda_method19(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
The ConnectionString property has not been initialized.. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:14:46.9154490+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/OrdInf/GetHeaderInf?SinDate=********&RaiinNo=68&PtId=46 - 1455
2025-07-29T18:14:46.9173980+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.SmartKartePortController.GetPortList (EmrCloudApi)"'
2025-07-29T18:14:46.9177410+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetPortList\", controller = \"SmartKartePort\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.SmartKartePort.GetPortListResponse]] GetPortList(EmrCloudApi.Requests.SmartKartePort.GetPortListRequest)" on controller "EmrCloudApi.Controller.SmartKartePortController" ("EmrCloudApi").
2025-07-29T18:14:46.9187350+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/TodayOrd/GetInsuranceComboList?sinDate=********&ptId=46 - 790
2025-07-29T18:14:46.9209510+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:14:46.9212560+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.KensaMstController.GetInHospitalKensaMst (EmrCloudApi)" in 26.3405ms
2025-07-29T18:14:46.9213360+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.KensaMstController.GetInHospitalKensaMst (EmrCloudApi)"'
2025-07-29T18:14:46.9214740+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/KensaMst/GetInHospitalKensaMst" responded 400 in 27.6068 ms
2025-07-29T18:14:46.9220060+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.SmartKartePortController.GetPortList (EmrCloudApi). Interactor.SmartKartePort. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.SmartKartePortController.GetPortList(GetPortListRequest request) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/SmartKartePortController.cs:line 73
   at lambda_method22(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
The ConnectionString property has not been initialized.. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:14:46.9222370+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 POST http://host.docker.internal:5286/api/History/GetList application/json 193
2025-07-29T18:14:46.9247370+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/KensaMst/GetInHospitalKensaMst?isExceptVital=false - 518 - 400 - application/json;+charset=utf-8 50.2054ms
2025-07-29T18:14:46.9281810+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.OrdInfController.GetHeaderInf (EmrCloudApi)"'
2025-07-29T18:14:46.9284620+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetHeaderInf\", controller = \"OrdInf\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.OrdInfs.GetHeaderInfResponse]] GetHeaderInf(EmrCloudApi.Requests.OrdInfs.GetMaxRpNoRequest)" on controller "EmrCloudApi.Controller.OrdInfController" ("EmrCloudApi").
2025-07-29T18:14:46.9324020+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:14:46.9326780+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.SmartKartePortController.GetPortList (EmrCloudApi)" in 14.7332ms
2025-07-29T18:14:46.9327460+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.SmartKartePortController.GetPortList (EmrCloudApi)"'
2025-07-29T18:14:46.9328300+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/SmartKartePort/GetList" responded 400 in 15.4358 ms
2025-07-29T18:14:46.9328440+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "BadRequestObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:14:46.9333220+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.OrdInfController.GetHeaderInf (EmrCloudApi)" in 4.6865ms
2025-07-29T18:14:46.9333690+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.OrdInfController.GetHeaderInf (EmrCloudApi)"'
2025-07-29T18:14:46.9334200+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/OrdInf/GetHeaderInf" responded 400 in 5.2511 ms
2025-07-29T18:14:46.9350840+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.TodayOrdController.GetInsuranceComboList (EmrCloudApi)"'
2025-07-29T18:14:46.9350890+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.HistoryController.GetList (EmrCloudApi)"'
2025-07-29T18:14:46.9355590+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetInsuranceComboList\", controller = \"TodayOrd\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.InsuranceList.GetInsuranceComboListResponse]] GetInsuranceComboList(EmrCloudApi.Requests.Insurance.GetInsuranceComboListRequest)" on controller "EmrCloudApi.Controller.TodayOrdController" ("EmrCloudApi").
2025-07-29T18:14:46.9355850+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/SmartKartePort/GetList?onlyCommon=false - 523 - 400 - application/json;+charset=utf-8 43.0945ms
2025-07-29T18:14:46.9356390+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/OrdInf/GetHeaderInf?SinDate=********&RaiinNo=68&PtId=46 - 1455 - 400 - application/json;+charset=utf-8 20.2080ms
2025-07-29T18:14:46.9355740+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetList\", controller = \"History\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.MedicalExamination.GetMedicalExaminationHistoryResponse]] GetList(EmrCloudApi.Requests.MedicalExamination.GetMedicalExaminationHistoryRequest)" on controller "EmrCloudApi.Controller.HistoryController" ("EmrCloudApi").
2025-07-29T18:14:46.9381210+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"EmrCloudApi.Responses.Response`1[[EmrCloudApi.Responses.InsuranceList.GetInsuranceComboListResponse, EmrCloudApi, Version=*******, Culture=neutral, PublicKeyToken=null]]"'.
2025-07-29T18:14:46.9383030+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.TodayOrdController.GetInsuranceComboList (EmrCloudApi)" in 2.5888ms
2025-07-29T18:14:46.9383480+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.TodayOrdController.GetInsuranceComboList (EmrCloudApi)"'
2025-07-29T18:14:46.9383790+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/TodayOrd/GetInsuranceComboList" responded 200 in 3.3073 ms
2025-07-29T18:14:46.9399190+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/TodayOrd/GetInsuranceComboList?sinDate=********&ptId=46 - 790 - 200 - application/json;+charset=utf-8 21.1983ms
2025-07-29T18:14:46.9552860+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "BadRequestObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:14:46.9556530+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.HistoryController.GetList (EmrCloudApi)" in 19.8401ms
2025-07-29T18:14:46.9557730+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.HistoryController.GetList (EmrCloudApi)"'
2025-07-29T18:14:46.9558170+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "POST" "/api/History/GetList" responded 400 in 20.7288 ms
2025-07-29T18:14:46.9592460+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 POST http://host.docker.internal:5286/api/History/GetList application/json 193 - 400 - application/json;+charset=utf-8 36.9417ms
2025-07-29T18:14:47.0640020+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/SystemConf/GetList application/json 395
2025-07-29T18:14:47.0640500+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 POST http://host.docker.internal:5286/api/AuditLog/Save application/json 195
2025-07-29T18:14:47.0647460+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/User/GetAllPermission application/json 424
2025-07-29T18:14:47.0921060+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/KarteAllergy/GetKarteAllergyList?PtId=46 - 899
2025-07-29T18:14:47.1184370+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.AuditLogController.Save (EmrCloudApi)"'
2025-07-29T18:14:47.1188640+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"Save\", controller = \"AuditLog\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.AuditLog.SaveAuditLogResponse]] Save(EmrCloudApi.Requests.AuditLog.SaveAuditLogRequest)" on controller "EmrCloudApi.Controller.AuditLogController" ("EmrCloudApi").
2025-07-29T18:14:47.1201220+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.SystemConfController.GetSystemConfList (EmrCloudApi)"'
2025-07-29T18:14:47.1202680+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetSystemConfList\", controller = \"SystemConf\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.SystemConf.GetSystemConfListResponse]] GetSystemConfList()" on controller "EmrCloudApi.Controller.SystemConfController" ("EmrCloudApi").
2025-07-29T18:14:47.1230260+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.UserController.GetAllPermission (EmrCloudApi)"'
2025-07-29T18:14:47.1232230+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetAllPermission\", controller = \"User\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.User.GetAllPermissionResponse]] GetAllPermission()" on controller "EmrCloudApi.Controller.UserController" ("EmrCloudApi").
2025-07-29T18:14:47.1243320+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.AuditLogController.Save (EmrCloudApi). Interactor.AuditTrailLog. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.AuditLogController.Save(SaveAuditLogRequest req) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/AuditLogController.cs:line 35
   at lambda_method2357(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
The ConnectionString property has not been initialized.. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:14:47.1284380+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"EmrCloudApi.Responses.Response`1[[EmrCloudApi.Responses.SystemConf.GetSystemConfListResponse, EmrCloudApi, Version=*******, Culture=neutral, PublicKeyToken=null]]"'.
2025-07-29T18:14:47.1293490+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.SystemConfController.GetSystemConfList (EmrCloudApi)" in 9.0215ms
2025-07-29T18:14:47.1294290+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.SystemConfController.GetSystemConfList (EmrCloudApi)"'
2025-07-29T18:14:47.1294640+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/SystemConf/GetList" responded 200 in 9.3448 ms
2025-07-29T18:14:47.1368870+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/SystemConf/GetList application/json 395 - 200 - application/json;+charset=utf-8 72.8593ms
2025-07-29T18:14:47.1385530+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/KensaMst/GetInHospitalKensaMst?isExceptVital=false - 518
2025-07-29T18:14:47.1397300+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.UserController.GetAllPermission (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.UserController.GetAllPermission() in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/UserController.cs:line 152
   at lambda_method855(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:14:47.1501960+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:14:47.1506830+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.AuditLogController.Save (EmrCloudApi)" in 31.715ms
2025-07-29T18:14:47.1508520+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.AuditLogController.Save (EmrCloudApi)"'
2025-07-29T18:14:47.1513110+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:14:47.1513340+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "POST" "/api/AuditLog/Save" responded 400 in 32.9243 ms
2025-07-29T18:14:47.1514360+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.UserController.GetAllPermission (EmrCloudApi)" in 28.1571ms
2025-07-29T18:14:47.1517150+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.UserController.GetAllPermission (EmrCloudApi)"'
2025-07-29T18:14:47.1517460+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/User/GetAllPermission" responded 400 in 28.7317 ms
2025-07-29T18:14:47.1530260+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.KarteAllergyController.GetKarteAllergyList (EmrCloudApi)"'
2025-07-29T18:14:47.1532100+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetKarteAllergyList\", controller = \"KarteAllergy\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.KarteAllergy.GetKarteAllergyResponse]] GetKarteAllergyList(EmrCloudApi.Requests.KarteAllergy.KarteAllergyRequest)" on controller "EmrCloudApi.Controller.KarteAllergyController" ("EmrCloudApi").
2025-07-29T18:14:47.1555300+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/PatientInfor/GetBasicPatientInfo?ptId=46 application/json 2497
2025-07-29T18:14:47.1555680+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/CustomButtonConf/ListAllCustomButtonConf - 656
2025-07-29T18:14:47.1571380+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 POST http://host.docker.internal:5286/api/AuditLog/Save application/json 195 - 400 - application/json;+charset=utf-8 93.0199ms
2025-07-29T18:14:47.1577060+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/User/GetAllPermission application/json 424 - 400 - application/json;+charset=utf-8 92.9602ms
2025-07-29T18:14:47.1668880+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"EmrCloudApi.Responses.Response`1[[EmrCloudApi.Responses.KarteAllergy.GetKarteAllergyResponse, EmrCloudApi, Version=*******, Culture=neutral, PublicKeyToken=null]]"'.
2025-07-29T18:14:47.1672680+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.KarteAllergyController.GetKarteAllergyList (EmrCloudApi)" in 13.9897ms
2025-07-29T18:14:47.1674050+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.KarteAllergyController.GetKarteAllergyList (EmrCloudApi)"'
2025-07-29T18:14:47.1674790+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/KarteAllergy/GetKarteAllergyList" responded 200 in 14.4528 ms
2025-07-29T18:14:47.1682670+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/CustomButtonParamMst/ListCustomButtonParamMsts - 471
2025-07-29T18:14:47.1720780+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/KarteAllergy/GetKarteAllergyList?PtId=46 - 899 - 200 - application/json;+charset=utf-8 79.8495ms
2025-07-29T18:14:47.1738400+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.KensaMstController.GetInHospitalKensaMst (EmrCloudApi)"'
2025-07-29T18:14:47.1741930+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetInHospitalKensaMst\", controller = \"KensaMst\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.KensaMst.KensaMstListResponse]] GetInHospitalKensaMst(EmrCloudApi.Requests.KensaMst.GetKensaMstRequest)" on controller "EmrCloudApi.Controller.KensaMstController" ("EmrCloudApi").
2025-07-29T18:14:47.1766480+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/Reception/GetLastRaiinInfs?sinDate=********&isLastVisit=true&ptId=46 - 1179
2025-07-29T18:14:47.1775390+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.KensaMstController.GetInHospitalKensaMst (EmrCloudApi). Interactor.KensaMst. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.KensaMstController.GetInHospitalKensaMst(GetKensaMstRequest request) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/KensaMstController.cs:line 50
   at lambda_method19(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
The ConnectionString property has not been initialized.. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:14:47.1819380+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.CustomButtonConfController.ListAllCustomButtonConfList (EmrCloudApi)"'
2025-07-29T18:14:47.1824030+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"ListAllCustomButtonConfList\", controller = \"CustomButtonConf\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.CustomButtonConf.ListAllCustomButtonConfResponse]] ListAllCustomButtonConfList(Int32, Int64)" on controller "EmrCloudApi.Controller.CustomButtonConfController" ("EmrCloudApi").
2025-07-29T18:14:47.1859490+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.CustomButtonParamMstController.GetCustomButtonParamMstList (EmrCloudApi)"'
2025-07-29T18:14:47.1863220+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetCustomButtonParamMstList\", controller = \"CustomButtonParamMst\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.CustomButtonParamMst.GetCustomButtonParamMstResponse]] GetCustomButtonParamMstList()" on controller "EmrCloudApi.Controller.CustomButtonParamMstController" ("EmrCloudApi").
2025-07-29T18:14:47.1871080+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.CustomButtonConfController.ListAllCustomButtonConfList (EmrCloudApi). One or more errors occurred. (The ConnectionString property has not been initialized.). Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.CustomButtonConfController.ListAllCustomButtonConfList(Int32 ptId, Int64 raiinNo) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/CustomButtonConfController.cs:line 26
   at lambda_method2414(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
The ConnectionString property has not been initialized.. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:14:47.1909400+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"EmrCloudApi.Responses.Response`1[[EmrCloudApi.Responses.CustomButtonParamMst.GetCustomButtonParamMstResponse, EmrCloudApi, Version=*******, Culture=neutral, PublicKeyToken=null]]"'.
2025-07-29T18:14:47.1914920+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.CustomButtonParamMstController.GetCustomButtonParamMstList (EmrCloudApi)" in 5.0235ms
2025-07-29T18:14:47.1916840+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:14:47.1917400+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.CustomButtonParamMstController.GetCustomButtonParamMstList (EmrCloudApi)"'
2025-07-29T18:14:47.1917810+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/CustomButtonParamMst/ListCustomButtonParamMsts" responded 200 in 5.8430 ms
2025-07-29T18:14:47.1921030+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.KensaMstController.GetInHospitalKensaMst (EmrCloudApi)" in 17.7444ms
2025-07-29T18:14:47.1921600+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.KensaMstController.GetInHospitalKensaMst (EmrCloudApi)"'
2025-07-29T18:14:47.1922610+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/KensaMst/GetInHospitalKensaMst" responded 400 in 18.4316 ms
2025-07-29T18:14:47.1941960+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.ReceptionController.GetLastRaiinInfs (EmrCloudApi)"'
2025-07-29T18:14:47.1945280+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetLastRaiinInfs\", controller = \"Reception\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.Reception.GetLastRaiinInfsResponse]] GetLastRaiinInfs(EmrCloudApi.Requests.Reception.GetLastRaiinInfsRequest)" on controller "EmrCloudApi.Controller.ReceptionController" ("EmrCloudApi").
2025-07-29T18:14:47.1966030+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/CustomButtonParamMst/ListCustomButtonParamMsts - 471 - 200 - application/json;+charset=utf-8 28.3406ms
2025-07-29T18:14:47.1967120+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/KensaMst/GetInHospitalKensaMst?isExceptVital=false - 518 - 400 - application/json;+charset=utf-8 58.2051ms
2025-07-29T18:14:47.2018310+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.ReceptionController.GetLastRaiinInfs (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.ReceptionController.GetLastRaiinInfs(GetLastRaiinInfsRequest request) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/ReceptionController.cs:line 126
   at lambda_method2470(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:14:47.2036040+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/Reception/Get?raiinNo=68 application/json 2049
2025-07-29T18:14:47.2036750+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/SystemGenerationConf/GetList application/json 535
2025-07-29T18:14:47.2117680+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/Diseases/GetList?sinDate=********&ptId=46 - 1646
2025-07-29T18:14:47.2129150+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.PatientInforController.GetBasicPatientInfo (EmrCloudApi)"'
2025-07-29T18:14:47.2149680+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:14:47.2209280+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetBasicPatientInfo\", controller = \"PatientInfor\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.PatientInfor.GetBasicPatientInfoResponse]] GetBasicPatientInfo(EmrCloudApi.Requests.PatientInfor.BasicPatientInfo.GetBasicPatientInfoRequest)" on controller "EmrCloudApi.Controller.PatientInforController" ("EmrCloudApi").
2025-07-29T18:14:47.2212380+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.CustomButtonConfController.ListAllCustomButtonConfList (EmrCloudApi)" in 38.6227ms
2025-07-29T18:14:47.2220340+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.CustomButtonConfController.ListAllCustomButtonConfList (EmrCloudApi)"'
2025-07-29T18:14:47.2221750+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/CustomButtonConf/ListAllCustomButtonConf" responded 400 in 40.1944 ms
2025-07-29T18:14:47.2259260+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:14:47.2270450+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.ReceptionController.GetLastRaiinInfs (EmrCloudApi)" in 32.3719ms
2025-07-29T18:14:47.2272560+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.ReceptionController.GetLastRaiinInfs (EmrCloudApi)"'
2025-07-29T18:14:47.2273820+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/Reception/GetLastRaiinInfs" responded 400 in 33.1905 ms
2025-07-29T18:14:47.2289740+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.PatientInforController.GetBasicPatientInfo (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.PatientInforController.GetBasicPatientInfo(GetBasicPatientInfoRequest request) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/PatientInforController.cs:line 2106
   at lambda_method2360(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:14:47.2315730+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/CustomButtonConf/ListAllCustomButtonConf - 656 - 400 - application/json;+charset=utf-8 76.0680ms
2025-07-29T18:14:47.2336740+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/Reception/GetLastRaiinInfs?sinDate=********&isLastVisit=true&ptId=46 - 1179 - 400 - application/json;+charset=utf-8 57.0148ms
2025-07-29T18:14:47.2365820+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/UserConf/GetListForModel application/json 433
2025-07-29T18:14:47.2401800+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/Diseases/GetList?sinDate=********&ptId=46&requestFrom=0 - 1643
2025-07-29T18:14:47.2420900+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.SystemGenerationConfController.GetList (EmrCloudApi)"'
2025-07-29T18:14:47.2422190+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:14:47.2424000+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetList\", controller = \"SystemGenerationConf\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.SystemGenerationConf.GetSystemGenerationConfListResponse]] GetList()" on controller "EmrCloudApi.Controller.SystemGenerationConfController" ("EmrCloudApi").
2025-07-29T18:14:47.2424540+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.PatientInforController.GetBasicPatientInfo (EmrCloudApi)" in 20.212ms
2025-07-29T18:14:47.2427050+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.PatientInforController.GetBasicPatientInfo (EmrCloudApi)"'
2025-07-29T18:14:47.2427620+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/PatientInfor/GetBasicPatientInfo" responded 400 in 29.8581 ms
2025-07-29T18:14:47.2434190+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.ReceptionController.Get (EmrCloudApi)"'
2025-07-29T18:14:47.2438900+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"Get\", controller = \"Reception\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.Reception.GetReceptionResponse]] Get(EmrCloudApi.Requests.Reception.GetReceptionRequest)" on controller "EmrCloudApi.Controller.ReceptionController" ("EmrCloudApi").
2025-07-29T18:14:47.2455800+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.DiseasesController.GetDiseaseListMedicalExamination (EmrCloudApi)"'
2025-07-29T18:14:47.2458230+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetDiseaseListMedicalExamination\", controller = \"Diseases\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.Diseases.GetPtDiseaseListResponse]] GetDiseaseListMedicalExamination(EmrCloudApi.Requests.Diseases.GetPtDiseaseListRequest)" on controller "EmrCloudApi.Controller.DiseasesController" ("EmrCloudApi").
2025-07-29T18:14:47.2463640+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"EmrCloudApi.Responses.Response`1[[EmrCloudApi.Responses.SystemGenerationConf.GetSystemGenerationConfListResponse, EmrCloudApi, Version=*******, Culture=neutral, PublicKeyToken=null]]"'.
2025-07-29T18:14:47.2464690+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/PatientInfor/GetBasicPatientInfo?ptId=46 application/json 2497 - 400 - application/json;+charset=utf-8 90.9611ms
2025-07-29T18:14:47.2468830+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.SystemGenerationConfController.GetList (EmrCloudApi)" in 4.3481ms
2025-07-29T18:14:47.2476580+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.SystemGenerationConfController.GetList (EmrCloudApi)"'
2025-07-29T18:14:47.2477060+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/SystemGenerationConf/GetList" responded 200 in 5.6274 ms
2025-07-29T18:14:47.2493840+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.ReceptionController.Get (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.ReceptionController.Get(GetReceptionRequest request) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/ReceptionController.cs:line 107
   at lambda_method2522(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:14:47.2498030+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.DiseasesController.GetDiseaseListMedicalExamination (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.DiseasesController.GetDiseaseListMedicalExamination(GetPtDiseaseListRequest request) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/DiseasesController.cs:line 39
   at lambda_method2573(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:14:47.2605730+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/SystemGenerationConf/GetList application/json 535 - 200 - application/json;+charset=utf-8 56.8314ms
2025-07-29T18:14:47.2630360+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/Reception/GetLastRaiinInfs?sinDate=********&isLastVisit=true&ptId=46 - 1076
2025-07-29T18:14:47.2634360+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:14:47.2639720+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.ReceptionController.Get (EmrCloudApi)" in 20.0055ms
2025-07-29T18:14:47.2640930+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.ReceptionController.Get (EmrCloudApi)"'
2025-07-29T18:14:47.2642200+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/Reception/Get" responded 400 in 20.8054 ms
2025-07-29T18:14:47.2652080+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.DiseasesController.GetDiseaseListMedicalExamination (EmrCloudApi)"'
2025-07-29T18:14:47.2652430+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.UserConfController.GetListForModel (EmrCloudApi)"'
2025-07-29T18:14:47.2652110+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:14:47.2654540+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetDiseaseListMedicalExamination\", controller = \"Diseases\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.Diseases.GetPtDiseaseListResponse]] GetDiseaseListMedicalExamination(EmrCloudApi.Requests.Diseases.GetPtDiseaseListRequest)" on controller "EmrCloudApi.Controller.DiseasesController" ("EmrCloudApi").
2025-07-29T18:14:47.2654540+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetListForModel\", controller = \"UserConf\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.UserConf.GetUserConfModelListResponse]] GetListForModel()" on controller "EmrCloudApi.Controller.UserConfController" ("EmrCloudApi").
2025-07-29T18:14:47.2655800+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.DiseasesController.GetDiseaseListMedicalExamination (EmrCloudApi)" in 19.0088ms
2025-07-29T18:14:47.2657560+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.DiseasesController.GetDiseaseListMedicalExamination (EmrCloudApi)"'
2025-07-29T18:14:47.2658560+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/Diseases/GetList" responded 400 in 20.2900 ms
2025-07-29T18:14:47.2668050+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/Reception/Get?raiinNo=68 application/json 2049 - 400 - application/json;+charset=utf-8 63.2088ms
2025-07-29T18:14:47.2673270+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/Diseases/GetList?sinDate=********&hokenId=0&ptId=46&requestFrom=2 - 1640
2025-07-29T18:14:47.2678390+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.DiseasesController.GetDiseaseListMedicalExamination (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.DiseasesController.GetDiseaseListMedicalExamination(GetPtDiseaseListRequest request) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/DiseasesController.cs:line 39
   at lambda_method2573(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:14:47.2685180+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/Diseases/GetList?sinDate=********&ptId=46 - 1646 - 400 - application/json;+charset=utf-8 56.7892ms
2025-07-29T18:14:47.2687500+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"EmrCloudApi.Responses.Response`1[[EmrCloudApi.Responses.UserConf.GetUserConfModelListResponse, EmrCloudApi, Version=*******, Culture=neutral, PublicKeyToken=null]]"'.
2025-07-29T18:14:47.2690350+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.UserConfController.GetListForModel (EmrCloudApi)" in 3.4309ms
2025-07-29T18:14:47.2691580+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.UserConfController.GetListForModel (EmrCloudApi)"'
2025-07-29T18:14:47.2693300+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/UserConf/GetListForModel" responded 200 in 4.0851 ms
2025-07-29T18:14:47.2716630+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/UserConf/GetListForModel application/json 433 - 200 - application/json;+charset=utf-8 35.0781ms
2025-07-29T18:14:47.2739180+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:14:47.2743600+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.DiseasesController.GetDiseaseListMedicalExamination (EmrCloudApi)" in 8.6598ms
2025-07-29T18:14:47.2744820+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.DiseasesController.GetDiseaseListMedicalExamination (EmrCloudApi)"'
2025-07-29T18:14:47.2745630+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/Diseases/GetList" responded 400 in 9.3558 ms
2025-07-29T18:14:47.2759070+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.ReceptionController.GetLastRaiinInfs (EmrCloudApi)"'
2025-07-29T18:14:47.2762000+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetLastRaiinInfs\", controller = \"Reception\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.Reception.GetLastRaiinInfsResponse]] GetLastRaiinInfs(EmrCloudApi.Requests.Reception.GetLastRaiinInfsRequest)" on controller "EmrCloudApi.Controller.ReceptionController" ("EmrCloudApi").
2025-07-29T18:14:47.2763750+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/Diseases/GetList?sinDate=********&ptId=46&requestFrom=0 - 1643 - 400 - application/json;+charset=utf-8 36.2103ms
2025-07-29T18:14:47.2783880+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.DiseasesController.GetDiseaseListMedicalExamination (EmrCloudApi)"'
2025-07-29T18:14:47.2788150+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetDiseaseListMedicalExamination\", controller = \"Diseases\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.Diseases.GetPtDiseaseListResponse]] GetDiseaseListMedicalExamination(EmrCloudApi.Requests.Diseases.GetPtDiseaseListRequest)" on controller "EmrCloudApi.Controller.DiseasesController" ("EmrCloudApi").
2025-07-29T18:14:47.2798980+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.ReceptionController.GetLastRaiinInfs (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.ReceptionController.GetLastRaiinInfs(GetLastRaiinInfsRequest request) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/ReceptionController.cs:line 126
   at lambda_method2470(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:14:47.2823760+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.DiseasesController.GetDiseaseListMedicalExamination (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.DiseasesController.GetDiseaseListMedicalExamination(GetPtDiseaseListRequest request) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/DiseasesController.cs:line 39
   at lambda_method2573(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:14:47.2852620+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:14:47.2854770+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.ReceptionController.GetLastRaiinInfs (EmrCloudApi)" in 9.0667ms
2025-07-29T18:14:47.2856330+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.ReceptionController.GetLastRaiinInfs (EmrCloudApi)"'
2025-07-29T18:14:47.2857130+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/Reception/GetLastRaiinInfs" responded 400 in 9.8070 ms
2025-07-29T18:14:47.2876620+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:14:47.2880980+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.DiseasesController.GetDiseaseListMedicalExamination (EmrCloudApi)" in 9.0707ms
2025-07-29T18:14:47.2882240+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.DiseasesController.GetDiseaseListMedicalExamination (EmrCloudApi)"'
2025-07-29T18:14:47.2882870+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/Diseases/GetList" responded 400 in 9.9224 ms
2025-07-29T18:14:47.2887350+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/Reception/GetLastRaiinInfs?sinDate=********&isLastVisit=true&ptId=46 - 1076 - 400 - application/json;+charset=utf-8 25.6944ms
2025-07-29T18:14:47.2907460+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/Diseases/GetList?sinDate=********&hokenId=0&ptId=46&requestFrom=2 - 1640 - 400 - application/json;+charset=utf-8 23.3840ms
2025-07-29T18:14:47.5088050+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/NextOrder/GetNextOrderNextCheck?ptId=46 - 470
2025-07-29T18:14:47.5194680+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.NextOrderController.GetNextOrderNextCheck (EmrCloudApi)"'
2025-07-29T18:14:47.5196980+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetNextOrderNextCheck\", controller = \"NextOrder\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.NextOrder.GetNextOrderNextCheckResponse]] GetNextOrderNextCheck(EmrCloudApi.Requests.NextOrder.GetNextOrderNextCheckRequest)" on controller "EmrCloudApi.Controller.NextOrderController" ("EmrCloudApi").
2025-07-29T18:14:47.5217610+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"EmrCloudApi.Responses.Response`1[[EmrCloudApi.Responses.NextOrder.GetNextOrderNextCheckResponse, EmrCloudApi, Version=*******, Culture=neutral, PublicKeyToken=null]]"'.
2025-07-29T18:14:47.5219610+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.NextOrderController.GetNextOrderNextCheck (EmrCloudApi)" in 2.2029ms
2025-07-29T18:14:47.5221030+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.NextOrderController.GetNextOrderNextCheck (EmrCloudApi)"'
2025-07-29T18:14:47.5223870+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/NextOrder/GetNextOrderNextCheck" responded 200 in 2.9366 ms
2025-07-29T18:14:47.5238430+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/NextOrder/GetNextOrderNextCheck?ptId=46 - 470 - 200 - application/json;+charset=utf-8 15.0360ms
2025-07-29T18:14:49.5510450+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 POST http://host.docker.internal:5286/api/Lock/RemoveLockMedicalByTab application/json 145
2025-07-29T18:14:49.5709580+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.LockController.RemoveLockMedicalByTab (EmrCloudApi)"'
2025-07-29T18:14:49.5740480+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"RemoveLockMedicalByTab\", controller = \"Lock\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.Lock.UpdateVisitingLockResponse]] RemoveLockMedicalByTab(EmrCloudApi.Requests.Lock.RemoveLockMedicalByTabRequest)" on controller "EmrCloudApi.Controller.LockController" ("EmrCloudApi").
2025-07-29T18:14:49.5985580+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.LockController.RemoveLockMedicalByTab (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.LockController.RemoveLockMedicalByTab(RemoveLockMedicalByTabRequest request) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/LockController.cs:line 384
   at lambda_method2881(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:14:49.6040280+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:14:49.6042590+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.LockController.RemoveLockMedicalByTab (EmrCloudApi)" in 29.8034ms
2025-07-29T18:14:49.6043260+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.LockController.RemoveLockMedicalByTab (EmrCloudApi)"'
2025-07-29T18:14:49.6043640+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "POST" "/api/Lock/RemoveLockMedicalByTab" responded 400 in 33.4517 ms
2025-07-29T18:14:49.6054220+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 POST http://host.docker.internal:5286/api/Lock/RemoveLockMedicalByTab application/json 145 - 400 - application/json;+charset=utf-8 54.3917ms
2025-07-29T18:16:55.4608620+09:00 [INF] (Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager) User profile is available. Using '"/Users/<USER>/.aspnet/DataProtection-Keys"' as key repository; keys will not be encrypted at rest.
2025-07-29T18:16:55.4919830+09:00 [INF] (Microsoft.AspNetCore.SignalR.StackExchangeRedis.RedisHubLifetimeManager) Connected to Redis.
2025-07-29T18:16:55.5359450+09:00 [INF] (Microsoft.Hosting.Lifetime) Now listening on: "http://localhost:5286"
2025-07-29T18:17:03.9309240+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 POST http://host.docker.internal:5286/api/Lock/RemoveLockMedicalByTab application/json 145
2025-07-29T18:17:03.9672260+09:00 [WRN] (Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware) Failed to determine the https port for redirect.
2025-07-29T18:17:04.1370950+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.LockController.RemoveLockMedicalByTab (EmrCloudApi)"'
2025-07-29T18:17:04.1554300+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"RemoveLockMedicalByTab\", controller = \"Lock\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.Lock.UpdateVisitingLockResponse]] RemoveLockMedicalByTab(EmrCloudApi.Requests.Lock.RemoveLockMedicalByTabRequest)" on controller "EmrCloudApi.Controller.LockController" ("EmrCloudApi").
2025-07-29T18:17:05.1157410+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/LabelMst/GetList application/json 346
2025-07-29T18:17:05.1161400+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/JsonSetting/Get?key=RECEPTION_FILTER_STATE application/json 379
2025-07-29T18:17:05.1751170+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/User/GetList?SinDate=********&IsDoctorOnly=true - 1387
2025-07-29T18:17:05.1866190+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)"'
2025-07-29T18:17:05.1929830+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"Get\", controller = \"JsonSetting\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.JsonSetting.GetJsonSettingResponse]] Get(EmrCloudApi.Requests.JsonSetting.GetJsonSettingRequest)" on controller "EmrCloudApi.Controller.JsonSettingController" ("EmrCloudApi").
2025-07-29T18:17:05.1964440+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)"'
2025-07-29T18:17:05.1964740+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)"'
2025-07-29T18:17:05.1986290+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetList\", controller = \"LabelMst\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.LabelMst.GetListLabelMstResponse]] GetList()" on controller "EmrCloudApi.Controller.LabelMstController" ("EmrCloudApi").
2025-07-29T18:17:05.2002400+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetList\", controller = \"User\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.User.GetUserListResponse]] GetList(EmrCloudApi.Requests.User.GetUserListRequest)" on controller "EmrCloudApi.Controller.UserController" ("EmrCloudApi").
2025-07-29T18:17:10.3545030+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.LockController.RemoveLockMedicalByTab (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.LockController.RemoveLockMedicalByTab(RemoveLockMedicalByTabRequest request) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/LockController.cs:line 384
   at lambda_method7(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:17:10.4062330+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:17:10.4159850+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.LockController.RemoveLockMedicalByTab (EmrCloudApi)" in 6258.4205ms
2025-07-29T18:17:10.4167770+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.LockController.RemoveLockMedicalByTab (EmrCloudApi)"'
2025-07-29T18:17:10.4173440+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "POST" "/api/Lock/RemoveLockMedicalByTab" responded 400 in 6281.5499 ms
2025-07-29T18:17:10.4263390+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 POST http://host.docker.internal:5286/api/Lock/RemoveLockMedicalByTab application/json 145 - 400 - application/json;+charset=utf-8 6496.5709ms
2025-07-29T18:17:11.0773510+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/JsonSetting/Get?key=RECEPTION_FILTER_STATE application/json 379
2025-07-29T18:17:11.0782790+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/LabelMst/GetList application/json 346
2025-07-29T18:17:11.0856510+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/User/GetList?SinDate=********&IsDoctorOnly=true - 1387
2025-07-29T18:17:11.1146780+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)"'
2025-07-29T18:17:11.1173170+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetList\", controller = \"LabelMst\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.LabelMst.GetListLabelMstResponse]] GetList()" on controller "EmrCloudApi.Controller.LabelMstController" ("EmrCloudApi").
2025-07-29T18:17:11.1202680+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)"'
2025-07-29T18:17:11.1212090+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"Get\", controller = \"JsonSetting\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.JsonSetting.GetJsonSettingResponse]] Get(EmrCloudApi.Requests.JsonSetting.GetJsonSettingRequest)" on controller "EmrCloudApi.Controller.JsonSettingController" ("EmrCloudApi").
2025-07-29T18:17:11.1337260+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)"'
2025-07-29T18:17:11.1344380+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetList\", controller = \"User\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.User.GetUserListResponse]] GetList(EmrCloudApi.Requests.User.GetUserListRequest)" on controller "EmrCloudApi.Controller.UserController" ("EmrCloudApi").
2025-07-29T18:17:14.8921490+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.JsonSettingController.Get(GetJsonSettingRequest req) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/JsonSettingController.cs:line 31
   at lambda_method468(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:17:14.8921490+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.JsonSettingController.Get(GetJsonSettingRequest req) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/JsonSettingController.cs:line 31
   at lambda_method468(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:17:14.8977950+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.LabelMstController.GetList() in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/LabelMstController.cs:line 27
   at lambda_method470(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:17:14.8978130+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.LabelMstController.GetList() in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/LabelMstController.cs:line 27
   at lambda_method470(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:17:14.8980580+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.UserController.GetList (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.UserController.GetList(GetUserListRequest req) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/UserController.cs:line 59
   at lambda_method471(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:17:14.8981220+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.UserController.GetList (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.UserController.GetList(GetUserListRequest req) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/UserController.cs:line 59
   at lambda_method471(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:17:14.8995670+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:17:14.8996530+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:17:14.9001600+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)" in 9706.8151ms
2025-07-29T18:17:14.9002140+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)" in 3778.061ms
2025-07-29T18:17:14.9003440+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)"'
2025-07-29T18:17:14.9002500+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)"'
2025-07-29T18:17:14.9005260+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/JsonSetting/Get" responded 400 in 3780.1528 ms
2025-07-29T18:17:14.9005260+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/JsonSetting/Get" responded 400 in 9713.8660 ms
2025-07-29T18:17:14.9029670+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/JsonSetting/Get?key=RECEPTION_FILTER_STATE application/json 379 - 400 - application/json;+charset=utf-8 3825.5585ms
2025-07-29T18:17:14.9029900+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/JsonSetting/Get?key=RECEPTION_FILTER_STATE application/json 379 - 400 - application/json;+charset=utf-8 9787.2627ms
2025-07-29T18:17:14.9036190+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:17:14.9036780+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:17:14.9040650+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:17:14.9041250+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:17:14.9042510+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)" in 9705.3106ms
2025-07-29T18:17:14.9042730+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)" in 3785.9003ms
2025-07-29T18:17:14.9043220+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)" in 9703.5068ms
2025-07-29T18:17:14.9043390+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)"'
2025-07-29T18:17:14.9043490+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)" in 3769.4809ms
2025-07-29T18:17:14.9045140+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)"'
2025-07-29T18:17:14.9045450+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/LabelMst/GetList" responded 400 in 9708.0526 ms
2025-07-29T18:17:14.9045680+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)"'
2025-07-29T18:17:14.9046170+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/User/GetList" responded 400 in 9708.1290 ms
2025-07-29T18:17:14.9050050+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)"'
2025-07-29T18:17:14.9050320+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/LabelMst/GetList" responded 400 in 3790.3390 ms
2025-07-29T18:17:14.9053600+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/User/GetList" responded 400 in 3771.6524 ms
2025-07-29T18:17:14.9074970+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/User/GetList?SinDate=********&IsDoctorOnly=true - 1387 - 400 - application/json;+charset=utf-8 9732.3450ms
2025-07-29T18:17:14.9075250+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/LabelMst/GetList application/json 346 - 400 - application/json;+charset=utf-8 9791.7996ms
2025-07-29T18:17:14.9075550+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/User/GetList?SinDate=********&IsDoctorOnly=true - 1387 - 400 - application/json;+charset=utf-8 3822.2854ms
2025-07-29T18:17:14.9075850+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/LabelMst/GetList application/json 346 - 400 - application/json;+charset=utf-8 3830.2086ms
2025-07-29T18:17:26.5957310+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/JsonSetting/Get?key=RECEPTION_FILTER_STATE application/json 379
2025-07-29T18:17:26.5971350+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/LabelMst/GetList application/json 346
2025-07-29T18:17:26.5992340+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/User/GetList?SinDate=********&IsDoctorOnly=true - 1387
2025-07-29T18:17:26.6157880+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)"'
2025-07-29T18:17:26.6164370+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetList\", controller = \"LabelMst\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.LabelMst.GetListLabelMstResponse]] GetList()" on controller "EmrCloudApi.Controller.LabelMstController" ("EmrCloudApi").
2025-07-29T18:17:26.6282050+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)"'
2025-07-29T18:17:26.6282060+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)"'
2025-07-29T18:17:26.6285140+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"Get\", controller = \"JsonSetting\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.JsonSetting.GetJsonSettingResponse]] Get(EmrCloudApi.Requests.JsonSetting.GetJsonSettingRequest)" on controller "EmrCloudApi.Controller.JsonSettingController" ("EmrCloudApi").
2025-07-29T18:17:26.6285200+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetList\", controller = \"User\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.User.GetUserListResponse]] GetList(EmrCloudApi.Requests.User.GetUserListRequest)" on controller "EmrCloudApi.Controller.UserController" ("EmrCloudApi").
2025-07-29T18:17:26.6454560+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.JsonSettingController.Get(GetJsonSettingRequest req) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/JsonSettingController.cs:line 31
   at lambda_method468(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:17:26.6454800+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.UserController.GetList (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.UserController.GetList(GetUserListRequest req) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/UserController.cs:line 59
   at lambda_method471(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:17:26.6454740+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.LabelMstController.GetList() in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/LabelMstController.cs:line 27
   at lambda_method470(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:17:26.6827530+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:17:26.6828510+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:17:26.6830100+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)" in 66.4637ms
2025-07-29T18:17:26.6830720+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)"'
2025-07-29T18:17:26.6831090+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/LabelMst/GetList" responded 400 in 67.4037 ms
2025-07-29T18:17:26.6831100+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)" in 54.4677ms
2025-07-29T18:17:26.6832090+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)"'
2025-07-29T18:17:26.6834310+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/User/GetList" responded 400 in 55.1261 ms
2025-07-29T18:17:26.7119430+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:17:26.7126390+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)" in 84.0535ms
2025-07-29T18:17:26.7128350+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)"'
2025-07-29T18:17:26.7129390+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/JsonSetting/Get" responded 400 in 84.7408 ms
2025-07-29T18:17:26.7135280+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/LabelMst/GetList application/json 346 - 400 - application/json;+charset=utf-8 116.3911ms
2025-07-29T18:17:26.7135280+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/User/GetList?SinDate=********&IsDoctorOnly=true - 1387 - 400 - application/json;+charset=utf-8 114.2986ms
2025-07-29T18:17:26.7145070+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/JsonSetting/Get?key=RECEPTION_FILTER_STATE application/json 379 - 400 - application/json;+charset=utf-8 118.8480ms
2025-07-29T18:17:43.8793590+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/JsonSetting/Get?key=RECEPTION_FILTER_STATE application/json 379
2025-07-29T18:17:43.8995240+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/LabelMst/GetList application/json 346
2025-07-29T18:17:43.9088730+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/User/GetList?SinDate=********&IsDoctorOnly=true - 1387
2025-07-29T18:17:43.9670010+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)"'
2025-07-29T18:17:43.9673690+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"Get\", controller = \"JsonSetting\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.JsonSetting.GetJsonSettingResponse]] Get(EmrCloudApi.Requests.JsonSetting.GetJsonSettingRequest)" on controller "EmrCloudApi.Controller.JsonSettingController" ("EmrCloudApi").
2025-07-29T18:17:43.9699550+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)"'
2025-07-29T18:17:43.9705990+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetList\", controller = \"User\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.User.GetUserListResponse]] GetList(EmrCloudApi.Requests.User.GetUserListRequest)" on controller "EmrCloudApi.Controller.UserController" ("EmrCloudApi").
2025-07-29T18:17:43.9718530+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.JsonSettingController.Get(GetJsonSettingRequest req) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/JsonSettingController.cs:line 31
   at lambda_method468(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:17:43.9719980+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)"'
2025-07-29T18:17:43.9730620+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetList\", controller = \"LabelMst\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.LabelMst.GetListLabelMstResponse]] GetList()" on controller "EmrCloudApi.Controller.LabelMstController" ("EmrCloudApi").
2025-07-29T18:17:43.9763620+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.LabelMstController.GetList() in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/LabelMstController.cs:line 27
   at lambda_method470(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:17:43.9764110+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.UserController.GetList (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.UserController.GetList(GetUserListRequest req) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/UserController.cs:line 59
   at lambda_method471(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:17:44.0072020+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:17:44.0110880+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)" in 43.5597ms
2025-07-29T18:17:44.0113970+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)"'
2025-07-29T18:17:44.0114670+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/JsonSetting/Get" responded 400 in 44.4588 ms
2025-07-29T18:17:44.0152100+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:17:44.0153280+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:17:44.0156940+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)" in 44.9178ms
2025-07-29T18:17:44.0157720+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)" in 42.6079ms
2025-07-29T18:17:44.0158590+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)"'
2025-07-29T18:17:44.0158740+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)"'
2025-07-29T18:17:44.0159190+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/User/GetList" responded 400 in 45.9802 ms
2025-07-29T18:17:44.0159560+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/LabelMst/GetList" responded 400 in 43.9771 ms
2025-07-29T18:17:44.0269150+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/JsonSetting/Get?key=RECEPTION_FILTER_STATE application/json 379 - 400 - application/json;+charset=utf-8 147.5564ms
2025-07-29T18:17:44.0279890+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/LabelMst/GetList application/json 346 - 400 - application/json;+charset=utf-8 128.4968ms
2025-07-29T18:17:44.0279890+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/User/GetList?SinDate=********&IsDoctorOnly=true - 1387 - 400 - application/json;+charset=utf-8 119.1237ms
2025-07-29T18:18:00.3196980+09:00 [INF] (Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager) User profile is available. Using '"/Users/<USER>/.aspnet/DataProtection-Keys"' as key repository; keys will not be encrypted at rest.
2025-07-29T18:18:00.3282370+09:00 [INF] (Microsoft.AspNetCore.SignalR.StackExchangeRedis.RedisHubLifetimeManager) Connected to Redis.
2025-07-29T18:18:00.3663460+09:00 [INF] (Microsoft.Hosting.Lifetime) Now listening on: "http://localhost:5286"
2025-07-29T18:18:08.8394040+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/User/GetList?SinDate=********&IsDoctorOnly=true - 1387
2025-07-29T18:18:08.8464360+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/JsonSetting/Get?key=RECEPTION_FILTER_STATE application/json 379
2025-07-29T18:18:08.8535630+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/LabelMst/GetList application/json 346
2025-07-29T18:18:08.9167010+09:00 [WRN] (Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware) Failed to determine the https port for redirect.
2025-07-29T18:18:09.0651450+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)"'
2025-07-29T18:18:09.0651440+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)"'
2025-07-29T18:18:09.0651430+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)"'
2025-07-29T18:18:09.0787500+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetList\", controller = \"LabelMst\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.LabelMst.GetListLabelMstResponse]] GetList()" on controller "EmrCloudApi.Controller.LabelMstController" ("EmrCloudApi").
2025-07-29T18:18:09.0809780+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"Get\", controller = \"JsonSetting\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.JsonSetting.GetJsonSettingResponse]] Get(EmrCloudApi.Requests.JsonSetting.GetJsonSettingRequest)" on controller "EmrCloudApi.Controller.JsonSettingController" ("EmrCloudApi").
2025-07-29T18:18:09.0813330+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetList\", controller = \"User\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.User.GetUserListResponse]] GetList(EmrCloudApi.Requests.User.GetUserListRequest)" on controller "EmrCloudApi.Controller.UserController" ("EmrCloudApi").
2025-07-29T18:18:14.4223520+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.LabelMstController.GetList() in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/LabelMstController.cs:line 27
   at lambda_method13(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:18:14.4223500+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.JsonSettingController.Get(GetJsonSettingRequest req) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/JsonSettingController.cs:line 31
   at lambda_method14(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:18:14.4223530+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.UserController.GetList (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.UserController.GetList(GetUserListRequest req) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/UserController.cs:line 59
   at lambda_method15(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:18:14.4695660+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:18:14.4695660+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:18:14.4695650+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:18:14.4781610+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)" in 5395.2766ms
2025-07-29T18:18:14.4781630+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)" in 5394.9197ms
2025-07-29T18:18:14.4781620+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)" in 5397.4132ms
2025-07-29T18:18:14.4788700+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)"'
2025-07-29T18:18:14.4788730+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)"'
2025-07-29T18:18:14.4788720+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)"'
2025-07-29T18:18:14.4794330+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/LabelMst/GetList" responded 400 in 5415.1048 ms
2025-07-29T18:18:14.4794320+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/User/GetList" responded 400 in 5415.0768 ms
2025-07-29T18:18:14.4794320+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/JsonSetting/Get" responded 400 in 5415.0623 ms
2025-07-29T18:18:14.4874370+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/LabelMst/GetList application/json 346 - 400 - application/json;+charset=utf-8 5672.0225ms
2025-07-29T18:18:14.4874380+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/User/GetList?SinDate=********&IsDoctorOnly=true - 1387 - 400 - application/json;+charset=utf-8 5672.0150ms
2025-07-29T18:18:14.4874360+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/JsonSetting/Get?key=RECEPTION_FILTER_STATE application/json 379 - 400 - application/json;+charset=utf-8 5672.0387ms
2025-07-29T18:18:20.9619300+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/JsonSetting/Get?key=RECEPTION_FILTER_STATE application/json 379
2025-07-29T18:18:20.9619280+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/LabelMst/GetList application/json 346
2025-07-29T18:18:20.9655840+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/User/GetList?SinDate=********&IsDoctorOnly=true - 1387
2025-07-29T18:18:21.0081220+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)"'
2025-07-29T18:18:21.0081510+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)"'
2025-07-29T18:18:21.0081510+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)"'
2025-07-29T18:18:21.0089720+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetList\", controller = \"LabelMst\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.LabelMst.GetListLabelMstResponse]] GetList()" on controller "EmrCloudApi.Controller.LabelMstController" ("EmrCloudApi").
2025-07-29T18:18:21.0089680+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetList\", controller = \"User\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.User.GetUserListResponse]] GetList(EmrCloudApi.Requests.User.GetUserListRequest)" on controller "EmrCloudApi.Controller.UserController" ("EmrCloudApi").
2025-07-29T18:18:21.0089680+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"Get\", controller = \"JsonSetting\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.JsonSetting.GetJsonSettingResponse]] Get(EmrCloudApi.Requests.JsonSetting.GetJsonSettingRequest)" on controller "EmrCloudApi.Controller.JsonSettingController" ("EmrCloudApi").
2025-07-29T18:18:21.0443080+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.JsonSettingController.Get(GetJsonSettingRequest req) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/JsonSettingController.cs:line 31
   at lambda_method14(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:18:21.0443400+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.UserController.GetList (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.UserController.GetList(GetUserListRequest req) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/UserController.cs:line 59
   at lambda_method15(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:18:21.0444800+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.LabelMstController.GetList() in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/LabelMstController.cs:line 27
   at lambda_method13(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:18:21.0501140+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:18:21.0502550+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:18:21.0504150+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:18:21.0507260+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)" in 41.6059ms
2025-07-29T18:18:21.0508160+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)" in 41.542ms
2025-07-29T18:18:21.0508230+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)"'
2025-07-29T18:18:21.0508730+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)"'
2025-07-29T18:18:21.0509690+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/LabelMst/GetList" responded 400 in 42.8301 ms
2025-07-29T18:18:21.0508580+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)" in 41.66ms
2025-07-29T18:18:21.0510500+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/User/GetList" responded 400 in 42.9507 ms
2025-07-29T18:18:21.0511630+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)"'
2025-07-29T18:18:21.0518370+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/JsonSetting/Get" responded 400 in 43.4280 ms
2025-07-29T18:18:21.0543320+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/JsonSetting/Get?key=RECEPTION_FILTER_STATE application/json 379 - 400 - application/json;+charset=utf-8 92.4095ms
2025-07-29T18:18:21.0543320+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/LabelMst/GetList application/json 346 - 400 - application/json;+charset=utf-8 92.4119ms
2025-07-29T18:18:21.0543320+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/User/GetList?SinDate=********&IsDoctorOnly=true - 1387 - 400 - application/json;+charset=utf-8 88.7545ms
2025-07-29T18:48:53.0892640+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/JsonSetting/Get?key=RECEPTION_FILTER_STATE application/json 379
2025-07-29T18:48:53.0892640+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/User/GetList?SinDate=********&IsDoctorOnly=true - 1387
2025-07-29T18:48:53.0892640+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/LabelMst/GetList application/json 346
2025-07-29T18:48:53.2494910+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)"'
2025-07-29T18:48:53.2504020+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetList\", controller = \"User\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.User.GetUserListResponse]] GetList(EmrCloudApi.Requests.User.GetUserListRequest)" on controller "EmrCloudApi.Controller.UserController" ("EmrCloudApi").
2025-07-29T18:48:53.2630680+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.UserController.GetList (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.UserController.GetList(GetUserListRequest req) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/UserController.cs:line 59
   at lambda_method15(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:48:53.2692820+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:48:53.2695640+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)" in 18.9377ms
2025-07-29T18:48:53.2696280+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)"'
2025-07-29T18:48:53.2696790+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/User/GetList" responded 400 in 20.2527 ms
2025-07-29T18:48:53.2712210+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/User/GetList?SinDate=********&IsDoctorOnly=true - 1387 - 400 - application/json;+charset=utf-8 182.2860ms
2025-07-29T18:48:53.4059030+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/JsonSetting/Get?key=RECEPTION_FILTER_STATE application/json 379 - 401 - application/json;+charset=utf-8 316.9450ms
2025-07-29T18:48:53.4059310+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/LabelMst/GetList application/json 346 - 401 - application/json;+charset=utf-8 316.9882ms
2025-07-29T18:48:56.0211240+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 POST http://host.docker.internal:5286/api/Lock/RemoveAllLockMedicalTab application/json 51
2025-07-29T18:48:56.0459630+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.LockController.RemoveAllLockMedicalTab (EmrCloudApi)"'
2025-07-29T18:48:56.0545680+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"RemoveAllLockMedicalTab\", controller = \"Lock\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.Lock.UpdateVisitingLockResponse]] RemoveAllLockMedicalTab(EmrCloudApi.Requests.Lock.RemoveAllLockMedicalTabRequest)" on controller "EmrCloudApi.Controller.LockController" ("EmrCloudApi").
2025-07-29T18:49:01.2768600+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/JsonSetting/Get?key=RECEPTION_FILTER_STATE application/json 379
2025-07-29T18:49:01.2783960+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/LabelMst/GetList application/json 346
2025-07-29T18:49:01.2886140+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request starting HTTP/1.1 GET http://host.docker.internal:5286/api/User/GetList?SinDate=********&IsDoctorOnly=true - 1387
2025-07-29T18:49:01.3110550+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)"'
2025-07-29T18:49:01.3114740+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"Get\", controller = \"JsonSetting\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.JsonSetting.GetJsonSettingResponse]] Get(EmrCloudApi.Requests.JsonSetting.GetJsonSettingRequest)" on controller "EmrCloudApi.Controller.JsonSettingController" ("EmrCloudApi").
2025-07-29T18:49:01.3141570+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)"'
2025-07-29T18:49:01.3142090+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executing endpoint '"EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)"'
2025-07-29T18:49:01.3144340+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetList\", controller = \"LabelMst\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.LabelMst.GetListLabelMstResponse]] GetList()" on controller "EmrCloudApi.Controller.LabelMstController" ("EmrCloudApi").
2025-07-29T18:49:01.3144400+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Route matched with "{action = \"GetList\", controller = \"User\"}". Executing controller action with signature "Microsoft.AspNetCore.Mvc.ActionResult`1[EmrCloudApi.Responses.Response`1[EmrCloudApi.Responses.User.GetUserListResponse]] GetList(EmrCloudApi.Requests.User.GetUserListRequest)" on controller "EmrCloudApi.Controller.UserController" ("EmrCloudApi").
2025-07-29T18:49:01.3162220+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.JsonSettingController.Get(GetJsonSettingRequest req) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/JsonSettingController.cs:line 31
   at lambda_method14(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:49:01.3176750+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.LabelMstController.GetList() in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/LabelMstController.cs:line 27
   at lambda_method13(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:49:01.3203880+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.UserController.GetList (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.UserController.GetList(GetUserListRequest req) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/UserController.cs:line 59
   at lambda_method15(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:49:01.3245310+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:49:01.3246550+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:49:01.3249910+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)" in 13.4508ms
2025-07-29T18:49:01.3250060+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)" in 10.3916ms
2025-07-29T18:49:01.3250890+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.JsonSettingController.Get (EmrCloudApi)"'
2025-07-29T18:49:01.3251460+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.LabelMstController.GetList (EmrCloudApi)"'
2025-07-29T18:49:01.3251890+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/JsonSetting/Get" responded 400 in 14.1423 ms
2025-07-29T18:49:01.3252090+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/LabelMst/GetList" responded 400 in 11.0646 ms
2025-07-29T18:49:01.3281170+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/LabelMst/GetList application/json 346 - 400 - application/json;+charset=utf-8 49.6684ms
2025-07-29T18:49:01.3282480+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/JsonSetting/Get?key=RECEPTION_FILTER_STATE application/json 379 - 400 - application/json;+charset=utf-8 51.4157ms
2025-07-29T18:49:01.3291060+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:49:01.3298250+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)" in 15.025ms
2025-07-29T18:49:01.3301070+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.UserController.GetList (EmrCloudApi)"'
2025-07-29T18:49:01.3302220+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "GET" "/api/User/GetList" responded 400 in 16.0108 ms
2025-07-29T18:49:01.3322140+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 GET http://host.docker.internal:5286/api/User/GetList?SinDate=********&IsDoctorOnly=true - 1387 - 400 - application/json;+charset=utf-8 43.5722ms
2025-07-29T18:49:01.5481380+09:00 [ERR] (Infrastructure.Common.GlobalExceptionFilters) GlobalExceptionFilter: Error in EmrCloudApi.Controller.LockController.RemoveAllLockMedicalTab (EmrCloudApi). The ConnectionString property has not been initialized.. Stack Trace:    at UseCase.Core.Sync.Invoker.UseCaseInvoker.Invoke[TOutputData](IInputData`1 inputData)
   at UseCase.Core.Sync.UseCaseBus.Handle[TOutputData](IInputData`1 inputData)
   at EmrCloudApi.Controller.LockController.RemoveAllLockMedicalTab(RemoveAllLockMedicalTabRequest request) in /Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/Controller/LockController.cs:line 359
   at lambda_method1442(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted). 
System.Collections.Generic.List`1[Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor]. 
Microsoft.AspNetCore.Http.DefaultHttpContext. UseCase.Core. 
. 
 
TOutputData Invoke[TOutputData](UseCase.Core.Sync.Core.IInputData`1[TOutputData])
2025-07-29T18:49:01.5517740+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor) Executing "ObjectResult", writing value of type '"Helper.Responses.DenkaruErrorResponse"'.
2025-07-29T18:49:01.5519660+09:00 [INF] (Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker) Executed action "EmrCloudApi.Controller.LockController.RemoveAllLockMedicalTab (EmrCloudApi)" in 5497.1415ms
2025-07-29T18:49:01.5520230+09:00 [INF] (Microsoft.AspNetCore.Routing.EndpointMiddleware) Executed endpoint '"EmrCloudApi.Controller.LockController.RemoveAllLockMedicalTab (EmrCloudApi)"'
2025-07-29T18:49:01.5521760+09:00 [INF] (Serilog.AspNetCore.RequestLoggingMiddleware) HTTP "POST" "/api/Lock/RemoveAllLockMedicalTab" responded 400 in 5506.1941 ms
2025-07-29T18:49:01.5534080+09:00 [INF] (Microsoft.AspNetCore.Hosting.Diagnostics) Request finished HTTP/1.1 POST http://host.docker.internal:5286/api/Lock/RemoveAllLockMedicalTab application/json 51 - 400 - application/json;+charset=utf-8 5532.2499ms
